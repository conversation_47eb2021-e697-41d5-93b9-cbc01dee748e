

import asyncio
from datetime import datetime, timedelta
import logging
import time



from aiogram import Bo<PERSON>, Router, types
# from aiogram import LoggingMiddleware
from aiogram import F,Router
from aiogram.filters import <PERSON><PERSON><PERSON>er,ChatMemberUpdatedFilter, IS_MEMBER, IS_NOT_MEMBER
from aiogram.types import Chat<PERSON>emberUpdated, ChatPermissions, CallbackQuery


from aibot_handlers.tg_util import check_admin,extract_bot_msg_details
from common import loggers
from common.models.ai_bot_admin.group_msg_bean import TgMsgContentBO
from common.models.ai_bot_admin.user_card_admin_bean import OpAction, OpActionType
from services.bot_group.bot_group_service import BotHandlerManager, SpamProtectionHandler,UserSpamBehaviorService
from services.bot_group.group_msg_service import GroupUserCardInfoService
from services.bot_group.group_user_op_service import group_user_op_service





logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)


bot_handler_manager = BotHandlerManager()

FULL_NAME_LEN = 30

#- 用户TG用户名: #Dinnerfast 用户TGID: #7420374468
#


async def check_group_admin(bot:Bot,group_id:int,tg_id:int) -> bool:    
    try:
        user_member = await bot.get_chat_member(group_id, tg_id,3)
        if user_member.status in ['administrator', 'creator']:
            log.info(f"spam_protection_rule_handler:admin user {tg_id} in {group_id},{user_member.status},skip spam check")
            return True
    except Exception as e:
        log.error(f"get_chat_member {tg_id},error:{e}")
    return False

class AdminFilter(BaseFilter):
    async def __call__(self, callback: CallbackQuery) -> bool:
        bot: Bot = callback.bot # type: ignore
        chat_id = callback.message.chat.id
        user_id = callback.from_user.id

        try:
            member = await bot.get_chat_member(chat_id, user_id)
            return member.status in ['administrator', 'creator']
        except Exception as e:
            log.error(f"get_chat_member {user_id},error:{e}")
            return False

def create_group_help_dp_router() -> Router:

    group_help_dp_router = Router()

    async def delete_message_after_delay(bot:Bot,chat_id: int, message_id: int,delay:int):
        await asyncio.sleep(delay=delay)  
        try:
            await bot.delete_message(chat_id=chat_id, message_id=message_id)
            logging.info(f'Message {message_id} deleted in chat {chat_id}')
        except Exception as e:
            logging.error(f'Error deleting message: {e}')

    async def check_nick_name_length(message: types.Message):

        nick_name = message.from_user.full_name
        tg_id  = message.from_user.id
        bot = message.bot
        group_id = message.chat.id
        log.info(f"check_nick_name_length {tg_id},{nick_name}")
        try:
            if len(nick_name) > FULL_NAME_LEN:
                sent_msg = await message.reply(text=f"您的TG昵称过长请修改，用户昵称累计不超过{FULL_NAME_LEN}个字")
                asyncio.create_task(delete_message_after_delay(bot,sent_msg.chat.id, sent_msg.message_id, 60)) # type: ignore
                spam_cnt = await UserSpamBehaviorService.increase_user_spam_cnt(tg_id,group_id,2)
                if spam_cnt >= 2:
                    log.info(f"check_nick_name_length,del msg: {tg_id},message:{message}")
                    asyncio.create_task(delete_message_after_delay(bot,message.chat.id, message.message_id, 120)) # type: ignore
                    return True
                # 第一次通过私聊bot 给用户发送消息进行提醒 &&jinyan sanfen zhong
                await bot_handler_manager.sender_bot.send_message(tg_id,f"您的TG昵称过长请修改，用户昵称累计不超过{FULL_NAME_LEN}个字")
                try:
                    # 执行禁言操作 3分钟
                    forbid_time = 3*60 + int(time.time())
                    log.info(f"forbid user {tg_id},forbid_time:{forbid_time},spam_cnt:{spam_cnt},nickname too long")
                    permissions = ChatPermissions(can_send_messages=False,can_send_audios=False,can_send_documents=False,can_send_photos=False,can_send_videos=False,can_send_video_notes=False,can_send_voice_notes=False,can_send_polls=False,can_send_other_messages=False,can_add_web_page_previews=False,can_change_info=False,can_invite_users=False,can_pin_messages=False,can_manage_topics=False)
                    await bot.restrict_chat_member(chat_id=group_id, user_id=tg_id, permissions=permissions,until_date=forbid_time)
                except Exception as e:
                        log.error(f"forbid user {tg_id},error:{e}")
                return True
            return False
        except Exception as e:
            log.error(f"check_nick_name_length {tg_id},{nick_name},error:{e}")
            return False
    # 处理用户加入群组的事件
    @group_help_dp_router.chat_member(ChatMemberUpdatedFilter(IS_NOT_MEMBER >> IS_MEMBER))
    async def user_joined(event: ChatMemberUpdated):
        user = event.new_chat_member.user
        chat = event.chat
        bot = event.bot

        log.info(f"join_group {user.full_name} |{chat.id}|{user.id}|加入群组！")
        try:

            welcome_config_handler = bot_handler_manager.welcome_config.get(chat.id)

            if welcome_config_handler and welcome_config_handler.config:
            
                config = welcome_config_handler.config

                # {member}为新入群带链接的成员名字同 full_name，{userId}为新进群用户id，{userName}为新进群用户名，{dateDay}为群成员入群日期，{dateTime}为群成员入时间，{groupTitle}为群名称
                msg_text = config.msg_content.get("msg_text") if isinstance(config.msg_content, dict) else "欢迎加入"
                msg_text = msg_text.replace("{member}",user.full_name)
                msg_text = msg_text.replace("{userId}",str(user.id))
                msg_text = msg_text.replace("{userName}",user.username) # type: ignore
                sent_msg = await bot.send_message(chat_id=chat.id, message_thread_id=config.message_thread_id, text=msg_text,parse_mode="html")
                if config.delete_memo > 0:
                    asyncio.create_task(delete_message_after_delay(bot,chat.id, sent_msg.message_id, config.delete_memo)) # type: ignore
        except Exception as e:
            log.error(f"send welcome message :{user.id},{user.full_name},error:{e}")
    # 处理用户离开群组的事件
    @group_help_dp_router.chat_member(ChatMemberUpdatedFilter(IS_MEMBER >> IS_NOT_MEMBER))
    async def user_leaved(event: ChatMemberUpdated):
        user = event.new_chat_member.user
        chat = event.chat
        log.info(f"用户 {user.full_name} |{user.id}|离开群组！")
        # await bot.send_message(chat.id, f"欢迎 {user.full_name} 加入群组！"
    
    #处理用户编辑消息的事件
    @group_help_dp_router.edited_message((F.chat.type == 'group') | (F.chat.type == 'supergroup'))
    async def handle_edited_message(message: types.Message):
        log.warning(f"handle_edited_message_delete:{message.message_id},{message.chat.id}")
        # 处理用户编辑消息的事件
        group_id = message.chat.id
        msg_id = message.message_id
        u_tg_id = message.from_user.id
        
        if check_admin(u_tg_id):
            log.info(f"handle_edited_message_delete:admin user {u_tg_id},skip delete message")
            return
        # 检查用户是否是管理员
        if message.bot:
            is_admin = await check_group_admin(message.bot,group_id,u_tg_id)
            if is_admin:
                log.info(f"handle_edited_message_delete:admin user {u_tg_id},skip delete message")
                return
        
        try:
            # await message.reply("不允许编辑消息")
            del_msg = await message.delete()
            log.warning(f"handle_edited_message_delete:{del_msg}")
            msg_content = await extract_bot_msg_details(message,message.bot) # type: ignore
            await GroupUserCardInfoService.process_group_edited_msg(group_id=group_id,msg_id=msg_id,tg_id=u_tg_id,msg_content=msg_content)
            
        except Exception as e:
            log.error(f"delete message {message.message_id},error:{e}",exc_info=True)
        
        return
    
    @group_help_dp_router.message(F.text & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
    async def handle_text_message(message: types.Message):
        log.info(f"megroup_help_dp_router.message:{message.text}")
        group_id = message.chat.id
        user_id = message.from_user.id
        bot = message.bot

        # 处理自动回复规则
        auto_reply_rule_handler = bot_handler_manager.auto_reply_rule_handlers.get(group_id)

        log.info(f"auto_reply_rule_handler:{auto_reply_rule_handler}")
        if auto_reply_rule_handler:
            #step 1 check user or tgid
            check_rule_user = auto_reply_rule_handler.check_message_from_user(user_id,message.from_user.username,message.text) # type: ignore
            if check_rule_user:
                try:
                    last_time = await UserSpamBehaviorService.get_user_spam_last_time(user_id,group_id,101)

                    if datetime.utcnow() - last_time.replace(tzinfo=None) > timedelta(hours=3):
                        log.info(f"check_rule_user:{check_rule_user}")
                        sent_msg = await message.answer(check_rule_user.message_text) # type: ignore
                    else:
                        log.warning(f"check_rule_user:{check_rule_user} :{datetime.utcnow()}{last_time}3小时内已经发送过")
                    await UserSpamBehaviorService.increase_user_spam_cnt(user_id,group_id,101)
                except Exception as e:
                    log.error(f"send message {check_rule_user},error",exc_info=True)

            check_rule = auto_reply_rule_handler.check_message(user_id,message.text) # type: ignore
            if check_rule:
                if check_rule.is_quote:
                    sent_msg = await message.reply(check_rule.message_text,reply_to_message_id=message.message_id) # type: ignore
                else:
                    try:
                        if check_rule.msg_type == 1:
                            #发送文字
                            sent_msg = await message.answer(check_rule.message_text,parse_mode="html") # type: ignore
                        elif check_rule.msg_type == 2:
                            #发送图片
                            image_url = check_rule.image_url
                            sent_msg = await message.answer_photo(photo=image_url,caption=check_rule.message_text,parse_mode="html")  # type: ignore
                    except Exception as e:
                        log.error(f"send message {check_rule.message_text},error:{e}")
                        return 
                if check_rule.del_bot_msg_delay > 0:
                    log.info(f"del_bot_msg_delay:{sent_msg.model_dump(exclude_none=True)},{check_rule.del_bot_msg_delay}")
                    try:
                        # 创建异步任务来延迟删除消息
                        asyncio.create_task(delete_message_after_delay(bot,message.chat.id, sent_msg.message_id, check_rule.del_bot_msg_delay)) # type: ignore
                    except Exception as e:
                        log.error(f"delete bot message {sent_msg},error:{e}")

                if check_rule.del_src_msg_delay > 0:
                    log.info(f"del_src_msg_delay:{message},{check_rule.del_src_msg_delay}")
                    try:
                        asyncio.create_task(delete_message_after_delay(bot,message.chat.id, message.message_id, check_rule.del_src_msg_delay)) # type: ignore
                    except Exception as e:
                        log.error(f"delete src message {message},error:{e}")
                return

        # 处理垃圾信息保护规则
        # step.1 skip admin
        try:
            user_member = await bot.get_chat_member(message.chat.id, message.from_user.id,5)
            if user_member.status in ['administrator', 'creator']:
                log.info(f"spam_protection_rule_handler:admin user {user_id},skip spam check")
                return
        except Exception as e:
            log.error(f"get_chat_member {message.from_user.id},error:{e}")
            return

        # await check_nick_name_length(message)

        spam_protection_rule_handler = bot_handler_manager.spam_protection_rule_handlers.get(group_id)
        log.info(f"spam_protection_rule_handler:{spam_protection_rule_handler}")
        if spam_protection_rule_handler:
            spam_rule = await spam_protection_rule_handler.check_message(message, user_id)
            log.info(f"spam_protection_rule_handler,message,{message},spam_rule:{spam_rule}")
            if spam_rule:
            
                spam_cnt = await UserSpamBehaviorService.increase_user_spam_cnt(user_id,group_id,spam_rule.rubbish_type)
                rubbish_action = spam_rule.rubbish_action

                if rubbish_action.delete and rubbish_action.delete.isDel == 1:
                    try:
                        log.info(f"match rubbish_action:{rubbish_action},delete message {message}")
                        await message.delete()
                    except Exception as e:
                        log.error(f"delete message {message},error:{e}")
                if rubbish_action.alert and rubbish_action.alert.isAlert == 1:
                    message_text = f"用户 {message.from_user.full_name} 发送了敏感信息，请不要再发送。"
                    warn_msg = await bot.send_message(chat_id=group_id, text=message_text,message_thread_id=message.message_thread_id)
                    # 创建异步任务来延迟删除消息
                    asyncio.create_task(delete_message_after_delay(bot,group_id, warn_msg.message_id, 30)) # type: ignore
                if rubbish_action.forbid and rubbish_action.forbid.isForbid == 1:
                    forbid_time = rubbish_action.forbid.forbidTime + int(time.time())
                    forbid_cnt = rubbish_action.forbid.forbidCnt
                    is_ban = False
                    if spam_cnt == 1:
                        forbid_time = forbid_time
                    elif spam_cnt >= 2:
                        forbid_time = rubbish_action.forbid.forbidTime * spam_cnt + int(time.time()) # 用户第二次触发，删除消息+禁
                    # 超过禁言次数，直接踢出
                    if spam_cnt >= forbid_cnt:
                        is_ban = True

                    try:
                        # 执行禁言操作
                        log.info(f"forbid user {user_id},forbid_time:{forbid_time},spam_cnt:{spam_cnt},{spam_rule}")
                        permissions = ChatPermissions(can_send_messages=False,can_send_audios=False,can_send_documents=False,can_send_photos=False,can_send_videos=False,can_send_video_notes=False,can_send_voice_notes=False,can_send_polls=False,can_send_other_messages=False,can_add_web_page_previews=False,can_change_info=False,can_invite_users=False,can_pin_messages=False,can_manage_topics=False)
                        await bot.restrict_chat_member(chat_id=group_id, user_id=user_id, permissions=permissions,until_date=forbid_time)
                    except Exception as e:
                        log.error(f"forbid user {user_id},error:{e}")

                    if is_ban and rubbish_action.kick and rubbish_action.kick.isKick == 1:
                        try:
                            # 执行踢人操作
                            if rubbish_action.kick.kickTime==0:
                                ban_time = 0
                            else:
                                ban_time = rubbish_action.kick.kickTime + int(time.time())
                            log.info(f"kick user {user_id},ban_time:{ban_time},spam_cnt:{spam_cnt},{spam_rule}")
                            await bot.ban_chat_member(chat_id=group_id, user_id=user_id,until_date=ban_time,revoke_messages=True)
                        except Exception as e:
                            log.error(f"kick user {user_id},error:{e}")
    
        return

    # 处理图片消息
    @group_help_dp_router.edited_message((F.photo) & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
    @group_help_dp_router.message((F.photo) & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
    async def handle_photo_message(message: types.Message):
        bot = message.bot
        token = bot.token
        try:
            
            user_tg_id = message.from_user.id
            user_nickname = message.from_user.full_name
            username = "" if message.from_user.username is None else message.from_user.username
            group_id = message.chat.id
            group_username = message.chat.username
            msg_id = message.message_id
            # 获取最高分辨率的照片
            photo = message.photo[-1] # type: ignore
            # 获取文件 ID
            file_id = photo.file_id
            file_unique_id = photo.file_unique_id
            file_info = await bot.get_file(file_id)
            print(f"file_info:{file_info}")
            # 下载图片
            file_path = file_info.file_path
            file_url = f"https://api.telegram.org/file/bot{token}/{file_path}"

            #处理forwarded消息
            forward_from_id = -1
            if message.forward_origin:
                log.info(f"message.forward_origin:{message.forward_origin},{message.sender_chat},{message.forward_from_chat}")
                if isinstance(message.forward_origin, types.MessageOriginUser):
                    # 转发来源是某个用户
                    forward_from_id = message.forward_origin.sender_user.id
                    log.info(f"消息是从用户 {forward_from_id} 转发的")
                
                elif isinstance(message.forward_origin, types.MessageOriginHiddenUser):
                    # 转发来源是隐藏的用户（匿名管理员）
                    log.info(f"消息是从匿名用户转发的")
                elif isinstance(message.forward_origin, types.MessageOriginChat):
                    # 转发来源是某个群组或超级群组
                    forward_from_id = message.forward_origin.sender_chat.id
                    log.info(f"消息是从群组 {forward_from_id} 转发的")
                elif isinstance(message.forward_origin, types.MessageOriginChannel):
                    # 转发来源是某个频道
                    forward_from_id = message.forward_origin.chat.id
                    log.info(f"消息是从频道 {forward_from_id} 转发的")
            
            
            msg_dict = {
                "username":username,
                "tg_id":user_tg_id,
                "user_nickname":user_nickname,
                "group_id":group_id,
                "group_username":group_username,
                "file_id":file_id,
                "file_path":file_path,
                "image_url":file_url,
                "file_unique_id":file_unique_id,
                "msg_id":msg_id,
                "forward_from_id":forward_from_id,
                "file_type":"photo"
            }
            log.info(f"photo recevied:{msg_dict}")
            asyncio.create_task(SpamProtectionHandler.image_ff_check(msg_dict))
        except Exception as e:
            log.error(f"handle_photo_message error:{e}",exc_info=True)

    # 处理视频消息
    @group_help_dp_router.message(F.animation & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
    async def handle_gif_message(message: types.Message):
        bot = message.bot
        token = bot.token
        try:
            user_tg_id = message.from_user.id
            user_nickname = message.from_user.full_name
            username = "" if message.from_user.username is None else message.from_user.username
            group_id = message.chat.id
            group_username = message.chat.username
            msg_id = message.message_id
            animation = message.animation
            file_id = animation.file_id
            file_unique_id = animation.file_unique_id
            file_info = await bot.get_file(file_id)
            print(f"file_info:{file_info}")
            file_path = file_info.file_path
            file_url = f"https://api.telegram.org/file/bot{token}/{file_path}"
         
            log.info(f"file_uqiue:{animation.file_unique_id}")
            msg_dict = {
                "username": username,
                "tg_id": user_tg_id,
                "user_nickname": user_nickname,
                "group_id": group_id,
                "group_username": group_username,
                "file_id": file_id,
                "file_unique_id": file_unique_id,
                "file_path": file_path,
                "file_url": file_url,
                "image_url": file_url,
                "msg_id": msg_id,
                "file_type":"animation"
            }
            log.info(f"animation received:{msg_dict}")
            asyncio.create_task(SpamProtectionHandler.stick_gif_check(msg_dict))
            # 处理 GIF 动图消息的逻辑
        except Exception as e:
            log.error(f"handle_gif_message error:{e}", exc_info=True)

    # 处理sticker消息
    @group_help_dp_router.message(F.sticker & ((F.chat.type == 'group') | (F.chat.type == 'supergroup')))
    async def handle_sticker_message(message: types.Message):
        bot = message.bot
        token = bot.token
        try:
            user_tg_id = message.from_user.id
            user_nickname = message.from_user.full_name
            username = "" if message.from_user.username is None else message.from_user.username
            group_id = message.chat.id
            group_username = message.chat.username
            msg_id = message.message_id
            sticker = message.sticker
            file_id = sticker.file_id
            file_unique_id = sticker.file_unique_id
            is_video_task = sticker.is_animated or sticker.is_video
            file_info = await bot.get_file(file_id)
            print(f"file_info:{file_info}")
            file_path = file_info.file_path
            file_url = f"https://api.telegram.org/file/bot{token}/{file_path}"
    
            msg_dict = {
                "username": username,
                "tg_id": user_tg_id,
                "user_nickname": user_nickname,
                "group_id": group_id,
                "group_username": group_username,
                "file_id": file_id,
                "file_path": file_path,
                "file_unique_id": file_unique_id,
                "file_url": file_url,
                "image_url": file_url,
                "msg_id": msg_id,
                "file_type":"sticker"
            }
            log.info(f"sticker received:{msg_dict},is_video:{is_video_task}")
            
            if is_video_task:
                asyncio.create_task(SpamProtectionHandler.stick_gif_check(msg_dict))
            else:
                msg_dict["image_url"] = file_url
                asyncio.create_task(SpamProtectionHandler.image_ff_check(msg_dict))
            # 处理 sticker 消息的逻辑
        except Exception as e:
            log.error(f"handle_sticker_message error:{e}", exc_info=True)

    # 处理命令回调，撤销
    @group_help_dp_router.callback_query(AdminFilter(),lambda c: c.data.endswith('clear_alert'))
    async def handle_clear_alert(callback_query: types.CallbackQuery):
        log.info(f"handle_clear_alert:{callback_query.data}")
        tg_id = callback_query.data.split(':')[0]
        group_id = callback_query.message.chat.id

        op_action_type = OpActionType.ClEAR_ALERT
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=op_action_type,group_id=group_id)

        await group_user_op_service.exec_op_action(op_action)
        callback_query.answer("撤销警告成功")
        
        return
    # 处理命令回调，解封
    @group_help_dp_router.callback_query(AdminFilter(),lambda c: c.data.endswith('unban'))
    async def handle_unban(callback_query: types.CallbackQuery):
        log.info(f"handle_unban:{callback_query.data}")
        tg_id = callback_query.data.split(':')[0]
        group_id = callback_query.message.chat.id
        
        op_action_type = OpActionType.UNBAN
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=op_action_type,group_id=group_id)
        await group_user_op_service.exec_op_action(op_action)
        
        callback_query.answer("解封成功")
        
        return

   
    
    return group_help_dp_router