import asyncio
from pathlib import Path
from datetime import datetime
import time
import uuid
import json
from image_bot_dp_router import upload_img_to_cos
from boilerplate import API
from novelai_api.ImagePreset import ImageModel, ImagePreset, UCPreset, ImageResolution

nai_image_resolution_dict_v4 = {
    "small_portrait": ImageResolution.Small_Portrait_v4,
    "small_landscape": ImageResolution.Small_Landscape_v4,
    "small_square": ImageResolution.Small_Square_v4,
    "normal_portrait": ImageResolution.Normal_Portrait_v4,
    "normal_landscape": ImageResolution.Normal_Landscape_v4,
    "normal_square": ImageResolution.Normal_Square_v4,
    "large_portrait": ImageResolution.Large_Portrait_v4,
    "large_landscape": ImageResolution.Large_Landscape_v4,
    "large_square": ImageResolution.Large_Square_v4,
}

nai_image_resolution_dict_v3 = {
    "small_portrait": ImageResolution.Small_Portrait_v3,
    "small_landscape": ImageResolution.Small_Landscape_v3,
    "small_square": ImageResolution.Small_Square_v3,
    "normal_portrait": ImageResolution.Normal_Portrait_v3,
    "normal_landscape": ImageResolution.Normal_Landscape_v3,
    "normal_square": ImageResolution.Normal_Square_v3,
    "large_portrait": ImageResolution.Large_Portrait_v3,
    "large_landscape": ImageResolution.Large_Landscape_v3,
    "large_square": ImageResolution.Large_Square_v3,
}

# 配置模型和分辨率 参数映射
model_config = {
    "image_style_1": (ImageModel.Anime_v4_Full, nai_image_resolution_dict_v4),
    "image_style_2": (ImageModel.Furry_v3, nai_image_resolution_dict_v3),
}

"""
@param {int} tg_id
@param {str} prompt
@param {str} style image_style_1:二次元风 image_style_2:福瑞风
@param {str} resolution 
small_portrait=  (512, 768)
small_landscape=  (768, 512)
small_square=（640, 640）
normal_portrait=(832, 1216)
normal_landscape=(1216, 832)
normal_square=(1024, 1024)
large_portrait=(1024, 1536)
large_landscape = (1536, 1024)
large_square= (1472, 1472)
"""


async def main(tg_id: int, prompt: str, style: str, resolution: str) -> str:
    # d = Path("gen_images/")
    # d.mkdir(exist_ok=True)

    time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # file_path = (d / f"{tg_id}_{time}.png")

    async with API() as api_handler:
        api = api_handler.api
        model, res_dict = model_config.get(
            style, (ImageModel.Anime_v4_Full, nai_image_resolution_dict_v4)
        )
        preset = ImagePreset.from_default_config(model)
        preset.resolution = res_dict.get(resolution, ImageResolution.Small_Portrait_v4)
        preset.seed = 42
        preset.uc_preset = UCPreset.Preset_Heavy
        preset.quality_toggle = False

        # multiple images
        # preset.n_samples = 4
        
        async for _, img in api.high_level.generate_image(prompt, model, preset):
            # 上传cos
            image_url = upload_img_to_cos(img, tg_id)

    # 返回json格式字符串
    dict = {
        "image_url": image_url,
        "prompt": prompt,
        "request_id": tg_id,
        "code": 200,
        "msg": "success",
    }
    print(json.dumps(dict))
    return json.dumps(dict)


if __name__ == "__main__":
    asyncio.run(
        main(
            "1222",
            "Preteen very skinny, pale skin, black hair, naked skinny ass covered in cum dripping down, expression of disgust, cum on face, wearing an unbuttoned pajama jumpsuit revealing tiny naked butt in pomp, in a toilet",
            style="image_style_1",
            resolution="small_portrait",
        )
    )
