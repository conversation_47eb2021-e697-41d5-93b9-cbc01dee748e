import logging
import os, json
import hmac
import hashlib
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Request
from common.entity import EvmTransaction
from services import re_purchase_service, recharge_service, user_growth_service
from services.user_service import user_service

alchemy_router = APIRouter()

ALCHEMY_SIGNING_KEY = os.getenv('ALCHEMY_SIGNING_KEY')
RECIPIENT_ADDRESS = os.getenv('ALCHEMY_RECIPIENT_ADDRESS')

@alchemy_router.post('/alchemy/webhook')
async def alchemy_webhook(request: Request):
    request_body = await request.body()
    signature = request.headers.get('X-Alchemy-Signature')

    signing_key = ALCHEMY_SIGNING_KEY.encode()
    h = hmac.new(signing_key, request_body, hashlib.sha256)
    digest = h.hexdigest()
    if signature != digest:
        logging.error(f'Invalid signature, {signature}')
        return JSONResponse(content={'status': 'error', 'message': 'Invalid signature'})

    body = json.loads(request_body.decode('utf-8'))
    network = body['event']['network']
    activities = body['event']['activity']
    target_contract = os.environ.get('ALCHEMY_USDT_CONTRACT_ADDRESS').casefold()
    if target_contract:
        activities = [a for a in activities if is_target_contract(a, target_contract)]

    transactions = [EvmTransaction(**a) for a in activities]
    logging.info(transactions)
    for tran in transactions:
        if tran.toAddress != RECIPIENT_ADDRESS:
            logging.error(f'Invalid address, {tran.toAddress}')
        else:
            recharge_order = await recharge_service.alchemy_usdt_recharge_success(tran, network)
            if recharge_order:
                user = await user_service.get_user_by_id(recharge_order.user_id)
                await user_growth_service.add_fc_reward(user)
                await re_purchase_service.after_recharge(user, recharge_order)


def is_target_contract(activity, target_contract) -> bool:
    raw_contract = activity.get('rawContract')
    if not raw_contract:
        return False
    address = raw_contract.get('address')
    if not address:
        return False
    return address.casefold() == target_contract.casefold()
