import logging
from fastapi import Depends, <PERSON><PERSON><PERSON><PERSON>, Head<PERSON>
from pydantic import BaseModel
from common.common_constant import ApiSource, Language
from controllers.request_depends import dep_api_source
from controllers.user_check import get_current_user
from services.bot_services import create_web_share_link
from persistence.models.models import (
    WelfareTask,
)
from services import (
    check_in_service,
    welfare_service,
)
from services.user_service import user_service
from utils import response_util
from utils.translate_util import _tl

task_router = APIRouter()

log = logging.getLogger(__name__)


@task_router.get("/tasks")
async def task_list_new(
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
    tg_bot_id: str = Header(default=""),
    api_source: ApiSource = Depends(dep_api_source),
):
    log.info(f"list task for user_id:{user_id},tg_bot_id:{tg_bot_id}")
    user = await user_service.get_user_by_id(user_id)
    task_list = await welfare_service.user_display_tasks(
        user, tg_bot_id, api_source, current_language
    )
    return task_list


@task_router.get("/tasks/my_invite_link")
async def my_invite_link(
    user_id: int = Depends(get_current_user),
):
    link = await create_web_share_link(user_id)
    return response_util.ok({"link": link})


@task_router.post("/tasks/check_in")
async def check_in(
    user_id: int = Depends(get_current_user),
    api_source: ApiSource = Depends(dep_api_source),
    current_language: str = Header(default=Language.ZH.value),
):
    if api_source != ApiSource.OVERSEAS_WEB.value:
        return response_util.def_error("not support")
    user = await user_service.get_user_by_id(user_id)
    check_in_ret, amount = await check_in_service.process_web_check_in(user)
    check_failed_message = "今日已签到，请明天再来哦！"
    if not check_in_ret:
        return response_util.def_error(check_failed_message)
    title = _tl(
        "🎉🎉🎉 太棒了！@{username} 今天获得了{amount}🟡！🎉🎉🎉",
        current_language,
        WelfareTask.__name__,
    )
    title = title.format(
        username=user.nickname,
        amount=amount,
    )
    return response_util.ok({"amount": amount, "title": title}, message=title)


class TaskReceiveRequest(BaseModel):
    task_id: str = ""


@task_router.post("/tasks/receive")
async def receive_task(
    receive_request: TaskReceiveRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    if not receive_request.task_id:
        return response_util.def_error("task id is empty")
    receive_ret, rewards = await welfare_service.complete_task(
        user_id, receive_request.task_id
    )
    if not receive_ret:
        return response_util.def_error("receive task failed")
    tips = _tl("领取奖励成功！", current_language, WelfareTask.__name__)
    if rewards:
        titles = [
            _tl(reward.title, current_language, WelfareTask.__name__)
            for reward in rewards
        ]
        format_tips = _tl(
            "领取奖励成功！你获得了：{rewards}",
            current_language,
            WelfareTask.__name__,
        )
        tips = format_tips.format(rewards=",".join(titles))
    return response_util.ok({"received": True, "tips": tips})
