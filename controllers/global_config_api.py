from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter

from common import common_constant

global_config_router = APIRouter()


@global_config_router.get("/global/config/all")
async def get_all_global_config():
    ret = {"language_list": common_constant.Language.fetch_config()}
    replay_max_config = {
        common_constant.RoleLevelType.PREMIUM.value: 2000,
        common_constant.RoleLevelType.NORMAL.value: 700,
    }
    ret["replay_token_max_config"] = replay_max_config
    return JSONResponse(content=ret)
