from datetime import datetime
from itertools import groupby
import json
import json.tool
import logging
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
import requests

from common.common_constant import ChatModeType, RoleTag
from common.entity import RegexRuleResponse
from persistence.models.models import RegexOption, RoleOrder
from services import regex_service, tag_service
from utils import response_util, str_util, tg_util

extension_router = APIRouter()


log = logging.getLogger(__name__)


@extension_router.get("/extensions")
async def get_all_regex_rules():
    all_regex = await regex_service.get_effective_regex_rules()
    rules = [
        RegexRuleResponse.from_model(rule)
        for rule in all_regex
        if RegexOption.FORMAT_DISPLAY in rule.options
    ]
    return JSONResponse(content=jsonable_encoder({"regex_rules": rules}))


@extension_router.post("/sentry/post_error")
async def sentry_error(request: Request):
    try:
        body = await request.json()
        project = body["project"]
        message = body["message"]
        if str_util.is_empty(message) and "event" in body:
            event = body["event"]
            if "title" in event and not str_util.is_empty(event["title"]):
                message = event["title"]
        data = {"project": project, "message": message, "date": datetime.now()}
        await tg_util.send_monitor(data)
        # 发送一个http请求到sentry

        log.info(f"project: {project}, message: {message}")
    except Exception as e:
        log.error(f"post_error error: {e}")
    return "success"


# @extension_router.get("/init/tag_order")
# async def init_tag_order():

#     await RoleOrder.filter(mode_type=ChatModeType.SINGLE.value).delete()

#     role_order_ids = await tag_service.list_role_orders_by_tags(RoleTag.CHOSEN.value)
#     i = 0
#     for role_order_id in role_order_ids:
#         await RoleOrder.create(
#             position=i,
#             mode_type=ChatModeType.SINGLE.value,
#             mode_target_id=role_order_id,
#         )
#         i += 1

#     order_list = (
#         await RoleOrder.filter(mode_type=ChatModeType.SINGLE.value)
#         .all()
#         .order_by("position")
#     )
#     ret_list = [
#         {
#             "position": order.position,
#             "mode_target_id": order.mode_target_id,
#             "mode_type": order.mode_type,
#         }
#         for order in order_list
#     ]
#     return response_util.success(ret_list)
