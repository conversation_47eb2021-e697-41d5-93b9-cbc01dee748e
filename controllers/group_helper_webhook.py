import asyncio
from datetime import timed<PERSON>ta
import json
import logging
import os
import random
import redis
from aiogram import Bo<PERSON>, Di<PERSON>atcher, Router, types
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.filters import CommandStart, CommandObject, Command, ChatMemberUpdatedFilter, IS_MEMBER, IS_NOT_MEMBER
from fastapi import Header
from fastapi.routing import APIRouter
from common.activity_diamond_model import NoticeMethod
from persistence.redis_client import redis_client as normal_redis_client
from services import check_in_service, config_service, gift_award_service, tg_config_service, tg_message_service, user_diamond_season_service, welfare_service
from services.bot_services import helper_bots, bot as tma_bot, chat_bot
from services.user_service import user_service, update_user_premium_status
from utils import env_const, user_growth_constants


redis_client = redis.Redis(host='127.0.0.1', decode_responses=True)
ps = redis_client.pubsub()

chatbot_group_join_tip = f'''🎉🎉恭喜你完成加入幻梦官方群，获得500🟡，可在充值页面底部查询
幻梦官方群：{user_growth_constants.TARGET_JOIN_GROUP_LINK}'''
chatbot_channel_join_tip = f'''🎉🎉恭喜你完成加入幻梦官方频道，获得500🟡，可在充值页面底部查询
幻梦官方频道：{user_growth_constants.ROLE_CHANNEL_LINK}'''

webhook_url = os.environ['TG_WEBHOOK_URL']

group_helper_router = APIRouter()

WEBHOOK_PATH = '/tg_webhook'
WEBHOOK_SECRET = os.environ['TG_WEBHOOK_SECRET']
BASE_WEBHOOK_URL = os.environ['TG_WEBHOOK_URL']

router = Router()
start_markup = InlineKeyboardMarkup(inline_keyboard=[
                            [InlineKeyboardButton(text="开始陪聊", url=env_const.TMA_DIRECT_URL)]])

bot = helper_bots[0]
dp = Dispatcher(bot=bot)
dp.include_router(router)

@router.chat_member(ChatMemberUpdatedFilter(IS_NOT_MEMBER >> IS_MEMBER))
async def chat_member_updated_handler(event: types.ChatMemberUpdated, 
                                      bot: Bot) -> None:
    logging.info(f'chat_member_updated_handler: {event}')
    chat_id = event.chat.id
    tg_user_id = event.from_user.id
    chat_ids = await welfare_service.join_task_chat_ids()
    if chat_id not in chat_ids:
        return
    user = await user_service.get_user_by_tg_id(tg_user_id)
    if user is None:
        invite_link = getattr(event.invite_link, 'invite_link', None)
        user = await user_service.register_by_tg_with_invite_link(
            tg_user_id, event.from_user.first_name, event.from_user.last_name, 
            event.from_user.username, chat_id, event.chat.type.upper(), invite_link, event.from_user.is_premium or False, bot.id)
        award = gift_award_service.get_gift_award()
        await gift_award_service.add_award_by_user_gift(user.id, award)

    await welfare_service.handle_chat_join(chat_id, user.id, event.chat.title)
    await check_in_service.check_user_bio(user, event.from_user)

@router.message()
async def handle_message(message: types.Message):
    logging.info(f'handle_message: {message.model_dump()}')
    if message.text is None:
        return
    await check_in_service.process_in_group_check_in(message)
    if message.from_user and message.from_user.is_premium is not None:
        await update_user_premium_status(message.from_user.id, message.from_user.is_premium)
    # 消耗钻活动的相关处理
    await _deal_with_diamond_season(message)

 # 消耗钻活动的相关处理
async def _deal_with_diamond_season(message: types.Message):
    if not message.from_user or message.from_user.is_bot:
        return
    main_group = await tg_config_service.get_main_group()
    if message.chat.id != main_group.chat_id:
        return
    await _deal_with_diamond_season_warming_up(message.chat.id)
    await _deal_with_task_enroll_notice(message.chat.id)

# 消耗钻活动里预热消息的处理
async def _deal_with_diamond_season_warming_up(chat_id: int):
    season_config = await user_diamond_season_service.get_current_warming_up_diamond_season()
    if not season_config:
        return
    method_config = season_config.warming_up_notice_method
    if method_config is None or method_config.method != NoticeMethod.AMOUNT.value:
        return
    interval = method_config.interval
    redis_key = f"wp:season:{season_config.season_id}"
    count = normal_redis_client.incr(redis_key)
    normal_redis_client.expire(redis_key, int(season_config.warming_up_end_at - season_config.warming_up_start_at))
    current_count = int(str(count))
    if current_count % interval == 0:
        idx = random.randint(0, len(helper_bots) - 1)   
        random_bot = helper_bots[idx]
        await random_bot.send_message(chat_id, season_config.warming_up_notice, parse_mode='HTML')

# 消耗钻活动里剩余报名数量的消息处理  
async def _deal_with_task_enroll_notice(chat_id: int):
    # 发送剩余报名数量
    task_config, count = await user_diamond_season_service.get_current_diamond_task_paticipation_info()
    if task_config is None:
        return
    max_count = task_config.max_participants
    remain_count = max_count - count
    if remain_count <= 0:
        return
    if task_config.left_participants_notice_interval <=0:
        return
    redis_key = f"enroll:task:{task_config.task_id}"
    count = normal_redis_client.incr(redis_key)
    normal_redis_client.expire(redis_key, int(task_config.end_at - task_config.start_at))
    current_count = int(str(count))
    if current_count % task_config.left_participants_notice_interval == 0:
        idx = random.randint(0, len(helper_bots) - 1)   
        random_bot = helper_bots[idx]
        msg = user_diamond_season_service.generate_left_amount_notice_msg(task_config, remain_count)
        await random_bot.send_message(chat_id, msg, parse_mode='HTML')





    

def get_message_chat(update: dict) -> tuple[int, int | None, str] | None:
    message = update.get('message')
    if not message:
        return None
    chat = message.get('chat')
    if chat:
        from_user = message.get('from')
        user_id = from_user.get('id') if from_user else None
        return chat.get('id'), user_id, message.get('text')

def is_to_block_text(text: str | None) -> bool:
    if not text:
        return False
    return text.startswith('/') and not text.endswith('@GHClone3Bot')

@group_helper_router.post(WEBHOOK_PATH)
async def webhook_handler(update: dict, x_telegram_bot_api_secret_token: str = Header(...)):
    if x_telegram_bot_api_secret_token != WEBHOOK_SECRET:
        return "Invalid token"
    logging.info(f'webhook_handler: {update}')
    redis_client.publish('tg_webhook', json.dumps(update))

async def handle_webhook_message(update: dict):
    try:
        await dp.feed_raw_update(bot=bot, update=update)
    except Exception as e:
        logging.exception(e)

async def on_startup():
    ps.subscribe('tg_webhook')
    while True:
        message = ps.get_message()
        if message and message['data'] != 1:
            logging.info(f'on_startup: {message}')
            try:
                await handle_webhook_message(json.loads(message['data']))
            except Exception as e:
                logging.warning(e)
        await asyncio.sleep(0.1)

def on_shutdown():
    ps.unsubscribe()
