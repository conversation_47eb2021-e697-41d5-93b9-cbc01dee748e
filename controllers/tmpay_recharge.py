import logging
import os
import hashlib
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel
from controllers.user_check import get_current_user
from persistence.models.models import RechargeChannelEnum, RechargeOrder
from services import re_purchase_service, user_growth_service, tmpay_recharge_service
from services.bot_services import get_bot_by_bot_id
from services.out_recharge_common import RechargeRequest, create_recharge_order
from controllers.user_check import user_service
from utils import env_const

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

tmpay_recharge_router = APIRouter()

class TMPayNotify(BaseModel):
    amount: str
    payAmount: str
    tradeNo: str
    appId: str
    sign: str
    signType: str
    merchantOrderNo: str
    payStatus: str
    body: str | None = None

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign', 'attach'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={app_key}'
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

async def handle_result(notify_result: TMPayNotify):
    if notify_result.appId != env_const.TMPAY_APP_ID:
        logging.info(f"Unknown app id from tmpay: {notify_result.appId}")
        return False
    if notify_result.payStatus != '2':
        return 'FAIL'
    if not notify_result.verify_sign(env_const.TMPAY_APP_KEY):
        return 'verify sign failed'
    order = await tmpay_recharge_service.pay_success(notify_result.merchantOrderNo, notify_result.tradeNo, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    if order.from_bot_id != 0:
        bot = get_bot_by_bot_id(order.from_bot_id)
        if bot:
            tg_user = await user_service.get_tg_info_by_user_id(order.user_id)
            if tg_user:
                content = f'充值成功 {order.amount} 💎 + 🟡'
                try:
                    await bot.send_message(tg_user.tg_id, content)
                except Exception as e:
                    logging.warning(f'chat id {tg_user.tg_id} send message failed: {e}')
    return 'success'

@tmpay_recharge_router.get('/tmpay/result')
async def tmpay_recharge_return(req: Request):
    return HTMLResponse(content=success_html)

@tmpay_recharge_router.post('/tmpay/notify')
async def tmpay_recharge_notify(notify_result: TMPayNotify):
    return await handle_result(notify_result)
