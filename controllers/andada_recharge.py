import json
import logging
import os
import hashlib
import random
from typing import Annotated, Optional
import urllib.parse
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Header, Request
from pydantic import BaseModel
from common.common_constant import Language, RechargeRouteStrategy
from controllers.user_check import get_current_user
from persistence.models.models import RechargeChannelConfig, RechargeChannelEnum, RechargeProduct, RechargeOrder, RechargeStatusEnum
from services import andada_recharge_service, bot_services, ff_recharge_service, jlbzf_recharge_service, qszf_recharge_service, re_purchase_service, recharge_service, sdfkw_recharge_service, sjzf_recharge_service, star_payment_service, tg_config_service, tmpay_recharge_service, user_growth_service, out_recharge_common, recharge_channel_service, xjtzf_recharge_service
from services.andada_recharge_service import andada_recharge_app_key
from services.out_recharge_common import RechargeRequest, create_recharge_order

from controllers.user_check import user_service
from utils import response_util, tg_util
from utils.bucket_factory import UserBasedBucketFactory
from pyrate_limiter import Limiter, Duration, Rate
from persistence.redis_client import redis_client
from utils.translate_util import _tl

recharge_order_rate_limit = Rate(5, Duration.MINUTE)
bucket_factory = UserBasedBucketFactory('recharge_order_bucket', redis_client, [recharge_order_rate_limit])
limiter = Limiter(bucket_factory)

over_limit_tip = '''【温馨提示】您已在1分钟之内生成了3笔支付订单。
为保障您的支付安全，降低重复支付的风险，建议您先完成当前订单。
如需生成新的订单，请您耐心等待30秒，然后重新提交，祝您玩得愉快。'''

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

andada_recharge_router = APIRouter()

class AndadaNotify(BaseModel):
    pid: int
    trade_no: str
    out_trade_no: str
    type: str
    name: str
    money: str
    trade_status: str
    sign: str
    sign_type: str
    params: str = None

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign', 'sign_type'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + app_key
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

async def handle_result(notify_result: AndadaNotify):
    if not notify_result.verify_sign(andada_recharge_app_key):
        return 'verify sign failed'
    if notify_result.trade_status != 'TRADE_SUCCESS':
        return 'FAIL'
    order = await andada_recharge_service.pay_success(notify_result.out_trade_no, notify_result.trade_no, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    return 'success'

@andada_recharge_router.get('/andada_recharge/result')
async def andada_recharge_return(req: Request):
    return HTMLResponse(content=success_html)

@andada_recharge_router.get('/andada_recharge/notify')
async def andada_recharge_notify(req: Request):
    data = req.query_params
    logging.info(f'andada_recharge_notify: {data}')
    notify_result = AndadaNotify(**data)
    return await handle_result(notify_result)

@andada_recharge_router.post('/andada_recharge/notify')
async def andada_recharge_notify_p(req: Request):
    data = await req.form()
    logging.info(f'andada_recharge_notify: {data}')
    notify_result = AndadaNotify(**data)
    return await handle_result(notify_result)

async def star_recharge(req: RechargeRequest, tg_bot_id: str, user_id: int):
    recent_orders = await recharge_service.get_recent_init_orders(user_id)
    if len(recent_orders) >= 5:
        return response_util.error(429, over_limit_tip)
    bot_config = await tg_config_service.get_bot_config_by_tma_bot_id(tg_bot_id)
    if bot_config is None:
        return response_util.error(404, "Bot not found")
    bot = bot_services.get_bot_by_bot_id(bot_config.bot_id)
    if bot is None:
        return response_util.error(404, "Bot not found")
    tg_user: TelegramUser = await user_service.get_tg_info_by_user_id(user_id) # type: ignore

    star_payment, prices = await star_payment_service.create_star_payment_order(
        user_id, tg_user.tg_id, req.recharge_id, bot_config.bot_id)
    if star_payment is None:
        return response_util.error(422, "Failed to create star payment order")

    link = await bot.create_invoice_link(
        '幻梦AI', '幻梦AI充值套餐', str(star_payment.invoice_id), 'XTR', prices)

    return response_util.success({'message': 'success', 'pay_url': link})

class PaymentResponse(BaseModel):
    success: bool
    message: str
    status_code: Optional[int] = None
    pay_url: Optional[str] = None
    raw: Optional[str] = None
    out_order_id: Optional[str] = None

class RechargeChannelHandler:
    def __init__(self, channel_enum: RechargeChannelEnum):
        self.channel_enum = channel_enum
        
    async def create_order(self, user_id: int, recharge_id: str, type_name: str, client_ip: str) -> PaymentResponse:
        order = await create_recharge_order(user_id, recharge_id, self.channel_enum, type_name)
        if order is None:
            return PaymentResponse(success=False, message="找不到套餐", status_code=404, out_order_id="")
        channel_name = self.channel_enum.description
        try:
            result = await self._process_payment(order, type_name, client_ip)
            if result.success:
                return result

            logging.warning(f"充值渠道 {channel_name} 处理失败: {result.raw}")
            await self.update_order_status(str(order.recharge_order_id), RechargeStatusEnum.FAILED)
            await tg_util.send_monitor({
                '充值失败': f'渠道: {channel_name}', 
                '支付方式': type_name,
                'user_id': f'`{user_id}`',
                '套餐': f'`{recharge_id}`', 
                '返回': result.raw or ''
            })
            return PaymentResponse(
                success=False,
                message="充值渠道发生异常，请稍后再试",
                status_code=400,
                raw=str(result.raw),
                out_order_id=str(order.recharge_order_id)
            )
        except Exception as e:
            logging.error(f"充值渠道 {channel_name} 发生异常: {str(e)}")
            await self.update_order_status(str(order.recharge_order_id), RechargeStatusEnum.FAILED)
            await tg_util.send_monitor({
                '充值异常': f'渠道: {channel_name}',
                '支付方式': type_name,
                'user_id': f'`{user_id}`',
                '套餐': f'`{recharge_id}`', 
                '异常': str(e)
            })
            return PaymentResponse(
                success=False,
                message="充值渠道发生异常，请稍后再试",
                status_code=500,
                raw=str(e),
                out_order_id=str(order.recharge_order_id)
            )

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        raise NotImplementedError("implement this method in subclasses")
    
    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        raise NotImplementedError("implement this method in subclasses")
    
    async def update_order_status(self, order_id: str, status: RechargeStatusEnum) -> None:
        await out_recharge_common.update_order_status(order_id, status)

class TmpayHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.TMPAY)
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        data = tmpay_recharge_service.create_tmpay_order(order, type_name, client_ip)
        logging.info(f'tmpay_recharge_response: {data}')
        if data['status'] != 200:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        await self.update_out_order_id(str(order.recharge_order_id), data['data']['tradeNo'])
        return PaymentResponse(success=True, message="success", pay_url=data['data']['payUrl'])
    
    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await tmpay_recharge_service.update_out_order_id(order_id, out_order_id)


class QszfHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.QSZF)
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        data = qszf_recharge_service.create_qszf_order(order, type_name, client_ip)
        logging.info(f'qszf_recharge_response: {data}')
        if data['status'] != 200:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        await self.update_out_order_id(str(order.recharge_order_id), data['data']['tradeNo'])
        return PaymentResponse(success=True, message="success", pay_url=data['data']['payUrl'])
    
    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await qszf_recharge_service.update_out_order_id(order_id, out_order_id)


class SdfkwHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.SDFKW_API)
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        data = sdfkw_recharge_service.create_sdfkw_order(order, type_name, client_ip)
        logging.info(f'sdfkw_recharge_response: {data}')
        if data['code'] != 1:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        pay_url = data['data']['payurl']
        parsed_url = urllib.parse.urlparse(pay_url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        trade_no = query_params.get('trade_no', [None])[0]
        await self.update_out_order_id(str(order.recharge_order_id), trade_no)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)
    
    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await sdfkw_recharge_service.update_out_order_id(order_id, out_order_id)

class FFPayHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.FFPAY)

    async def _try_create_ffpay_order(self, order: RechargeOrder, type_name: str, client_ip: str) -> tuple[bool, dict, str]:
        data = ff_recharge_service.create_ffpay_order(order, type_name, client_ip)
        logging.info(f'ffpay_recharge_response: {data}')
        if data['code'] != 1:
            return False, data, json.dumps(data, ensure_ascii=False)
        
        await self.update_out_order_id(str(order.recharge_order_id), data['trade_no'])
        pay_url = data.get('payurl', data.get('qrcode'))
        return True, data, pay_url
            
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        # Try up to 3 times
        for attempt in range(1, 4):
            success, data, result = await self._try_create_ffpay_order(order, type_name, client_ip)

            if success:
                return PaymentResponse(success=True, message="success", pay_url=result)

            if attempt < 3:
                logging.warning(f"FFPay attempt {attempt} failed, retrying... Error: {result}")

        return PaymentResponse(
            success=False,
            message="支付失败",
            status_code=400,
            out_order_id=str(order.recharge_order_id)
        )

    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await ff_recharge_service.update_out_order_id(order_id, out_order_id)

class SJZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.SJZF)
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        data = sjzf_recharge_service.create_sjzf_order(order, type_name, client_ip)
        logging.info(f'sjzf_recharge_response: {data}')
        if data['code'] != 1:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        out_order_id = data['trade_no']
        await self.update_out_order_id(str(order.recharge_order_id), out_order_id)
        pay_url = data.get('payurl', data.get('qrcode'))
        return PaymentResponse(success=True, message="success", pay_url=pay_url)

    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await out_recharge_common.update_out_order_id(order_id, out_order_id)

class JLBZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.JLBZF)
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        data = jlbzf_recharge_service.create_jlbzf_order(order, type_name, client_ip)
        logging.info(f'jlbzf_recharge_response: {data}')
        if data['status'] != '1':
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        pay_url = data.get('h5_url', data.get('pay_url', data.get('sdk_url')))
        out_order_id = self._extract_out_order_id(pay_url)
        if not out_order_id:
            out_order_id = str(order.recharge_order_id)

        await self.update_out_order_id(str(order.recharge_order_id), out_order_id)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)

    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await out_recharge_common.update_out_order_id(order_id, out_order_id)

    def _extract_out_order_id(self, url: str) -> str:
        parsed_url = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        if 'key' in query_params and query_params['key']:
            out_order_id = query_params['key']
            if out_order_id:
                return out_order_id[0]

        if 'a' in query_params and query_params['a']:
            out_order_id = query_params['d']
            if out_order_id:
                return out_order_id[0]
        return ""

class XJTZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.XJTZF)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> PaymentResponse:
        data = xjtzf_recharge_service.create_xjtzf_order(order, type_name, client_ip)
        logging.info(f'xjtzf_recharge_response: {data}')
        if data['code'] != 0:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        pay_url = data['data']['payData']
        out_order_id = data['data']['payOrderId']
        await self.update_out_order_id(str(order.recharge_order_id), out_order_id)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)

    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        await xjtzf_recharge_service.update_out_order_id(order_id, out_order_id)

class RechargeHandlerFactory:
    @staticmethod
    def get_handler(channel_name: str) -> RechargeChannelHandler:
        handlers = {
            'TMPAY': TmpayHandler(),
            'QSZF': QszfHandler(),
            'SDFKW_API': SdfkwHandler(),
            'FFPAY': FFPayHandler(),
            'SJZF': SJZFHandler(),
            'JLBZF': JLBZFHandler(),
            'XJTZF': XJTZFHandler(),
        }
        return handlers.get(channel_name, TmpayHandler())  # 默认使用TmpayHandler

class RechargeChannelManager:
    @staticmethod
    async def process_with_failover(user_id: int, req: RechargeRequest, client_ip: str,
                                   primary_channel: str, secondary_channels: list[str]) -> PaymentResponse:
        available_channels = []
        channels_to_try = recharge_channel_service.get_all_can_use_channels()
        if primary_channel in channels_to_try:
            available_channels.append(primary_channel)

        for ch in secondary_channels:
            if ch in channels_to_try:
                available_channels.append(ch)

        if not available_channels:
            logging.error("所有充值渠道均不可用")
            return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

        # 依次尝试所有可用渠道
        for channel_name in available_channels:
            handler = RechargeHandlerFactory.get_handler(channel_name)
            logging.info(f"尝试使用充值渠道: {channel_name}")
            result = await handler.create_order(user_id, req.recharge_id, req.type, client_ip)
            if result.success:
                logging.info(f"充值渠道 {channel_name} 处理成功")
                return result
            else:
                logging.info(f"充值渠道 {channel_name} 不可用: {result.message}， 尝试下一个渠道")
                continue

        return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

    @staticmethod
    async def process_with_manual_routing(user_id: int, req: RechargeRequest, client_ip: str) -> PaymentResponse:
        # 获取充值产品信息以获取金额
        recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
        if not recharge_product:
            return PaymentResponse(success=False, message="找不到充值套餐", status_code=404)

        pay_type = 'wechat' if req.type == 'wxpay' else req.type
        amount = recharge_product.cny_price
        user_controls = await recharge_channel_service.get_manual_strategy_channels(pay_type, amount)
        all_channels = [c.channel.value for c in user_controls]
        channel = await recharge_channel_service.get_channel_queue_item(pay_type)
        if channel and channel in all_channels:
            all_channels.remove(channel)
            all_channels.insert(0, channel)

        for channel_name in all_channels:
            handler = RechargeHandlerFactory.get_handler(channel_name)
            logging.info(f"尝试使用充值渠道: {channel_name}")
            result = await handler.create_order(user_id, req.recharge_id, pay_type, client_ip)
            if result.success:
                logging.info(f"充值渠道 {channel_name} 处理成功")
                return result
            else:
                logging.info(f"充值渠道 {channel_name} 不可用: {result.message}， 尝试下一个渠道")
                continue

        return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

    @staticmethod
    async def process_with_smart_routing(user_id: int, req: RechargeRequest, client_ip: str) -> PaymentResponse:
        # 获取充值产品信息以获取金额
        recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
        if not recharge_product:
            return PaymentResponse(success=False, message="找不到充值套餐", status_code=404)

        # 确定用户类型，这里简化为根据支付类型判断
        queue_type = 'type1' if req.channel == 'channel1' else 'type2'
        pay_type = 'wechat' if req.type == 'wxpay' else req.type
        amount = recharge_product.cny_price

        # 获取智能路由队列，传递支付类型和金额
        primary_queue, secondary_queue = await recharge_channel_service.get_channel_queues_for_routing(queue_type, pay_type, amount)

        # 根据用户历史过滤渠道
        primary_queue = await recharge_channel_service.filter_channels_by_user_history(primary_queue, user_id)

        all_channels = primary_queue

        logging.info(f"智能路由：渠道类型={queue_type}, 支付类型={pay_type}, 金额={recharge_product.cny_price}")
        logging.info(f"智能路由：主队列={primary_queue}, 备用队列={secondary_queue}")

        rkey = 'recharge_channels_used'
        used_channels = redis_client.lrange(rkey, -5, -1) # type: ignore

        used_channels: list[str] = [c.decode() for c in used_channels]
        used_with_order = {c:i for i, c in enumerate(used_channels)}
        all_channels.sort(key = lambda x: used_with_order.get(x, -1))

        if not all_channels:
            logging.info("智能路由：所有充值渠道均不可用，fallback 所有可用渠道")
            all_channels = await recharge_channel_service.get_enabled_channels_for_request(pay_type, amount)
            if not all_channels:
                return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

        # 依次尝试所有可用渠道
        for channel_name in all_channels:
            handler = RechargeHandlerFactory.get_handler(channel_name)
            logging.info(f"智能路由：尝试使用充值渠道: {channel_name}")
            result = await handler.create_order(user_id, req.recharge_id, pay_type, client_ip)
            if result.success:
                logging.info(f"智能路由：充值渠道 {channel_name} 处理成功")
                redis_client.rpush(rkey, channel_name)
                return result
            else:
                logging.info(f"智能路由：充值渠道 {channel_name} 不可用: {result.message}， 尝试下一个渠道")
                continue

        return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

async def handle_recharge_request(user_id: int, req: RechargeRequest, client_ip: str,
                                 channel_config: RechargeChannelConfig, current_language: str) -> JSONResponse:
    recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
    if recharge_product is None:
        return response_util.error(404, _tl("找不到充值套餐", current_language))

    route_strategy = await recharge_channel_service.get_current_mode()
    if route_strategy == RechargeRouteStrategy.MANUAL:
        result = await RechargeChannelManager.process_with_manual_routing(user_id, req, client_ip)
    else:
        result = await RechargeChannelManager.process_with_smart_routing(user_id, req, client_ip)

    if not result.success:
        return JSONResponse(content={"message": _tl(result.message, current_language)}, 
                            status_code=result.status_code or 400)

    return JSONResponse(content={'message': result.message, 'pay_url': result.pay_url})

async def handle_stripe_payment(user_id: int, recharge_product: RechargeProduct, return_host: str):
    checkout_url = await recharge_service.create_checkout_session(user_id, recharge_product, return_host)
    return JSONResponse(content={'message': 'success', 'pay_url': checkout_url})

@andada_recharge_router.post('/andada_recharge/recharge')
async def andada_recharge_recharge(req: RechargeRequest, request: Request, 
                                   tg_bot_id: Annotated[str, Header()] = None,
                                   user_id=Depends(get_current_user),
                                   current_language: str = Header(default=Language.ZH.value)):
    recent_orders = await recharge_service.get_recent_init_orders(user_id)
    if len(recent_orders) >= 5:
        return response_util.error(429, over_limit_tip)

    user = await user_service.get_user_by_id(user_id)
    await user_growth_service.notify_recharge_channels(user)

    recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
    if recharge_product is None:
        return response_util.error(404, "找不到充值套餐")

    if req.type == 'star':
        return await star_recharge(req, tg_bot_id, user_id)

    if req.type == 'stripe':
        refer = request.headers.get('Referer')
        if refer:
            r = urllib.parse.urlparse(refer)
            return_host = f'{r.scheme}://{r.netloc}'
        else:
            return_host = os.environ['WEB_APP_URL'].rstrip('/')
        return await handle_stripe_payment(user_id, recharge_product, return_host)

    # 其他支付渠道
    channel_config = await recharge_channel_service.get_by_product_id(req.recharge_id)
    if channel_config is None:
        channel_config = RechargeChannelConfig.default_config()
        
    return await handle_recharge_request(user_id, req, request.client.host, channel_config, current_language)