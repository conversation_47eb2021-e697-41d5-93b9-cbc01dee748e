from datetime import timed<PERSON><PERSON>
import logging
import os
import urllib.parse
from aiogram import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, Router, types
from aiogram.enums import ParseMode
from aiogram.filters import CommandStart, CommandObject, Command, ChatMemberUpdatedFilter, IS_MEMBER, IS_NOT_MEMBER
from aiogram.filters.callback_data import CallbackData
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton, ReplyKeyboardRemove, WebAppInfo, KeyboardButton, ReplyKeyboardMarkup, CopyTextButton, PreCheckoutQuery
from aiogram.types.bot_command import BotCommand
from aiogram.utils.deep_linking import decode_payload, create_start_link
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.types import Chat<PERSON>emberUpdated
from fastapi import Header, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.routing import APIRouter
from common import bot_common
from common.common_constant import <PERSON><PERSON><PERSON><PERSON><PERSON>
from controllers.bot_hooks.bot_setting import send_login_link
from persistence.models.models import RechargeStatusEnum, TgBotConfig, UserRegisterSource
from services import andada_recharge_service, check_in_service, config_service, gift_award_service, star_payment_service, tg_config_service, tg_message_service, user_active_service, user_diamond_season_service, user_growth_service, welfare_service
from services.bot_message_service import format_content_replace
from services.role import role_loader_service
from utils import env_const, user_growth_constants, utils
from common.bot_common import CheckInCallback, DiamondSeasonRewardCallback, charge_url, new_user_tip
from services.bot_services import bot, get_register_source_by_bot, send_invite_notice, send_unlock_tip, tma_bots_map, get_bot_name, send_transfer_message, send_invitation_message
from utils.translate_util import _tl

from .user_check import user_service

webhook_url = os.environ['TG_WEBHOOK_URL']

class RechargeCallback(CallbackData, prefix='charge'):
    recharge_product_id: str
    type: str

tg_router = APIRouter()

tips = f'''
后宫佳丽们已经等候多时了，今天你会来翻谁的牌子呢？

点击下方”开始陪聊“按钮，立即开始享受帝王般的体验！

【如果无法载入或加载缓慢】可能是电报app版本问题，可点击下方链接使用极速版：

https://t.me/{BotReplace.MAIN_CHAT_BOT.value}?start=u_300
'''

start_tips_for_exist_user = '''
欢迎来到幻梦AI伴侣，你的专属梦中情人已经等候多时了！

今天你会来翻谁的牌子呢？快点击下方按钮开始聊天吧！
'''


TOKEN = os.environ["TMA_BOT_TOKEN"]
WEBHOOK_PATH = '/tg_webhook'
WEBHOOK_SECRET = os.environ['TG_WEBHOOK_SECRET']
BASE_WEBHOOK_URL = os.environ['TG_WEBHOOK_URL']

CHAT_BOT_TOKEN = os.environ["CHAT_BOT_TOKEN"]

router = Router()

url = os.environ['TMA_URL']
pay_url = utils.change_url_path(url, '/pay')
async def on_tg_startup() -> None:
    ...
    #await bot.delete_webhook(True)
    #await bot.set_webhook(f"{BASE_WEBHOOK_URL}{WEBHOOK_PATH}", secret_token=WEBHOOK_SECRET, allowed_updates=["chat_member", "callback_query"])

dp = Dispatcher()
dp.include_router(router)

def get_start_markup_by_url(url: str) -> InlineKeyboardMarkup:
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text='开始陪聊', url=url)]
    ])

#迁移到bot_service
async def get_start_markup(tbot: Bot, text: str = '开始陪聊', lang = 'zh') -> InlineKeyboardMarkup:
    me = await tbot.get_me()
    url = f'https://t.me/{me.username}/tavern'
    text = _tl(text, lang)
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text=text, url=url)]
    ])

reply_markup_text = '进入「幻梦AI升级版」'
def get_keyboard_markup() -> ReplyKeyboardMarkup:
    return ReplyKeyboardMarkup(keyboard=[
        [KeyboardButton(text=reply_markup_text)]
    ], resize_keyboard=True)

#@router.message(CommandStart())
async def command_start_handler_direct(message: Message) -> None:
    try:
        user = await user_service.get_user_by_tg_id(message.from_user.id)
        if user is None:
            await user_service.register_by_tg(message.from_user.id, message.from_user.first_name, message.from_user.last_name, message.from_user.username)
    except Exception as e:
        logging.error(f'Error in command_start_handler_direct: {e}')
    start_markup = await get_start_markup(bot)
    content = await format_content_replace(tips)
    await message.answer(content, parse_mode=ParseMode.HTML, reply_markup=start_markup)

async def create_share_button(user_id: int, bot: Bot) -> InlineKeyboardMarkup:
    link = await bot_common.create_invite_link(user_id, bot)
    text = urllib.parse.quote('👆点我体验幻梦AI，一款AI角色扮演聊天产品，满足你的一切幸福幻想，分享赚🟡无上限')
    share_url = f'tg://msg_url?url={link}&text={text}'
    builder = InlineKeyboardBuilder()
    builder.button(text='复制邀请链接', copy_text=CopyTextButton(text=link))
    builder.button(text='分享邀请链接', url=share_url)
    builder.adjust(1)
    return builder.as_markup()

@router.message(CommandStart())
async def command_start_handler(message: Message, command: CommandObject,
                                bot: Bot) -> None:
    logging.info(f'command_start_handler args: {command.args}')
    tg_id = message.from_user.id
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    remove_keyboard_message = None
    inviter_user_id = None
    tg_bot_config: TgBotConfig = await tg_config_service.get_bot_config(bot) # type: ignore
    register_source = get_register_source_by_bot(bot)
    start_markup = await get_start_markup(bot, tg_bot_config.lang)
    is_login = command.args is not None and command.args.startswith('web')
    is_check_in = command.args is not None and command.args.startswith('ct')
    is_act_enroll = command.args is not None and command.args.startswith('act_enroll_')
    if user is None:
        if (command.args is not None 
            and not command.args.startswith('web')
            and not command.args.startswith('ct')
            and not is_act_enroll):
            if command.args.startswith('u_'):
                inviter_user_id = command.args[2:]
                if inviter_user_id.startswith('0x'):
                    inviter_user_id = int(inviter_user_id, 16)
                else:
                    inviter_user_id = int(inviter_user_id)
            else:
                inviter_user_id = int(decode_payload(command.args))
            await user_service.register_tg_with_invitation(
                message.from_user.id, message.from_user.first_name,
                message.from_user.last_name, message.from_user.username,
                inviter_user_id, register_source, message.chat.id, 
                is_premium=message.from_user.is_premium or False, from_bot_id=bot.id)
            is_invitation = True
        else:
            await user_service.register_by_tg(
                message.from_user.id, message.from_user.first_name, 
                message.from_user.last_name, message.from_user.username, 
                register_source, message.from_user.is_premium or False, from_bot_id=bot.id)
        content = await format_content_replace(tips)
        content = _tl(content, tg_bot_config.lang)
        sent_message = await message.answer(content, parse_mode=ParseMode.HTML, reply_markup=start_markup)
        user = await user_service.get_user_by_tg_id(message.from_user.id)
        # await user_growth_service.check_and_notify_fc_reward(user)
        await user_growth_service.forward_register(user.id, message.from_user.id, inviter_user_id)
        # await bot.send_message(message.chat.id, new_user_tip, reply_markup=ReplyKeyboardRemove())
        # await user_growth_service.new_recharge_product(tg_id,bot)
    elif not is_login and not is_check_in and not is_act_enroll:
        await message.answer(_tl(start_tips_for_exist_user, tg_bot_config.lang), parse_mode=ParseMode.HTML, reply_markup=start_markup)
        remove_keyboard_message = await message.answer(_tl('欢迎来到幻梦 AI', tg_bot_config.lang), reply_markup=ReplyKeyboardRemove())
    if is_login:
        await send_login_link(message.chat.id, bot)
        remove_keyboard_message = await message.answer(_tl('欢迎来到幻梦 AI', tg_bot_config.lang), reply_markup=ReplyKeyboardRemove())

    if remove_keyboard_message is not None:
        await remove_keyboard_message.delete()
    if is_act_enroll:
        await _handle__diamond_season_enroll_deeplink(command.args, message, bot)
    if is_check_in:
        await check_in_service.process_in_bot_check_in(message, bot, message.from_user)
    if inviter_user_id is not None:
        await send_invite_notice(message, inviter_user_id)

# 处理「聊天返钻石活动」的报名deeplink
async def _handle__diamond_season_enroll_deeplink(args: str, message: Message, bot: Bot):
    if not args.startswith('act_enroll_'):
        return
    task_id = args.replace('act_enroll_', '')
    if task_id == '':
        return
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if user is None:
        return
    role_ids, msg = await user_diamond_season_service.enroll_diamond_season_activity(user.id, task_id)
    # -1 表示所有角色卡都支持
    if not role_ids or -1 not in role_ids:
        await bot.send_message(message.chat.id, msg, parse_mode=ParseMode.HTML)
    else:
        start_markup = await get_start_markup(bot)
        await bot.send_message(message.chat.id, msg, reply_markup=start_markup, parse_mode=ParseMode.HTML)
    
    extra_msg = await user_diamond_season_service.get_diamond_activity_role_msg(role_ids)
    if extra_msg:
        await bot.send_message(message.chat.id, extra_msg, parse_mode=ParseMode.HTML)  



    
invite_text = '''
您的邀请链接为:

`{link}` 

点击可复制链接，分享给好友，邀请好友加入幻梦 AI！
'''
@router.message(Command(BotCommand(command='invite_link', description='获取邀请链接')))
async def command_invite_link_handler(message: Message, bot: Bot) -> None:
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    start_markup = await get_start_markup(bot)
    if user is None:
        logging.info(f'TG user not found: {message.from_user.id}')
        await message.answer('请先体验幻梦 AI 后再分享哦', reply_markup=start_markup)
    else:
        await send_invitation_message(message, user, bot)

@router.message(Command(BotCommand(command='recharge', description='充值')))
async def command_recharge_handler(message: Message, bot: Bot) -> None:
    me = await bot.get_me()
    link = f'https://t.me/{me.username}'
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    # is_pay_user = await user_service.is_payed_user(user.id)
    charge_message = await bot_common.chat_bot_charge_tip_wo_link(user.id,link)
    await message.answer(charge_message, parse_mode=ParseMode.HTML,
                        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                            [InlineKeyboardButton(text="立即充值", url=charge_url)]
                        ]))

@router.message(Command(BotCommand(command='preview_url', description='preview 地址')))
async def command_preview_url_handler(message: Message, command: CommandObject) -> None:
    preview_url = os.environ.get('TMA_PREVIEW_URL', url)
    tma_url = f'{env_const.TMA_BOT_URL}/preview'
    builder = InlineKeyboardBuilder()
    builder.button(text='web app info', web_app=WebAppInfo(url=preview_url))
    builder.button(text='direct url', url=tma_url)
    await message.answer('preview url', parse_mode=ParseMode.HTML,
                        reply_markup=builder.as_markup())

@router.message(Command(BotCommand(command='role_bot', description='角色橱窗')))
async def command_role_bot(message: Message, command: CommandObject) -> None:
    await message.answer('打开角色橱窗，一起浏览和讨论所有角色', parse_mode=ParseMode.HTML,
                        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                            [InlineKeyboardButton(text="打开橱窗", url=user_growth_constants.ROLE_CHANNEL_LINK)]]))

@router.message(Command(BotCommand(command='unlock', description='解锁')))
async def unlock_help(message: types.Message, bot: Bot):
    await send_unlock_tip(bot, message)

@tg_router.get('/pay_in_tg')
async def do_recharge(payload: str, product_id: str, type: str, request: Request) -> None:
    user_id = int(decode_payload(payload=payload))
    user = await user_service.get_user_by_id(user_id)
    order = await andada_recharge_service.create_deposit_order(user.id, product_id)
    data = andada_recharge_service.create_andada_order(order, type, request.client.host)
    if data['code'] != 1:
        logging.warning(f'create andada order failed: {data}')
        return HTMLResponse(content='recharge failed', status_code=500)
    pay_url = data['payurl']
    return RedirectResponse(url=pay_url)

chat_join_tip = f'''欢迎加入幻梦AI伴侣群组，赠送您 500 🟡

群组地址：{user_growth_constants.TARGET_JOIN_GROUP_LINK}
'''

# chatbot_group_join_tip = '''【活动12-加入群聊完成通知】恭喜您加入官方群，500🟡 已到账
# 欢迎您在群组中踊跃发言，官方有任何活动和福利，也会第一时间在群里中发布'''
chatbot_group_join_tip = f'''🎉🎉恭喜你完成加入幻梦官方群，获得500🟡，可在充值页面底部查询
幻梦官方群：{user_growth_constants.TARGET_JOIN_GROUP_LINK}'''
# chatbot_channel_join_tip = '''【活动2-加入频道完成通知】恭喜您加入官方角色卡频道，500🟡 已到账
# 当平台上线新的角色卡，会第一时间在频道中发布，敬请期待'''
chatbot_channel_join_tip = f'''🎉🎉恭喜你完成加入幻梦官方频道，获得500🟡，可在充值页面底部查询
幻梦官方频道：{user_growth_constants.ROLE_CHANNEL_LINK}'''




check_in_tips = f'''幻梦AI伴侣签到成功，赠送您 300 🟡'''

@router.chat_member(ChatMemberUpdatedFilter(IS_NOT_MEMBER >> IS_MEMBER))
async def chat_member_updated_handler(event: ChatMemberUpdated,
                                      bot: Bot) -> None:
    logging.info(f'chat_member_updated_handler: {event}')
    chat_id = event.chat.id
    tg_user_id = event.from_user.id
    # chat_ids = await config_service.join_task_chat_ids()
    # if chat_id not in chat_ids:
    #     return
    
    user = await user_service.get_user_by_tg_id(tg_user_id)
    if user is None:
        invite_link = getattr(event.invite_link, 'invite_link', None)
        user = await user_service.register_by_tg_with_invite_link(
            tg_user_id, event.from_user.first_name, event.from_user.last_name,
            event.from_user.username, chat_id, event.chat.type.upper(), 
            invite_link, event.from_user.is_premium or False, bot.id)
        award = gift_award_service.get_gift_award()
        await gift_award_service.add_award_by_user_gift(user.id, award)

    await welfare_service.handle_chat_join(chat_id, user.id, event.chat.title)
    

@router.message(Command(BotCommand(command='web', description='登录 web 版')))
async def handle_web_login(message: Message, bot: Bot) -> None:
    await send_login_link(message.chat.id, bot)

@router.message(Command(BotCommand(command='checkin', description='签到')))
async def handle_check_in(message: Message, bot: Bot) -> None:
    await check_in_service.process_in_bot_check_in(message, bot)

@router.callback_query(CheckInCallback.filter())
async def handle_recharge(query: CallbackQuery, bot: Bot,
                          callback_data: CheckInCallback):
    await check_in_service.process_in_bot_check_in(query.message, bot, query.from_user)


#处理「聊天返钻石活动」的 领取inline button
@router.callback_query(DiamondSeasonRewardCallback.filter())
async def handle_diamond_season_reward(query: CallbackQuery, bot: Bot,
                          callback_data: DiamondSeasonRewardCallback):
    task_id = callback_data.task_id
    user = await user_service.get_user_by_tg_id(query.from_user.id)

    if user is None or user.id != callback_data.user_id:
        await bot.send_message(query.message.chat.id, "领取链接不合法", parse_mode=ParseMode.HTML)
        return
    
    _, msg = await user_diamond_season_service.receive_diamond_reward(user.id, task_id)
    await bot.send_message(query.message.chat.id, msg, parse_mode=ParseMode.HTML)
    
@router.pre_checkout_query()
async def pre_checkout_query_handler(query: PreCheckoutQuery):
    payment_id = query.invoice_payload
    order = await star_payment_service.get_star_payment_order(payment_id, query.from_user.id)
    if order is None:
        await query.answer(False, '订单不存在')
        return
    if order.status == RechargeStatusEnum.SUCCEED:
        await query.answer(False, '订单已支付')
        return
    if order.status != RechargeStatusEnum.INIT:
        await query.answer(False, '订单状态错误')
        return
    await query.answer(True)

@router.message(F.successful_payment)
async def handle_successful_payment(message: Message):
    if not message.successful_payment:
        return
    await star_payment_service.on_star_payment_success(message.successful_payment)

@router.message()
async def handle_message(message: types.Message, bot: Bot):
    # message to json string

    logging.info(f'handle_message: {message.model_dump()}')
    if message.text is None:
        return
    if message.text.startswith(reply_markup_text):
        reply_markup = await get_start_markup(bot, reply_markup_text)
        await message.answer('点击下方按钮进入【幻梦AI升级版】', reply_markup=reply_markup)
        return

    await check_in_service.process_direct_message_check_in(message, bot)

@tg_router.get('/check_in_by_tg_id')
async def get_webhook(tg_id: int = 0):
    user = await user_service.get_user_by_tg_id(tg_id)
    if user is None:
        return 'Hello, Telegram!'
    await user_growth_service.create_check_in_task(user.id)
    return 'success'

def get_message_chat(update: dict) -> tuple[int, int | None, str] | None:
    message = update.get('message')
    if not message:
        return None
    chat = message.get('chat')
    if chat:
        from_user = message.get('from')
        user_id = from_user.get('id') if from_user else None
        return chat.get('id'), user_id, message.get('text')

def is_to_block_text(text: str | None) -> bool:
    if not text:
        return False
    return text.startswith('/') and not text.endswith('@GHClone3Bot')

@tg_router.post(WEBHOOK_PATH)
async def webhook_handler(update: dict, x_telegram_bot_api_secret_token: str = Header(...)):
    if x_telegram_bot_api_secret_token != WEBHOOK_SECRET:
        return "Invalid token"
    logging.info(f'webhook_handler: {update}')
    chat = get_message_chat(update)
    if chat:
        chat_id, user_id, text = chat
        if is_to_block_text(text) and chat_id != user_id:
            logging.info(f'webhook_handler: ignore command send to group: {text}')
            try:
                reply_message = await bot.send_message(chat_id=chat_id, text='指令发送不生效，可前往【幻梦AI伴侣】机器人输入指令可生效哦🥰🥰（@FancyTavernBot）')
                await tg_message_service.add_deleted_message_from_raw(update, reply_message, timedelta(minutes=1),bot.id)
            except Exception as e:
                pass
            return

    try:
        await dp.feed_raw_update(bot=bot, update=update)
    except Exception as e:
        logging.exception(e)

@tg_router.post('/tg_webhook/tma/{tma_id}')
async def webhook_handler_tma_general(update: dict,
                          tma_id: str,
                          x_telegram_bot_api_secret_token: str = Header(...)):
    if x_telegram_bot_api_secret_token != WEBHOOK_SECRET:
        return "Invalid token"
    logging.info(f'webhook_handler: {tma_id},{update}')

    try:
        bot = tma_bots_map[tma_id.upper()]
        await dp.feed_raw_update(bot=bot, update=update)
        message = update.get('message')
        if message and "from" in message and "id" in message.get('from'):
            tg_id = message.get('from').get('id')
            is_bot = message.get('from').get('is_bot')
            if is_bot:
                return
            await user_active_service.refresh_by_tg(bot.id, tg_id)
            await send_transfer_message(tg_id, bot)
    except Exception as e:
        logging.exception(e)
