import asyncio
import json
import math
from aiogram import Bo<PERSON>, types
from aiogram.enums import ParseMode
from aiogram.filters import CommandObject, Command
from aiogram.types import CallbackQ<PERSON>y, InlineKeyboardButton, Message, InlineKeyboardMarkup
from aiogram.types.bot_command import <PERSON>t<PERSON>ommand
from aiogram.utils.deep_linking import create_start_link
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.fsm.context import FSMContext
from aiogram.filters.callback_data import CallbackData
from common.common_constant import Language, RoleTag
from common.role_model import RoleFilterResponse, RoleRes
from controllers.user_check import user_service
from services import role_config_service
from services.role_config_service import RoleConfigService
from services.bot_services import is_sfw_bot, is_special_seo_bot
from utils import role_util, str_util, user_growth_constants
from .bot_setting import ActivityDiamondSeasonSlideCallback, RoleListPageCallback, RoleSelectCallback, handle_spoiler_avatar, map_lang_code, router, start_new_chat, NewRoleSlideCallback

roles_per_page = 10

role_desc_template='''<b>{i}. {title} {avatar}</b>

描述：{desc}

标签：{tags}
'''

# async def get_role_list_old(bot: Bot, chat_id: int, page: int, user_name: str, message_id: int | None = None):
#     roles = await RoleConfigService.get_effective_role_config_by_uid(0)
#     contents = []
#     # TODO: sort using db
#     start = (page - 1) * roles_per_page
#     end = page * roles_per_page
#     builder = InlineKeyboardBuilder()
#     cur_page_roles = roles[start:end]
#     media_group = []
#     for i, role in enumerate(cur_page_roles):
#         builder.button(text=str(i+1), callback_data=RoleSelectCallback(role_id=role.id))
#         desc = str_util.format_char(role.introduction, role.role_name)
#         desc = str_util.format_user(desc, user_name)
#         tags = '，'.join(role.sub_tags)
#         avatar = f'<a href="{role.role_avatar}">头像</a>' if i == 0 else ''
#         contents.append(role_desc_template.format(i=i+1, title=role.role_name, desc=desc, tags=tags, avatar=avatar))
#         media_group.append(types.InputMediaPhoto(media=role.role_avatar))
#     if page > 1:
#         builder.button(text='上一页', callback_data=RoleListPageCallback(page=page-1))
#     if len(roles) > end:
#         builder.button(text='下一页', callback_data=RoleListPageCallback(page=page+1))
#     builder.adjust(math.ceil(roles_per_page/2))

#     media_group[0].caption = '\n'.join(contents)
#     #await bot.send_media_group(chat_id, media=media_group)
#     if message_id is not None:
#         await bot.edit_message_text(text='\n'.join(contents), chat_id=chat_id, message_id=message_id,
#                                parse_mode=ParseMode.HTML, reply_markup=builder.as_markup())
#     else:
#         await bot.send_message(chat_id, '\n'.join(contents),
#                                parse_mode=ParseMode.HTML, reply_markup=builder.as_markup())

async def send_role(message: types.Message, command: CommandObject,
                 bot: Bot, state: FSMContext):
    role_id = int(command.args)
    role = await RoleConfigService.get_role_config(role_id)
    builder = InlineKeyboardBuilder()
    link = await create_start_link(bot, json.dumps({'role_id': role_id}), encode=True)
    builder.button(text='开始聊天', url=link)
    intro = str_util.format_char(role.introduction, role.role_name)
    content = f'''<b>{role.role_name}</b>\n{intro}'''
    await bot.send_photo(message.chat.id, photo=str_util.format_avatar(role.role_avatar),
                         caption=content, reply_markup=builder.as_markup())

@router.callback_query(RoleListPageCallback.filter())
async def handle_page(query: CallbackQuery, callback_data: RoleListPageCallback,
                 bot: Bot, state: FSMContext):
    await send_role_card(query.message, bot, state, callback_data.page)
    await state.set_state(None)

@router.callback_query(RoleSelectCallback.filter())
async def handle_role_select(query: CallbackQuery, callback_data: RoleSelectCallback,
                 bot: Bot, state: FSMContext):
    role_id = callback_data.role_id
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    await state.update_data({'role_id': role_id})
    await start_new_chat(query.message.chat.id, bot, state, user)
    await state.set_state(None)

async def send_role_card(message: types.Message, bot: Bot, state: FSMContext,
                         page: int, refresh: bool = False, tag = RoleTag.CHOSEN.value):
    nsfw = False # mask all images # not is_sfw_bot(bot)
    role = await role_config_service.role_bot_next(nsfw,page-1)
    builder = InlineKeyboardBuilder()
    builder.button(text='开始陪聊', callback_data=RoleSelectCallback(role_id=role.id))
    msize = 0
    if page > 1:
        builder.button(text='上一页', callback_data=NewRoleSlideCallback(page=page-1, tag=tag))
        msize += 1
    builder.button(text='下一页', callback_data=NewRoleSlideCallback(page=page+1, tag=tag))
    msize += 1
    builder.button(text='查看更多', url=user_growth_constants.ROLE_CHANNEL_LINK)
    builder.adjust(1, msize, 1)
    intro = role.introduction
    content = f'''<b>{role.role_name}</b>\n{intro}'''
    spoiler_avatar = bool(is_special_seo_bot(bot) and role.nsfw)
<<<<<<< HEAD
    avatar = str_util.handle_spoiler_avatar(role.role_avatar) #if spoiler_avatar else str_util.format_avatar(role.role_avatar)
    if refresh:
        await bot.edit_message_media(chat_id=message.chat.id, 
                                     message_id=message.message_id,
                                     media=types.InputMediaPhoto(media=avatar, caption=content),
                                     reply_markup=builder.as_markup())
    else:
        await bot.send_photo(message.chat.id, photo=avatar, 
                             caption=content, reply_markup=builder.as_markup())
    await state.set_state(None)

# 聊天返钻活动
async def send_activity_role_card(message: types.Message, bot: Bot, state: FSMContext,
                         page: int, role_ids_str: str, refresh: bool = False):
    role_ids = role_ids_str.split(',')
    current_role_id = int(role_ids[page])
    role_config = await role_config_service.get_by_id(current_role_id)
    role_config = role_util.format_role_config(role_config, "")
    role = RoleRes.from_model(role_config,False)
    builder = InlineKeyboardBuilder()
    builder.button(text='开始陪聊', callback_data=RoleSelectCallback(role_id=current_role_id))
    msize = 0
    if page > 0:
        builder.button(text='上一页', callback_data=ActivityDiamondSeasonSlideCallback(page=page-1, role_ids=role_ids_str))
        msize += 1
    if page < len(role_ids) - 1:
        builder.button(text='下一页', callback_data=ActivityDiamondSeasonSlideCallback(page=page+1, role_ids=role_ids_str))
        msize += 1
    builder.adjust(1, msize)
    intro = role.introduction
    content = f'''<b>{role.role_name}</b>\n{intro}'''
    spoiler_avatar = bool(is_special_seo_bot(bot) and role.nsfw)
    avatar = str_util.handle_spoiler_avatar(role.role_avatar) if spoiler_avatar else str_util.format_avatar(role.role_avatar)
||||||| 38d682ae
    avatar = str_util.handle_spoiler_avatar(role.role_avatar) if spoiler_avatar else str_util.format_avatar(role.role_avatar)
=======
    avatar = str_util.handle_spoiler_avatar(role.role_avatar) #if spoiler_avatar else str_util.format_avatar(role.role_avatar)
>>>>>>> main
    if refresh:
        await bot.edit_message_media(chat_id=message.chat.id, 
                                     message_id=message.message_id,
                                     media=types.InputMediaPhoto(media=avatar, caption=content),
                                     reply_markup=builder.as_markup())
    else:
        await bot.send_photo(message.chat.id, photo=avatar, 
                             caption=content, reply_markup=builder.as_markup())
    await state.set_state(None)


@router.message(Command(BotCommand(command='new_roles', description='角色上新')))
async def handle_new_roles(message: types.Message, command: CommandObject,
                 bot: Bot, state: FSMContext):
    await send_role_card(message, bot, state, 1, tag=RoleTag.NEW.value)

@router.callback_query(NewRoleSlideCallback.filter())
async def handle_new_role_page(query: CallbackQuery, callback_data: NewRoleSlideCallback, bot: Bot, state: FSMContext):
    await send_role_card(query.message, bot, state, callback_data.page, True, callback_data.tag)

# 聊天返钻活动
@router.callback_query(ActivityDiamondSeasonSlideCallback.filter())
async def handle_activity_diamond_season_page(query: CallbackQuery, callback_data: ActivityDiamondSeasonSlideCallback, bot: Bot, state: FSMContext):
    await send_activity_role_card(query.message, bot, state, callback_data.page, callback_data.role_ids, True,)

@router.message(Command(BotCommand(command='role_bot', description='角色橱窗')))
async def command_role_bot(message: Message, command: CommandObject) -> None:
    await message.answer('打开角色橱窗，一起浏览和讨论所有角色', parse_mode=ParseMode.HTML,
                        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                            [InlineKeyboardButton(text="打开橱窗", url=user_growth_constants.ROLE_CHANNEL_LINK)]]))

async def get_role_list(message: Message, bot: Bot, state: FSMContext):
    return await send_role_card(message, bot, state, 1)
