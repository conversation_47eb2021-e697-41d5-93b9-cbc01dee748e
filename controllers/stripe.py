import logging
import os, json
from fastapi.responses import HTMLResponse, JSONResponse
import stripe
from fastapi import APIRouter, Request
from services.bot_services import get_bot_by_bot_id
from services.recharge_service import pay_success 
from services.user_service import user_service
from services import user_growth_service, re_purchase_service
from . import html_template

stripe_router = APIRouter()

log = logging.getLogger(__name__)

html_file = os.path.join(os.path.dirname(__file__), 'success.html')

with open(html_file) as f:
    success_html = f.read()


@stripe_router.get('/stripe/success')
async def stripe_success(request: Request):
    return HTMLResponse(success_html)


@stripe_router.get('/stripe/canceled')
async def stripe_canceled(request: Request):
    return HTMLResponse(html_template.canceled_template)

# 提供给stripe server的回调接口
@stripe_router.post("/stripe/webhook")
async def stripe_webhook(request: Request):
    webhook_secret = os.environ.get("STRIPE_WEBHOOK_SECRET")
    body = await request.body()
    sign_header = request.headers["stripe-signature"]
    log.info(f'body: {body}, sign_header: {sign_header}')
    try:
        # json_event = json.loads(body)
        event = stripe.Webhook.construct_event(
            payload=body,
            sig_header=sign_header,
            secret=webhook_secret,
        )
        if event.type == "checkout.session.completed":
            session = event["data"]["object"]
            order = await pay_success(
                session.id, session.created, json.dumps(session)
            )
            user = await user_service.get_user_by_id(order.user_id)
            await user_growth_service.add_fc_reward(user)
            await re_purchase_service.after_recharge(user, order)
            if order.from_bot_id != 0:
                bot = get_bot_by_bot_id(order.from_bot_id)
                if bot:
                    tg_user = await user_service.get_tg_info_by_user_id(order.user_id)
                    if tg_user:
                        content = f'充值成功 {order.amount} 💎 + 🟡'
                        try:
                            await bot.send_message(tg_user.tg_id, content)
                        except Exception as e:
                            logging.warning(f'chat id {tg_user.tg_id} send message failed: {e}')

    except Exception as e:
        log.exception(f"Failed to handle stripe webhook: {e}")
        return JSONResponse(status_code=400, content=None)
    return JSONResponse(status_code=200, content=None)
