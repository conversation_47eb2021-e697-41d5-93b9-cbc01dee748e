import logging
import math
import os
from datetime import datetime, UTC, timedelta, timezone
import os
import mysql.connector
from common.common_constant import RoleSortType
from common.role_model import RoleFilterRequest
from persistence.models.models import (
    RoleRankStat,
)
from services import role_statistic_service
from services.role import role_loader_service
from utils import date_util, utils

log = logging.getLogger(__name__)



async def init_run_week_and_run_month():
    await run_day()
    today = date_util.now(8)
    if today.day == 1:
        await run_month()
    if today.weekday() == 0:
        await run_week()


async def run_day():
    start_index = int(date_util.now(8).strftime("%Y%m%d"))
    end_index = int(date_util.now(8).strftime("%Y%m%d"))
    # 查询并汇总
    day_hot = {}
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        sql = f"""SELECT 
            role_id,
            sum(total_message) + sum(total_diamond) as hot
            from role_daily_statistic
            WHERE date_index >= {start_index} and date_index <= {end_index} group by role_id
        """
        log.info(f"role_rank_day: start_index: {start_index}, end_index: {end_index}")
        cursor.execute(sql)
        mid_list = cursor.fetchall()
        for x in mid_list:
            day_hot[int(x[0])] = int(x[1])  # type: ignore
        cursor.close()
    role_ids = list(day_hot.keys())
    role_ids.sort(key=lambda x: day_hot[x], reverse=True)
    refresh_date = date_util.now(8)
    date_index = int(refresh_date.strftime("%Y%m%d"))
    await refresh_or_new_rank(role_ids, RoleSortType.DAILY,date_index=date_index)


async def run_week():
    # 获取上周周一时间
    end_day = date_util.now(8) - timedelta(days=date_util.now(8).weekday())
    start_day = end_day - timedelta(days=6)
    start_index = int(start_day.strftime("%Y%m%d"))
    end_index = int(end_day.strftime("%Y%m%d"))

    # 查询并汇总
    week_hot = {}
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        sql = f"""SELECT 
            role_id,
            sum(total_message) + sum(total_diamond) as hot
            from role_daily_statistic
            WHERE date_index >= {start_index} and date_index <= {end_index} group by role_id
        """
        log.info(f"run_rank_week: start_index {start_index}, end_index {end_index}")
        cursor.execute(sql)
        mid_list = cursor.fetchall()
        for x in mid_list:
            week_hot[int(x[0])] = int(x[1])  # type: ignore
        cursor.close()
    role_ids = list(week_hot.keys())
    role_ids.sort(key=lambda x: week_hot[x], reverse=True)
    refresh_date = date_util.week_start(tz_offset=8,weeks=-1)
    date_index = int(refresh_date.strftime("%Y%m%d"))
    await refresh_or_new_rank(role_ids, RoleSortType.WEEKLY,date_index=date_index)


async def run_month():
    # 获取上个月时间
    end_day = date_util.now(8) - timedelta(days=date_util.now(8).day)
    start_day = date_util.month_start(tz_offset=8, months=-1)
    start_index = int(start_day.strftime("%Y%m%d"))
    end_index = int(end_day.strftime("%Y%m%d"))

    # 查询并汇总
    month_hot = {}
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        sql = f"""SELECT 
            role_id,
            sum(total_message) + sum(total_diamond) as hot
            from role_daily_statistic
            WHERE date_index >= {start_index} and date_index <= {end_index} group by role_id
        """
        log.info(f"run_rank_month: start_index {start_index}, end_index {end_index}")
        cursor.execute(sql)
        mid_list = cursor.fetchall()
        for x in mid_list:
            month_hot[int(x[0])] = int(x[1])  # type: ignore
        cursor.close()
    role_ids = list(month_hot.keys())
    role_ids.sort(key=lambda x: month_hot[x], reverse=True)
    refresh_date = date_util.month_start(tz_offset=8,months=-1)
    date_index = int(refresh_date.strftime("%Y%m%d"))
    await refresh_or_new_rank(role_ids, RoleSortType.MONTHLY,date_index=date_index)


async def refresh_or_new_rank(role_ids: list[int], sort_type: RoleSortType,date_index: int = 0):
    new_role_ids = await sort_rank(role_ids, sort_type)
    await RoleRankStat.update_or_create(
        sort_type=sort_type.value,
        date_index=date_index,
        defaults={"role_ids": new_role_ids},
    )


async def sort_rank(role_ids: list[int], sort_type: RoleSortType):
    role_uid_map = await role_loader_service.map_id2uid_by_user_filter(
        RoleFilterRequest()
    )
    role_ids = [x for x in role_ids if x in role_uid_map]
    role_update_map = await role_statistic_service.role_update_time()
    utc8_start = date_util.utc8_day_start()

    start_time = utc8_start - timedelta(days=1)
    end_time = utc8_start - timedelta(seconds=1)
    if sort_type == RoleSortType.WEEKLY:
        start_time = date_util.week_start(tz_offset=8, weeks=-1)
        end_time = date_util.week_end(tz_offset=8, weeks=-1)
    elif sort_type == RoleSortType.MONTHLY:
        start_time = date_util.month_start(tz_offset=8, months=-1)
        end_time = date_util.month_end(tz_offset=8, months=-1)
    start_time = date_util.utc8_to_utc(start_time)
    end_time = date_util.utc8_to_utc(end_time)
    update_role_ids = [
        role_id
        for role_id, update_time in role_update_map.items()
        if start_time.timestamp() <= update_time <= end_time.timestamp()
        and role_id in role_ids
    ]
    # 更新角色排在最前面
    ret = [x for x in role_ids if x in update_role_ids]
    role_ids = [x for x in role_ids if x not in update_role_ids]
    role_ids = ret + role_ids

    # role_ids重新排序，保证同一个uid，不同的role_id位置不能小于10
    role_id_index = {}
    for index, role_id in enumerate(role_ids):
        role_id_index[role_id] = index
    uid_index = {}
    for index, role_id in enumerate(role_ids):
        if index >= 100:
            break
        uid = role_uid_map[role_id]
        if uid == 0:
            continue
        if uid in uid_index and index < uid_index[uid] + 10:
            new_index = uid_index[uid] + 10
            role_id_index[role_id] = new_index
            uid_index[uid] = new_index
            continue
        uid_index[uid] = index

    role_ids.sort(key=lambda x: role_id_index[x])
    return role_ids
