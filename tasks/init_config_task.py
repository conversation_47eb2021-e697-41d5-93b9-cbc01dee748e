from datetime import datetime, timedelta
import json
import logging
import os

from aiogram import Bo<PERSON>
from ai import lite_llm_bot
from common.bot_common import MessageTemplate
from common.common_constant import (
    <PERSON>tCategory,
    BotReplace,
    BotType,
    ChannelCategory,
    ChatModeType,
    Env,
    GroupCategory,
    Language,
    LiteLlmErrorType,
    ProductType,
    RoleTag,
    TgGroupChatId,
    UserModelFilter,
)
from aiogram import Bot, types
from aiogram.enums import ChatMemberStatus
from common.models.chat_model import ModelLlmRequestStat, ModelStatus
from common.role_model import CardDetail
from persistence.models.models import (
    LlmModelStatus,
    ModelWaterConfig,
    Product,
    RoleOrder,
    SubTag,
    TgBotConfig,
    TgChannelConfig,
    TgGroupConfig,
)
from properties import prop_util
from services import (
    bot_message_service,
    config_service,
    model_service,
    product_service,
    role_config_service,
    role_statistic_service,
    tag_service,
    tg_config_service,
)
from services.chat import chat_result_service
from services.role import role_group_service, role_loader_service
from utils import cos_util, date_util, env_util, json_util, request_util, tg_util
from utils import exception_util
from utils.exception_util import (
    ParamException,
    async_catch_exception,
    async_ignore_catch_exception,
)

log = logging.getLogger(__name__)


async def init_start():
    await refresh_sub_tag_role_count()
    return True


async def refresh_sub_tag_role_count():
    db_tag_list = await tag_service.list_all_sub_tags_with_deleted()
    db_role_configs = await role_loader_service.list_public_with_fields(
        ["id", "sub_tags"]
    )
    for sub_tag in db_tag_list:
        count = len(
            [
                x
                for x in db_role_configs
                if x.sub_tags and sub_tag.tag_name in x.sub_tags
            ]
        )
        if count == sub_tag.role_count:
            continue
        await SubTag.filter(id=sub_tag.id).update(
            role_count=count,
        )
        log.info(
            "refresh_sub_tag_role_count sub_tag:%s count:%s", sub_tag.tag_name, count
        )


async def init_role_orders(temp: bool = False):
    role_list = await role_loader_service.list_public_with_fields(
        ["id", "support_photo", "created_at"]
    )
    group_list = await role_group_service.list_public_config()
    support_photo_card_ids = [
        CardDetail.key_role(x.id) for x in role_list if x.support_photo
    ]
    all_card_ids = [CardDetail.key_role(x.id) for x in role_list] + [
        CardDetail.key_group(x.id) for x in group_list
    ]
    card_hk_count = await role_statistic_service.card_h24_hk_count()
    all_card_ids.sort(key=lambda x: card_hk_count.get(x, 0), reverse=True)
    photo_card_ids = [x for x in all_card_ids if x in support_photo_card_ids]
    all_card_ids = [x for x in all_card_ids if x not in photo_card_ids]

    # hot前8插入到25之后
    card_hot_count = await role_statistic_service.card_hot_count()
    hot_card_ids = sorted(
        card_hot_count, key=lambda x: card_hot_count[x], reverse=True
    )[:8]
    log.info(f"hot_card_ids:{hot_card_ids}")
    mid_card_ids = []
    card_len = len(all_card_ids)
    i = 0
    while i < card_len:
        if all_card_ids[i] in hot_card_ids and i < 25:
            all_card_ids.insert(i + 25, all_card_ids[i])
            i += 1
            card_len += 1
            continue
        mid_card_ids.append(all_card_ids[i])
        i += 1
    all_card_ids = mid_card_ids

    def in_day(created_at: datetime, days: int):
        created_at = date_util.utc2utc8(created_at)
        start = date_util.utc8_day_start(days=days, hours=4)
        end = date_util.utc8_day_start(days=days + 1, hours=4)
        return start <= created_at < end

    # 创建时间在UTC+8的前一日中午12点，到当日中午12点的卡，如果排在前10，则排名+10
    pre_ct_card_ids = [
        CardDetail.key_role(x.id) for x in role_list if in_day(x.created_at, -1)
    ]
    pre_ct_card_ids.extend(
        [CardDetail.key_group(x.id) for x in group_list if in_day(x.created_at, -1)]
    )
    log.info(f"pre_ct_card_ids:{pre_ct_card_ids}")
    mid_card_ids = []
    card_len = len(all_card_ids)
    i = 0
    while i < card_len:
        if all_card_ids[i] in pre_ct_card_ids and i < 10:
            all_card_ids.insert(i + 10, all_card_ids[i])
            i += 1
            card_len += 1
            continue
        mid_card_ids.append(all_card_ids[i])
        i += 1
    all_card_ids = mid_card_ids

    # 上新中创建时间在UTC+8的当日中午12点以后新角色卡，排50以后
    today_ct_card_ids = [
        CardDetail.key_role(x.id) for x in role_list if in_day(x.created_at, 0)
    ]
    today_ct_card_ids.extend(
        [CardDetail.key_group(x.id) for x in group_list if in_day(x.created_at, 0)]
    )
    log.info(f"today_ct_card_ids:{today_ct_card_ids}")
    mid_card_ids = []
    card_len = len(all_card_ids)
    i = 0
    while i < card_len:
        if all_card_ids[i] in today_ct_card_ids and i < 50:
            all_card_ids.insert(i + 50, all_card_ids[i])
            i += 1
            card_len += 1
            continue
        mid_card_ids.append(all_card_ids[i])
        i += 1
    all_card_ids = mid_card_ids

    # 照片卡插入到指定位置
    log.info(f"photo_card_ids:{photo_card_ids}")
    mid_card_ids = []
    for i in range(0, len(all_card_ids)):
        if len(photo_card_ids) >= 1 and i == 0:
            mid_card_ids.append(photo_card_ids[0])
        if len(photo_card_ids) >= 2 and i == 2:
            mid_card_ids.append(photo_card_ids[1])
        if len(photo_card_ids) >= 3 and i == 5:
            mid_card_ids.append(photo_card_ids[2])
        if len(photo_card_ids) >= 4 and i == 9:
            mid_card_ids.append(photo_card_ids[3])
        if (i - 9) % 4 == 0 and (i - 9) / 4 + 3 < len(photo_card_ids):
            mid_card_ids.append(photo_card_ids[int((i - 9) / 4 + 3)])
        mid_card_ids.append(all_card_ids[i])
    all_card_ids = mid_card_ids

    for position, x in enumerate(all_card_ids):
        mode_type = CardDetail.key_type(x)
        mode_target_id = CardDetail.key_target(x)
        role_order = await RoleOrder.filter(
            mode_type=mode_type, mode_target_id=mode_target_id
        ).first()
        if not role_order:
            role_order = RoleOrder(
                mode_type=mode_type,
                mode_target_id=mode_target_id,
                position=position,
                position_temp=position,
            )
        role_order.position_temp = position
        if not temp:
            role_order.position = position
        await role_order.save()
    return all_card_ids


async def check_bot_config():
    error_list = []
    bots = await tg_config_service.list_bot()
    for bot in bots:
        if not await tg_config_service.get_me(bot.bot_id):
            error_list.append(f"bot {bot.bot_id} tg request error")
    # helper bot check
    helper_bots = await tg_config_service.list_bot(BotCategory.HELPER)
    if not [x.main_bot for x in helper_bots]:
        error_list.append("helper bot 主bot与可投放bot缺失")
    if len(helper_bots) < 2:
        error_list.append("helper bot 少于2个（却少备用bot）")

    # TMA bot check
    tma_bots = await tg_config_service.list_bot(BotCategory.TMA)
    if not [x.main_bot for x in tma_bots]:
        error_list.append("tma bot 主bot缺失")
    if not [x.referenceable for x in tma_bots]:
        error_list.append("tma bot 可投放bot缺失")
    if len(tma_bots) < 2:
        error_list.append("tma bot 少于2个（却少备用bot）")

    # chat bot check
    chat_bots = await tg_config_service.list_bot(BotCategory.CHAT_BOT)
    if not [x.main_bot for x in chat_bots]:
        error_list.append("chat bot 主bot缺失")
    if not [x.referenceable for x in chat_bots]:
        error_list.append("chat bot 可投放bot缺失")
    if len(chat_bots) < 2:
        error_list.append("chat bot 少于2个(却少备用bot)")

    if error_list:
        log.error(f"check_bot_config error:{"\n".join(error_list)}")
        tg_util.send_message({"BotCheck:\n": "\n".join(error_list)})
    else:
        tg_util.send_message({"BotCheck:": "success"})
    return error_list


async def check_channel_config():
    error_list = []
    channels = await tg_config_service.list_channel()
    for channel in channels:
        if not channel.helper_bot_ids:
            error_list.append(f"channel {channel.username} helper bot缺失")
            continue
        if not await tg_config_service.chat_info(
            channel.helper_bot_ids[0], channel.chat_id
        ):
            error_list.append(f"channel {channel.username} 查询信息失败")
            continue
    channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
    if not [x.main for x in channels]:
        error_list.append("主角色频道缺失")
    if len([x for x in channels if x.referenceable]) < 2:
        error_list.append("角色频道少于2个(却少备用频道)")
    channels = await tg_config_service.list_channel(category=ChannelCategory.WELFARE)
    if not [x.main for x in channels]:
        error_list.append("主福利频道缺失")
    if len([x for x in channels if x.referenceable]) < 2:
        error_list.append("福利频道少于2个(却少备用频道)")

    if error_list:
        tg_util.send_message({"ChannelCheck:\n": "\n".join(error_list)})
    return error_list


async def check_group_config():
    error_list = []
    groups = await tg_config_service.list_group()
    for group in groups:
        if not group.helper_bot_ids:
            error_list.append(f"group {group.username} helper bot缺失")
            continue
        if not await tg_config_service.chat_info(
            group.helper_bot_ids[0], group.chat_id
        ):
            error_list.append(f"group {group.username} 查询信息失败")
            continue
    if len([x for x in groups if x.referenceable]) < 2:
        error_list.append("主群组少于2个(却少备用群组)")
    if error_list:
        tg_util.send_message({"GroupCheck:\n": "\n".join(error_list)})
    return error_list


async def notify_default_template():
    bot_replaces = [x for x in BotReplace]
    tips = "\n".join([f"{x.name} {x.value}" for x in bot_replaces])
    template = MessageTemplate(tips=tips)
    template = await bot_message_service.format_template_replace(template)
    tg_util.send_message({"template": template.tips})


async def add_channel(username: str, channel_category: ChannelCategory):
    tg_channel = await TgChannelConfig.filter(username=username).first()
    bots = await tg_config_service.list_bot(BotCategory.HELPER)
    helper_bot_ids = [bot.bot_id for bot in bots]
    if tg_channel and tg_channel.helper_bot_ids:
        helper_bot_ids = (
            json_util.convert_to_list(tg_channel.helper_bot_ids) + helper_bot_ids
        )
    valid_bot = None
    full_info = None
    admin_list = []
    for bot in bots:
        try:
            sender_bot = await tg_config_service.get_sender_bot_by_id(bot.bot_id)
            if not sender_bot:
                continue
            full_info = await sender_bot.get_chat("@" + username)
            admin_list = await sender_bot.get_chat_administrators("@" + username)
            valid_bot = sender_bot
        except Exception as e:
            pass
    if not full_info or not valid_bot:
        raise exception_util.param_error("请求TG频道信息失败")
    db_bot_ids = [
        admin.user.id for admin in admin_list if admin.user.id in helper_bot_ids
    ]
    if not db_bot_ids:
        raise exception_util.param_error("渠道却少helper bot")

    if not tg_channel:
        tg_channel = await TgChannelConfig.create(
            category=channel_category.value,
            username=username,
            chat_id=full_info.id,
            title=str(full_info.title),
            description=str(full_info.description),
            url=f"https://t.me/{full_info.username}",
            init=True,
            helper_bot_ids=[
                admin.user.id for admin in admin_list if admin.user.id in helper_bot_ids
            ],
        )
        return tg_channel
    tg_channel.category = channel_category.value
    tg_channel.chat_id = full_info.id
    tg_channel.title = str(full_info.title)
    tg_channel.description = str(full_info.description)
    tg_channel.url = f"https://t.me/{full_info.username}"
    tg_channel.init = True
    tg_channel.helper_bot_ids = db_bot_ids
    await tg_channel.save()
    return tg_channel


async def add_group(username: str, group_category: GroupCategory, main: bool = False):
    tg_group = await TgGroupConfig.filter(username=username).first()
    helper_bots = await tg_config_service.list_bot(BotCategory.HELPER)
    helper_bot_ids = []
    full_info = None
    for bot in helper_bots:
        try:
            sender_bot = await tg_config_service.get_sender_bot_by_id(bot.bot_id)
            if not sender_bot:
                continue
            if not full_info:
                full_info = await sender_bot.get_chat("@" + username)
            if not full_info:
                continue
            member = await sender_bot.get_chat_member(
                chat_id=full_info.id, user_id=bot.bot_id
            )
            if member.status in [
                ChatMemberStatus.MEMBER,
                ChatMemberStatus.ADMINISTRATOR,
            ]:
                helper_bot_ids.append(bot.bot_id)
        except Exception as e:
            pass
    if not full_info:
        raise exception_util.param_error("请求TG群组信息失败")
    helper_bot_ids = helper_bot_ids

    if not tg_group:
        tg_group = await TgGroupConfig.create(
            username=username,
            chat_id=full_info.id,
            title=str(full_info.title),
            description=str(full_info.description),
            url=f"https://t.me/{full_info.username}",
            init=True,
            category=group_category.value,
            helper_bot_ids=helper_bot_ids,
            main=main,
        )
        return tg_group
    tg_group.chat_id = full_info.id
    tg_group.title = str(full_info.title)
    tg_group.description = str(full_info.description)
    tg_group.url = f"https://t.me/{full_info.username}"
    tg_group.init = True
    tg_group.helper_bot_ids = helper_bot_ids
    tg_group.category = group_category.value
    tg_group.main = main
    await tg_group.save()
    return tg_group


async def add_bot(token: str, bot_category: BotCategory):
    tg_bot = await TgBotConfig.filter(token=token).first()
    sender_bot = tg_config_service.builder_sender_bot_by_token(token)
    user = await sender_bot.get_me()
    description = await sender_bot.get_my_description()
    if not tg_bot:
        tg_bot = await TgBotConfig.create(
            category=bot_category.value,
            token=token,
            bot_id=user.id,
            first_name=user.first_name,
            username=user.username,
            url=f"https://t.me/{user.username}",
            init=True,
            description=description.description,
        )
        return tg_bot
    tg_bot.category = bot_category.value
    tg_bot.bot_id = user.id
    tg_bot.first_name = user.first_name
    tg_bot.username = str(user.username)
    tg_bot.url = f"https://t.me/{user.username}"
    tg_bot.init = True
    tg_bot.description = description.description
    await tg_bot.save()
    return tg_bot


async def init_tg_config():

    # 增加一个callback方法，异步执行await sender_bot.get_me()，捕获异常，如果异常则不更新

    tg_bot_configs = await TgBotConfig.filter(init=0, status=1).all()
    tg_bot_configs = [x for x in tg_bot_configs if x.token]
    for tg_bot_config in tg_bot_configs:
        bot_full_info = await tg_config_service.get_me_by_token(tg_bot_config.token)
        if not bot_full_info:
            continue
        tg_bot_config.bot_id = int(bot_full_info.id)
        tg_bot_config.first_name = bot_full_info.first_name
        tg_bot_config.username = str(bot_full_info.username)
        tg_bot_config.url = f"https://t.me/{bot_full_info.username}"
        tg_bot_config.init = True
        await tg_bot_config.save()

    groups = await TgGroupConfig.filter(init=0, status=1).all()
    for group in groups:
        sender_bot = await tg_config_service.get_main_sender_bot_by_category(
            BotCategory.HELPER
        )
        full_info = await sender_bot.get_chat("@" + group.username)
        if not full_info:
            continue
        group.chat_id = full_info.id
        group.title = str(full_info.title)
        group.description = str(full_info.description)
        group.username = str(full_info.username)
        group.url = f"https://t.me/{full_info.username}"
        group.init = True
        await group.save()

    channels = await TgChannelConfig.filter(init=0, status=1).all()
    for channel in channels:
        sender_bot = await tg_config_service.get_main_sender_bot_by_category(
            BotCategory.HELPER
        )
        full_info = await sender_bot.get_chat("@" + channel.username)
        if not full_info:
            continue
        channel.chat_id = full_info.id
        channel.title = str(full_info.title)
        channel.description = str(full_info.description)
        channel.username = str(full_info.username)
        channel.url = f"https://t.me/{full_info.username}"
        channel.init = True
        await channel.save()
    return True


# async def refresh_model_stat():
#     now_time = date_util.utc_now() - timedelta(minutes=1)
#     min_timedelta = now_time.minute % 5
#     end_time = now_time - timedelta(minutes=min_timedelta + 1)
#     start_time = end_time - timedelta(minutes=5)
#     # 复制end_time
#     stat_time = now_time
#     stat_time = stat_time.replace(
#         minute=int(now_time.minute / 5) * 5, second=0, microsecond=0
#     )
#     map_stat = await model_service.map_model_request_by_time_range(
#         start_time=start_time, end_time=end_time
#     )
#     call_phone = False
#     for model, stat in map_stat.items():
#         try:
#             await LlmModelStatus.create(
#                 llm_model=model,
#                 status=stat.format_status().value,
#                 success_count=stat.success_count,
#                 fail_count=stat.fail_count,
#                 total_count=stat.total_count,
#                 stat_at=stat_time,
#             )
#         except Exception as e:
#             pass
#         model_status = stat.format_status()
#         if model_status != ModelStatus.AVAILABLE and stat.fail_count >= 10:
#             # 可用性小0.75电话报警，并且切流量
#             update_water = model_status in [
#                 ModelStatus.UNAVAILABLE,
#                 ModelStatus.CONGESTION,
#             ]
#             await tg_util.send_monitor(
#                 data={
#                     "title": "Many Fail Model Request",
#                     "llm_model": model,
#                     "count": f"fail_count:{stat.fail_count}, total_count:{stat.total_count},rate:{stat.fail_count/stat.total_count*100}%",
#                     "time": date_util.now(8),
#                     "update_water": update_water,
#                     "status": model_status.value,
#                 }
#             )
#             if update_water:
#                 await config_service.monitor_water(model)
#                 call_phone = True


#     # 删除LlmModelStatus24小时之前的数据
#     delete_time = now_time - timedelta(hours=24)
#     await LlmModelStatus.filter(stat_at__lt=delete_time).delete()
#     if call_phone:
#         await request_util.get_text(
#             url="https://fwalert.com/550449de-88b3-4d6f-9503-e196e0644315"
#         )
#     return True
# 自动恢复

# @async_catch_exception
# async def recovery_model_water_config(model_stat: dict[str, ModelLlmRequestStat]):
#     water_configs = await config_service.list_model_water_config()
#     products = await product_service.list_original_products(ProductType.CHAT)
#     for product in products:
#         pw_configs = [
#             x for x in water_configs if x.llm_model == product.model and x.num > 0
#         ]
#         before_to_llm_models = [
#             f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
#         ]
#         update_water = False
#         error_to_llm_models = [
#             x.to_llm_model
#             for x in pw_configs
#             if x.to_llm_model in model_stat
#             and model_stat.get(x.to_llm_model).total_count > 0
#             and model_stat.get(x.to_llm_model).format_status()
#             in [ModelStatus.UNAVAILABLE, ModelStatus.CONGESTION]
#             and x.request_status != ModelStatus.AVAILABLE.value
#         ]

#         # 没有错误，恢复所有的掺水
#         if not error_to_llm_models:
#             warn_configs = [
#                 x for x in pw_configs if x.num != x.original_num and x.num > 0
#             ]
#             for config in warn_configs:
#                 config.num = config.original_num
#                 config.request_status = ModelStatus.AVAILABLE.value
#                 await config.save()
#                 update_water = True
#         valid_configs = [
#             x
#             for x in pw_configs
#             if x.to_llm_model not in error_to_llm_models and x.num > 0
#         ]

#         # 如果没有可用的模型，发送报警
#         if not valid_configs:
#             tg_util.send_message(
#                 {
#                     "title": "All Model UnAvailable",
#                     "product_name": product.short_name,
#                     "llm_model": product.model,
#                     "to_llm_models": ", ".join(before_to_llm_models),
#                     "time": date_util.now(8),
#                 }
#             )
#             continue

#         # 错误模型降级，摘除
#         error_configs = [
#             x for x in pw_configs if x.to_llm_model in error_to_llm_models and x.num > 0
#         ]
#         for error_config in error_configs:
#             error_config.num = 0
#             error_config.original_num = 0
#             error_config.request_status = ModelStatus.UNAVAILABLE.value
#             await error_config.save()
#             update_water = True
#         # 报警
#         if update_water:
#             pw_configs = await config_service.list_model_water_config()
#             pw_configs = [x for x in pw_configs if x.llm_model == product.model]
#             after_to_llm_models = [
#                 f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
#             ]
#             await tg_util.send_monitor(
#                 data={
#                     "title": "Auto Switch Recovery Water",
#                     "product_name": product.short_name,
#                     "llm_model": product.model,
#                     "before_to_llm_models": ", ".join(before_to_llm_models),
#                     "after_to_llm_models": ", ".join(after_to_llm_models),
#                     "remove_to_llm_models": ", ".join(error_to_llm_models),
#                     "time": date_util.now(8),
#                 }
#             )
#             continue

#     return True


async def call_phone():
    await request_util.get_text(
        url="https://fwalert.com/550449de-88b3-4d6f-9503-e196e0644315"
    )


# 错误模型降级：两个模型自动降级，3个以上模型直接下掉
@async_catch_exception
async def refresh_water_config_new(monitor_models: list[ModelLlmRequestStat]):
    water_configs = await config_service.list_model_water_config()
    model_stat_map = {x.model: x for x in monitor_models}
    products = await product_service.list_original_products(ProductType.CHAT)
    for product in products:
        pw_configs = [
            x for x in water_configs if x.llm_model == product.model and x.num > 0
        ]
        if not pw_configs:
            continue
        before_to_llm_models = [
            f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
        ]
        valid_configs = [x for x in pw_configs if x.to_llm_model not in model_stat_map]
        error_configs = [x for x in pw_configs if x.to_llm_model in model_stat_map]
        if len(error_configs) == 0:
            continue
        if len(valid_configs) == 0:
            error_to_llm_models = [x.to_llm_model for x in error_configs]
            await tg_util.send_monitor(
                data={
                    "title": "所有模型报错（严重）",
                    "模型产品": product.short_name,
                    "降级前模型列表": ", ".join(before_to_llm_models),
                    "降级模型列表": ", ".join(error_to_llm_models),
                    "时间": date_util.now(8),
                }
            )
            continue

        for error_config in error_configs:
            error_config.num = 0
            error_config.request_status = ModelStatus.CONGESTION.value
            error_config.error_time = int(date_util.utc_now().timestamp())
            await error_config.save()
            await send_model_downgrade(error_config)


@async_catch_exception
async def send_monitor(monitor_models: list[ModelLlmRequestStat]):
    log.info(f"send_monitor: {monitor_models}")
    water_configs = await config_service.list_model_water_config()
    products = await product_service.list_original_products(ProductType.CHAT)
    map_model_stat = {x.model: x for x in monitor_models}
    for water_config in water_configs:
        model_stat = map_model_stat.get(water_config.to_llm_model)
        if not model_stat:
            continue
        await send_model_monitor(water_config, model_stat)

    call_phone = False
    for product in products:
        pw_configs = [
            x for x in water_configs if x.llm_model == product.model and x.num > 0
        ]
        valid_to_llm_models = [
            x.to_llm_model for x in pw_configs if x.to_llm_model not in monitor_models
        ]
        if not valid_to_llm_models:
            call_phone = True

    if call_phone and env_util.get_current_env() == Env.PROD:
        await request_util.get_text(
            url="https://fwalert.com/550449de-88b3-4d6f-9503-e196e0644315"
        )


async def refresh_model_stat():

    now_time = date_util.utc_now() - timedelta(minutes=1)
    min_timedelta = now_time.minute % 5
    end_time = now_time - timedelta(minutes=min_timedelta + 1)
    start_time = end_time - timedelta(minutes=4)
    # 复制end_time
    stat_time = now_time
    stat_time = stat_time.replace(
        minute=int(now_time.minute / 5) * 5, second=0, microsecond=0
    )
    map_stat = await model_service.map_model_request_by_time_range(
        start_time=start_time, end_time=end_time
    )
    # 恢复模型流量配置
    # await recovery_model_water_config(map_stat)
    monitor_count = 10 if env_util.get_current_env() == Env.PROD else 3
    monitor_models = []
    for model, stat in map_stat.items():
        try:
            await LlmModelStatus.create(
                llm_model=model,
                status=stat.format_status().value,
                success_count=stat.success_count,
                fail_count=stat.fail_count,
                total_count=stat.total_count,
                stat_at=stat_time,
            )
        except Exception as e:
            pass
        model_status = stat.format_status()
        if model_status != ModelStatus.AVAILABLE and stat.fail_count >= monitor_count:
            # 可用性小0.75电话报警，并且切流量
            if model_status in [
                ModelStatus.UNAVAILABLE,
                ModelStatus.CONGESTION,
            ]:
                monitor_models.append(stat)
                log.error(
                    f"monitor model:{model} status:{model_status.value} fail_count:{stat.fail_count} total_count:{stat.total_count}"
                )

    if monitor_models:
        await send_monitor(monitor_models)
        await refresh_water_config_new(monitor_models)

    delete_time = now_time - timedelta(hours=24)
    await LlmModelStatus.filter(stat_at__lt=delete_time).delete()

    llm_model_configs = await config_service.list_llm_model_config()
    check_llm_models = [
        x.llm_model for x in llm_model_configs if x.enabled and x.check_llm_request
    ]
    water_configs = await config_service.list_model_water_config()
    products = await product_service.list_original_products(ProductType.CHAT)
    now_time = date_util.utc_now()
    time_diff = (
        timedelta(minutes=3)
        if env_util.get_current_env() == Env.PROD
        else timedelta(minutes=1)
    )
    diff_time_long = (now_time - time_diff).timestamp()
    for product in products:
        wc_list = [x for x in water_configs if x.llm_model == product.model]
        if not wc_list:
            continue

        def check_wc(wc: ModelWaterConfig):
            return (
                wc.num != wc.original_num
                and wc.num == 0
                and wc.updated_at.timestamp() <= diff_time_long
                and wc.to_llm_model in check_llm_models
            )

        need_check_wc_list = [x for x in wc_list if check_wc(x)]
        if not need_check_wc_list:
            continue
        for wc in need_check_wc_list:
            llm_model_config = await config_service.get_llm_model_config_by_model(
                wc.to_llm_model
            )
            if not llm_model_config:
                continue
            base_url = config_service.get_lite_llm_base_url(
                llm_model_config.request_cluster
            )
            check_ret = await lite_llm_bot.check_models_success(
                llm_model_config.request_llm_model, base_url=base_url
            )
            if check_ret:
                wc.num = wc.original_num
                wc.request_status = ModelStatus.AVAILABLE.value
                await wc.save()
                await recovery_model_monitor(wc)
                continue
            # 如果模型不在有效模型列表中，未恢复报警
            await timeout_model_monitor(wc)
            if now_time.timestamp() - wc.updated_at.timestamp() > 3600 * 12:
                wc.num = 0
                wc.original_num = 0
                wc.request_status = ModelStatus.UNAVAILABLE.value
                await wc.save()
                await down_model_monitor(wc)
    return True


async def monitor_chat_id(water: ModelWaterConfig):
    if not water.use_filter or UserModelFilter.DEFAULT.value == water.use_filter:
        return TgGroupChatId.PAID_MONITOR.value
    if water.use_filter and UserModelFilter.FREE_BENEFIT.value == water.use_filter:
        return TgGroupChatId.FREE_MONITOR.value
    return TgGroupChatId.PAID_MONITOR.value


# 模型降级
async def send_model_downgrade(water: ModelWaterConfig):
    product = await product_service.get_by_chat(ProductType.CHAT.value, water.llm_model)
    if not product:
        return
    pw_configs = await config_service.list_water_config_by_model_and_use_filter(
        water.llm_model, water.use_filter
    )
    pw_configs = [x for x in pw_configs if x.llm_model == product.model]
    after_to_llm_models = [
        f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
    ]
    chat_id = await monitor_chat_id(water)
    data = {
        "title": "模型有报错，已降级比例",
        "模型产品": product.short_name,
        "降级后模型列表": ", ".join(after_to_llm_models),
        "降级模型": water.to_llm_model,
        "时间": date_util.now(8),
    }
    await tg_util.sm(data, chat_id)
    return True


async def send_model_monitor(
    water: ModelWaterConfig,
    stat: ModelLlmRequestStat,
):
    product = await product_service.get_by_chat(ProductType.CHAT.value, water.llm_model)
    if not product:
        return
    chat_id = await monitor_chat_id(water)
    free_desc = (
        "免费模型" if chat_id == TgGroupChatId.FREE_MONITOR.value else "付费模型"
    )
    record = await chat_result_service.get_monitor_error_record(water.to_llm_model)
    data = {
        "报错标题": f"模型报错（{free_desc}）",
        "模型产品": product.short_name,
        "模型名称": water.to_llm_model,
        "失败数量": f"{stat.fail_count}",
        "总数量": f"{stat.total_count}",
        "失败比例%": f"{stat.fail_count/stat.total_count*100}%",
        "错误类型": LiteLlmErrorType.display(record.error_type) if record else "未知",
        "错误信息": record.exception_message if record else "未知",
        "时间": date_util.now(8),
    }

    await tg_util.sm(data, chat_id)
    return True


# 超时未恢复报警
@async_ignore_catch_exception
async def timeout_model_monitor(water: ModelWaterConfig):
    min = int((date_util.now().timestamp() - water.updated_at.timestamp()) / 60)
    if min < 15:
        return False
    product = await product_service.get_by_chat(ProductType.CHAT.value, water.llm_model)
    if not product:
        return
    pw_configs = await config_service.list_water_config_by_model_and_use_filter(
        water.llm_model, water.use_filter
    )
    pw_configs = [x for x in pw_configs if x.llm_model == product.model]
    after_to_llm_models = [
        f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
    ]
    data = {
        "报错标题": f"模型报错持续({min})分钟未恢复（请重点关注一下）",
        "模型产品": product.short_name,
        "使用中模型列表": ", ".join(after_to_llm_models),
        "有问题模型": water.to_llm_model,
        "时间": date_util.now(8),
    }
    chat_id = await monitor_chat_id(water)
    await tg_util.sm(data, chat_id)
    if min > 60 and min < 70:
        # 如果超过60分钟未恢复，电话报警
        await call_phone()

    return True


async def recovery_model_monitor(water: ModelWaterConfig):
    product = await product_service.get_by_chat(ProductType.CHAT.value, water.llm_model)
    if not product:
        return
    pw_configs = await config_service.list_water_config_by_model_and_use_filter(
        water.llm_model, water.use_filter
    )
    pw_configs = [x for x in pw_configs if x.llm_model == product.model]
    after_to_llm_models = [
        f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
    ]
    data = {
        "报错标题": "模型恢复上架",
        "模型产品": product.short_name,
        "修改后模型列表": ", ".join(after_to_llm_models),
        "恢复": water.to_llm_model,
        "时间": date_util.now(8),
    }
    chat_id = await monitor_chat_id(water)
    await tg_util.sm(data, chat_id)
    return True


async def down_model_monitor(water: ModelWaterConfig):
    product = await product_service.get_by_chat(ProductType.CHAT.value, water.llm_model)
    if not product:
        return
    pw_configs = await config_service.list_model_water_config()
    pw_configs = [x for x in pw_configs if x.llm_model == product.model]
    after_to_llm_models = [
        f"{x.to_llm_model}({x.num})" for x in pw_configs if x.num > 0
    ]
    data = {
        "报错标题": "模型报错超过12小时，已强制下线",
        "模型产品": product.short_name,
        "修改后模型列表": ", ".join(after_to_llm_models),
        "下线模型": water.to_llm_model,
        "时间": date_util.now(8),
    }
    chat_id = await monitor_chat_id(water)
    await tg_util.sm(data, chat_id)
    return True
