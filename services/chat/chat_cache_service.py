import asyncio
from datetime import timedelta
from hashlib import md5
import logging
from dotenv import load_dotenv
from litellm import (
    ChatCompletionAssistantMessage,
    ChatCompletionCachedContent,
    ChatCompletionSystemMessage,
    ChatCompletionUserMessage,
)
from pydantic import BaseModel

from common.common_constant import (
    LlmModel,
    LlmCacheType,
    LlmModel,
)
from common.models.chat_model import ChatNextInput


from langchain.schema import HumanMessage, SystemMessage, AIMessage

from langchain_core.messages.base import BaseMessage
from persistence import redis_client, vol_engine_context_history_dao
from persistence.models.models import VolEngineContext
from persistence.models.mongo_models import VolEngineContextHistory
from utils import (
    date_util,
    exception_util,
    request_util,
    token_util,
)

log = logging.getLogger(__name__)


async def cache_message(input_message: list[BaseMessage], input: ChatNextInput):
    def transform_message(message: BaseMessage, use_cache: bool = False):
        if isinstance(message, HumanMessage):
            return ChatCompletionUserMessage(content=str(message.content), role="user")
        elif isinstance(message, AIMessage):
            return ChatCompletionAssistantMessage(
                content=str(message.content), role="assistant"
            )
        elif isinstance(message, SystemMessage):
            return (
                ChatCompletionSystemMessage(
                    content=message.content,
                    role="system",
                    cache_control=ChatCompletionCachedContent(type="ephemeral"),
                )
                if use_cache
                else ChatCompletionSystemMessage(content=message.content, role="system")
            )
        else:
            raise ValueError(f"Unsupported message type: {type(message)}")

    if input.cache_type == LlmCacheType.CONTEXT_API.value:
        ret_messages, request_model = await dp_cache_record(input, input_message)
        ret_messages = [transform_message(x) for x in ret_messages]
        return ret_messages, request_model
    if input.cache_type == LlmCacheType.EPHEMERAL_FLAG.value:
        if input_message[0].type == "system":
            cache_message = transform_message(input_message[0], use_cache=True)
            ret_messages = [cache_message] + [
                transform_message(x) for x in input_message[1:]
            ]
            return ret_messages, input.request_model
        return [transform_message(x) for x in input_message], input.request_model
    return [transform_message(x) for x in input_message], input.request_model


@exception_util.async_ignore_catch_exception
async def ark_post(cache_id: str, input: ChatNextInput, content: str):
    if not input.third_model_id or not input.third_api_key:
        logging.error(f"ArkPost,param empty,preset_model: {input.request_model}")
        return None
    now_timestamp = int(date_util.now().timestamp())
    body = {
        "model": input.third_model_id,
        "mode": "common_prefix",
        "messages": [{"role": "system", "content": str(content)}],
        "ttl": int(timedelta(minutes=60).total_seconds()),
    }
    response = await request_util.post_json(
        url="https://ark.cn-beijing.volces.com/api/v3/context/create",
        post_data=body,
        headers={
            "Authorization": f"Bearer {input.third_api_key}",
            "Content-Type": "application/json",
        },
        timeout=5,
        max_retries=1,
    )
    log.info(f"ArkPost: {body}, response: {response}")
    if not response:
        return None
    id = response.get("id", "")
    ttl = int(response.get("ttl", 0))
    if not id or not ttl:
        log.error(f"ArkPost failed, response: {response}")
        return None
    context_record = await VolEngineContext.filter(
        cache_id=cache_id, third_model_id=input.third_model_id
    ).first()
    if not context_record:
        context_record = VolEngineContext(
            cache_id=cache_id, third_model_id=input.third_model_id
        )
    context_record.expired_at = now_timestamp + ttl
    context_record.request_model = input.request_model
    context_record.context_id = id
    context_record.role_id = input.role_id
    context_record.user_id = input.user_id
    await context_record.save()
    history = VolEngineContextHistory(
        context_id=context_record.context_id,
        cache_id=cache_id,
        third_model_id=input.third_model_id,
        request_model=input.request_model,
        cache_content=str(content),
        user_id=input.user_id,
        role_id=input.role_id,
        message_id=input.message_id,
        conv_id=input.conversation_id,
    )
    await vol_engine_context_history_dao.insert(history)
    return None


async def dp_cache_record(input: ChatNextInput, input_messages: list[BaseMessage]):
    system_message = input_messages[0]
    token = token_util.num_tokens_from_string(str(system_message.content))
    cache_id = md5(str(system_message.content).encode("utf-8")).hexdigest()
    context_record = await VolEngineContext.filter(
        cache_id=cache_id, request_model=input.request_model
    ).first()
    now_timestamp = int(date_util.now().timestamp())
    if (
        context_record
        and context_record.expired_at > now_timestamp
        and not input.auto_retry
    ):
        input.read_token = token
        input.context_id = context_record.context_id
        input_messages = input_messages[1:]
        if input_messages and input_messages[-1].type == "ai":
            input_messages = input_messages[:-1]
        return input_messages, input.request_model + "-cache"
    if not context_record or context_record.expired_at < now_timestamp:
        lock = redis_client.acquire_lock("ark_post_lock", cache_id, 10)
        if lock:
            input.created_token = token
            asyncio.create_task(ark_post(cache_id, input, str(system_message.content)))
    return input_messages, input.request_model
