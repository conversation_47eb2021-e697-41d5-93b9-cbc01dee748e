import logging
from common.common_constant import ChatModeType, Language
from common.role_model import CardDetail
from persistence.models.models import (
    RoleCategoryOrder,
    RoleConfig,
    RoleOrder,
    SubTag,
    TagListOrder,
)
from properties import prop_util
from tortoise.expressions import F

from services import translate_service
from utils import json_util


log = logging.getLogger(__name__)

# sub tag interface


async def get_sub_tag_by_name(tag_name: str):
    return await SubTag.filter(tag_name=tag_name).first()

async def list_sub_tag_by_ids(sub_tag_ids: list[int]):
    return await SubTag.filter(id__in=sub_tag_ids).all()

async def list_sub_tags_with_enabled():
    return await SubTag.filter(enabled=True).all().order_by("order")


async def list_sub_tag_names_with_enabled():
    return [tag.tag_name for tag in await list_sub_tags_with_enabled()]


async def list_all_sub_tags_with_deleted() -> list[SubTag]:
    return await SubTag.all()


async def list_all_sub_tags_by_language(language: str) -> list[SubTag]:
    sub_tags_list = await SubTag.filter(enabled=True).all().order_by("order")
    if language == Language.ZH.value:
        return sub_tags_list
    trans_sub_tag_map: dict[str, str] = await translate_service.map_all_sub_tag(
        language
    )
    for sub_tag in sub_tags_list:
        sub_tag.tag_name = trans_sub_tag_map.get(sub_tag.tag_name, sub_tag.tag_name)
    return sub_tags_list


async def get_original_sub_tag_name(tag_name: str, language: str) -> str:
    if not tag_name:
        return tag_name
    if language == Language.ZH.value:
        return tag_name
    trans_sub_tag_map: dict[str, str] = await translate_service.map_all_sub_tag(
        language
    )
    for k, v in trans_sub_tag_map.items():
        if v == tag_name:
            return k
    return tag_name


async def list_original_sub_tag_names(tag_names: list[str], language: str) -> list[str]:
    if language == Language.ZH.value:
        return tag_names
    trans_sub_tag_map: dict[str, str] = await translate_service.map_all_sub_tag(
        language
    )
    mid_map = {}
    for k, v in trans_sub_tag_map.items():
        mid_map[v] = k
    return [mid_map.get(x, x) for x in tag_names]


async def update_or_create_sub_tag(tag_name: str, category: str) -> SubTag:
    # 获取 max order
    max_sub_tag = await SubTag.filter(enabled=True).all().order_by("-order").first()
    order = max_sub_tag.order + 1 if max_sub_tag else 0
    cur_tag = await SubTag.filter(tag_name=tag_name).first()
    if cur_tag:
        cur_tag.category = category
        cur_tag.enabled = True
        cur_tag.order = order
        await cur_tag.save()
        return cur_tag
    cur_tag = await SubTag.create(
        tag_name=tag_name, category=category, order=order, enabled=True
    )
    return cur_tag


# async def update_tag_category(sub_tag_id: int, category: str):
#     await SubTag.filter(id=sub_tag_id).update(category=category)


async def delete_sub_tag(sub_tag: SubTag) -> None:
    sub_tag.enabled = False
    await sub_tag.save()


async def update_sub_tag_order(sub_tag_name: str, order: int):
    sub_tag = await SubTag.filter(tag_name=sub_tag_name).first()
    if not sub_tag:
        return False
    if sub_tag.order == order:
        return False
    if order < sub_tag.order:
        await SubTag.filter(order__gte=order, order__lt=sub_tag.order).update(
            order=F("order") + 1
        )
    if order > sub_tag.order:
        await SubTag.filter(order__gt=sub_tag.order, order__lte=order).update(
            order=F("order") - 1
        )
    sub_tag.order = order
    await sub_tag.save()
    return True


# async def update_role_tags_by_file(init: bool = False):
#     roles_tags = prop_util.read_role_tags()
#     update_ret_tags = []
#     for role_tag in roles_tags:
#         card_name = role_tag["card_name"]
#         sub_tags = role_tag["sub_tags"]
#         up_role = await RoleConfig.filter(
#             card_name=card_name, privacy=True, status=True
#         ).first()
#         role_tag["exist_role_id"] = up_role.id if up_role else "-1"
#         if not up_role:
#             continue
#         role_tag["db_sub_tags"] = up_role.sub_tags
#         if up_role.sub_tags == sub_tags:
#             log.info(f"un_update_role_tags_by_file: {role_tag}")
#             continue
#         log.info(f"update_role_tags_by_file,role_id: {up_role.id},sub_tags: {sub_tags}")
#         update_ret_tags.append(role_tag)
#         if init:
#             await RoleConfig.filter(id=up_role.id).update(sub_tags=sub_tags)
#     return update_ret_tags


# async def init_sub_tags(init: bool = False):
#     sub_tags = prop_util.read_line_sub_tags()
#     sub_tags = [x for x in sub_tags if len(x) == 3]
#     all_sub_tag = await SubTag.filter().all()
#     map_sub_tag = {x.tag_name: x for x in all_sub_tag}
#     created_sub_tags = []
#     index = 0
#     for tag in sub_tags:
#         index += 1
#         db_sub_tag = map_sub_tag.get(tag[0])
#         if db_sub_tag and db_sub_tag.enabled:
#             log.info(f"sub_tag exist: {tag},order:{index}")
#             continue
#         if db_sub_tag and not db_sub_tag.enabled and init:
#             log.info(f"update sub_tag: {tag},order:{index}")
#             await SubTag.filter(id=db_sub_tag.id).update(enabled=True)
#             continue
#         created_sub_tags.append(tag)
#         if init:
#             log.info(f"create sub_tag: {tag},order:{index}")
#             await SubTag.create(tag_name=tag[0], order=index, enabled=True)
#     return {"created_sub_tags": created_sub_tags}


# async def remove_un_used_tags(init: bool = False):
#     sub_tag_map = {x.tag_name: x for x in await SubTag.filter().all()}
#     file_sub_tags = [x[0] for x in prop_util.read_line_sub_tags() if len(x) == 3]
#     role_sub_tags = []
#     role_list = await RoleConfig.filter(status=True, privacy=True).all()
#     for role in role_list:
#         role_sub_tags.extend(role.sub_tags)
#     role_sub_tags = list(set(role_sub_tags))

#     un_enabled_tags = []
#     role_used_tags = []
#     for tag in sub_tag_map.values():
#         if tag.tag_name in file_sub_tags:
#             continue
#         if tag.tag_name in role_sub_tags:
#             role_used_tags.append(tag.tag_name)
#             continue
#         if not tag.enabled:
#             continue
#         un_enabled_tags.append(tag.tag_name)
#         if init:
#             await SubTag.filter(id=tag.id).update(enabled=False)
#     return {
#         "un_enabled_tags": un_enabled_tags,
#         "role_used_tags": role_used_tags,
#     }


async def update_tag_order(mode_type: str, mode_target_id: int, position: int):
    role_order = await RoleOrder.filter(
        mode_type=mode_type, mode_target_id=mode_target_id
    ).first()
    if not role_order:
        # 自身的position+1
        await RoleOrder.filter(position__gte=position).update(
            position=F("position") + 1
        )
        await RoleOrder.create(
            mode_type=mode_type, mode_target_id=mode_target_id, position=position
        )
        return True

    if position > role_order.position:
        await RoleOrder.filter(
            position__gt=role_order.position, position__lte=position
        ).update(position=F("position") - 1)
        role_order.position = position
        await role_order.save()
        return True
    if position < role_order.position:
        await RoleOrder.filter(
            position__gte=position, position__lt=role_order.position
        ).update(position=F("position") + 1)
        role_order.position = position
        await role_order.save()
        return True
    return False


async def list_tags_with_orders():
    tags_order = await TagListOrder.all().order_by("-id").first()
    if tags_order is not None:
        return json_util.convert_to_list(tags_order.tags_order)
    else:
        return []


async def add_update_tag_list(tags_order: list[str]):
    tag_order_list = await TagListOrder.create(tags_order=tags_order)
    return tag_order_list


async def map_chosen_order():
    ret_list = await RoleOrder.filter().all()
    return {CardDetail.key(x.mode_type, x.mode_target_id): x.position for x in ret_list}


async def map_chose_role_order():
    ret_list = await RoleOrder.filter().all()
    ret_list = [x for x in ret_list if x.mode_type == ChatModeType.SINGLE.value]
    return {x.mode_target_id: x.position for x in ret_list}


async def role_hot_sub_tags(limit: int = 20):
    return await SubTag.filter(enabled=True).all().order_by("-role_count").limit(limit)


async def list_sub_tag_category():
    return [
        "互动方式",
        "性取向",
        "职业/身份",
        "关系/身份",
        "题材",
        "玩法/属性",
        "性格/心理特征/属性",
        "角色类型/属性",
    ]


async def list_category_group_sub_tags():
    category_list = await list_sub_tag_category()
    all_tags = await list_sub_tags_with_enabled()
    all_tags.sort(key=lambda x: x.order)
    category_groups = []
    for category in category_list:
        sub_tags = [tag.tag_name for tag in all_tags if tag.category == category]
        category_groups.append({"category": category, "sub_tags": sub_tags})
    return category_groups
