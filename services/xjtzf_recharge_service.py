from datetime import datetime
from decimal import Decimal
import hashlib
import logging
import requests
import os
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
)
from utils import env_const
from services import out_recharge_common

xjtzf_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/xjtzf/notify'
xjtzf_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/xjtzf/result'

APP_PAY_ID = env_const.XJTZF_APP_ID
APP_PAY_KEY = env_const.XJTZF_APP_KEY
APP_PAY_HOST = env_const.XJTZF_HOST

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.XJTZF, bot_id)

async def update_out_order_id(recharge_order_id: str, out_order_id: str):
    return await out_recharge_common.update_out_order_id(recharge_order_id, out_order_id)

async def pay_success(order_id: int,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success('', out_order_id, raw_data, order_id)

def create_xjtzf_order(order: RechargeOrder, type: str, client_ip: str):
    amount = int(order.pay_fee / 1000)
    if type == 'wechat':
        product_id = 2201
    else: # alipay
        product_id = 1202

    params = {
        'mchNo': APP_PAY_ID,
        'mchOrderNo': order.id,
        'productId': product_id,
        'amount': amount,
        'clientIp': client_ip,
        'notifyUrl': xjtzf_recharge_notify_url,
        'reqTime': int(datetime.now().timestamp()) * 1000,
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={APP_PAY_KEY}'
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.upper()

    # post params as application/x-www-form-urlencoded
    resp = requests.post(APP_PAY_HOST, 
                        data=params, 
                        proxies=env_const.RECHARGE_PROXY,
                        verify=False)

    logging.info(f'xjtzf_recharge_response: {resp.text}')
    return resp.json()