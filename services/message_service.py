import asyncio
from itertools import groupby
import json
import logging
from operator import itemgetter
import random
import re
from typing import AsyncI<PERSON><PERSON>, Iterator, Optional
import uuid
from fastapi import Request
from langchain_core.messages import BaseMessageChunk
from litellm import ChatCompletionChunk, CustomStreamWrapper
from aiogram.types import InlineKeyboardMarkup
from common.common_constant import (
    ChatChannel,
    ChatModeType,
    ErrorKey,
    LlmModel,
    ProductType,
    ChatApiVersion,
    RoleChatType,
)
from common.models.chat_model import (
    AddWaterMid,
    ChatFinishData,
    ChatHistory,
    ChatHistoryType,
    ChatNextInput,
    ChatTipsHistory,
    LiteLlmIterResponse,
)
from common.models.chat_request import ChatHistoryItem, ChatRequest
from persistence import chat_history_dao, redis_client
from persistence.models.models import (
    ChatHistoryStatistic,
    Product,
    RegexRule,
    RoleConfig,
    User,
)
from services import (
    model_service,
    operation_service,
    re_purchase_service,
    user_diamond_season_service,
    user_growth_service,
)
from services import product_service
from services.account_service import AccountService
from services.chat import chat_result_service
from services.user import user_benefit_service
from services.user_service import user_service
from utils import (
    convert_util,
    message_utils,
    response_util,
    role_util,
    str_util,
    token_util,
)
from persistence.chat_history import chat_history_persistence
from datetime import datetime

from utils import thread_util
from utils.exception_util import async_ignore_catch_exception
from utils.translate_util import _t, _tl
from aiogram.types import (
    Message,
)

log = logging.getLogger(__name__)


async def bot_message_suffix(input: ChatNextInput):
    diamond_activity_progress_desc = await _check_progress_in_diamond_activity(
        input.user_id
    )
    act_suffix = (
        f"\n\n{diamond_activity_progress_desc}"
        if diamond_activity_progress_desc
        else ""
    )
    act_suffix = add_chat_message_save_tips(input.history, act_suffix,input)
    if (
        input.chat_free_benefit > 0
        and input.chat_channel == ChatChannel.FREE_BENEFIT.value
    ):
        act_suffix += f"\n\n {input.product_display_name} {_tl("聊天剩余次数:", input.language)} {input.chat_free_benefit-1}"
    return act_suffix


async def edit_message(
    input: ChatNextInput, send_message: Message, content: str, markup, end: bool = False
):
    content = message_utils.bot_message_display_format(
        input.role_name, content, input.usb_switch
    )
    content = message_utils.process_user_name_on_display(
        content, input.request_user_name, input.display_user_name, []
    )
    if end:
        suffix = await bot_message_suffix(input)
        await send_message.edit_text(content + suffix, reply_markup=markup)
        return
    await send_message.edit_text(content)


@async_ignore_catch_exception
async def bot_chat_result_iter_new(
    user: User,
    request: ChatRequest,
    input: ChatNextInput,
    first_chunk: Optional[ChatCompletionChunk],
    response_iter: AsyncIterator[ChatCompletionChunk],
    send_message: Message,
    markup: InlineKeyboardMarkup,
    input_token_sum: int,
):
    start_time = datetime.now()
    content = message_utils.chunk_content(first_chunk) if first_chunk else ""
    suffix1 = _tl("思考中✏️……", input.language)
    suffix2 = _tl("思考中✏️………", input.language)
    suffix = suffix1
    count = 0
    finish_data = ChatFinishData(
        success=True,
        conversation_id=input.conversation_id,
        message_id=input.message_id,
        human_message_id=input.human_message_id,
        message_version=str(input.version),
    )
    first_token = True
    async for chunk in response_iter:
        chunk_content = message_utils.chunk_content(chunk)
        if not chunk_content:
            continue
        if first_token:
            first_token = False
            input.timestamp_first_token = int(datetime.now().timestamp())
        content += str(chunk_content)
        if message_utils.bot_chunk_continue(content, chunk, count):
            continue
        suffix = suffix1 if suffix == suffix2 else suffix2
        await edit_message(input, send_message, format(content) + suffix, markup, False)
        count += 1
    save_ret = await save_new_history_content_v2(
        user,
        request,
        input,
        input_token_sum,
        content,
        platform="CHAT_BOT",
        start_time=start_time,
    )
    if not save_ret and not input.auto_retry:
        content = _t(ErrorKey.CHAT_ERROR_AUTO_RETRY.value, input.language)
        content = f"{content}"
        await edit_message(input, send_message, content, markup, False)
        finish_data.success = False
        finish_data.need_retry = True
        return finish_data
    if not save_ret and input.auto_retry:
        content = _t(ErrorKey.CHAT_CONTENT_ERROR.value, input.language)
        await edit_message(input, send_message, content, markup, False)
        finish_data.success = False
        finish_data.need_retry = False
        return finish_data
    await edit_message(input, send_message, content, markup, True)

    input.timestamp_end = int(datetime.now().timestamp())
    log.info(
        f"BotChatRet,u:{input.user_id},call_id:{input.llm_call_id},fd:{finish_data},time:{input.time_data()},model:{input.preset_model}"
    )
    inviter_user_id = await user_service.add_invitation_reward(user)
    try:
        await user_growth_service.after_message_tasks(
            user, content, "", inviter_user_id
        )
        # await operation_service.after_chat_message(user, input.role_id)
    except Exception as e:
        log.warning(f"except exception,{e}")
    return finish_data


def add_chat_message_save_tips(history_list: list[ChatHistoryItem], act_suffix: str, input: ChatNextInput):
    if not history_list:
        return act_suffix
    human_history_list = [
        history
        for history in history_list
        if history.type == ChatHistoryType.HUMAN.value
    ]
    if len(human_history_list) % 2 == 0:
        act_suffix += f"\n\n{_tl("（聊天记录自动生成存档）", input.language)}"
    return act_suffix


async def _check_progress_in_diamond_activity(user_id: int):
    result = await user_diamond_season_service.get_diamond_task_progress_by_uid(user_id)
    if result is None:
        return None
    else:
        return f"目标钻石消耗：{result.get('diamond_consumption')}/{result.get('diamond_required')}"


async def chat_result_iter_v5(
    input: ChatNextInput,
    response_iter: AsyncIterator[ChatCompletionChunk],
    first_chunk: Optional[ChatCompletionChunk] = None,
):
    rule_config_len = max(len(input.role_name) + 5, 20)
    token_index = 0
    fast_check = False
    water_mid = message_utils.build_water_mid(input)
    all_content = message_utils.chunk_content(first_chunk) if first_chunk else ""
    buffered_content = message_utils.chunk_content(first_chunk) if first_chunk else ""
    finish_data = ChatFinishData(conversation_id=input.conversation_id)
    error_key = ""
    finish_reason = (
        message_utils.chunk_finish_reason("", first_chunk) if first_chunk else ""
    )
    try:
        async for chunk in response_iter:
            finish_reason = message_utils.chunk_finish_reason(finish_reason, chunk)
            content = message_utils.chunk_content(chunk)
            if not content:
                continue
            all_content += content
            buffered_content += content
            buffered_content, continue_skip = (
                await message_utils.process_stream_content(
                    buffered_content, input, token_index == 0, water_mid
                )
            )
            token_index += 1
            if continue_skip:
                continue
            content_len = len(buffered_content)
            fast_check, error_key = message_utils.fast_process_error_check(
                input, buffered_content, fast_check
            )
            if error_key:
                input.output_interrupt = True
                finish_data.need_retry = True
                break
            out_put = buffered_content[0 : content_len - rule_config_len]
            buffered_content = buffered_content[content_len - rule_config_len :]
            yield {"event": "data", "data": out_put}
            # 缓存中还有内容，需要输出
            if input.status_block_enable and not input.usb_switch:
                if buffered_content and "<StatusBlock>" in buffered_content:
                    buffered_content = message_utils.remove_status_block(
                        buffered_content
                    )
                    break

        if buffered_content:
            yield {"event": "data", "data": buffered_content}
        suffix = message_utils.calculate_suffix(all_content)
        if suffix:
            all_content += suffix
            yield {"event": "data", "data": suffix}
        finish_data.success = True
        finish_data.message_id = input.message_id
        finish_data.human_message_id = input.human_message_id
        finish_data.message_version = str(input.version)
        finish_data.finish_reason = message_utils.parse_finish_reason(
            finish_reason, all_content
        )
        if message_utils.chat_error_check(input, all_content):
            error_key = ErrorKey.CHAT_CONTENT_ERROR.value

    except Exception as e:
        log.error(f"chat_result_iter_v4 error:{e}")
        error_key = ErrorKey.CHAT_SYS_ERR.value

    if error_key:
        yield {"event": "data", "data": _t(error_key, input.language)}
        finish_data.success = False
        finish_data.need_retry = True
        finish_data.error = _t(error_key, input.language)
    # 自动重试过的不在重试
    if input.auto_retry and finish_data.need_retry:
        finish_data.need_retry = False
    input.timestamp_end = int(datetime.now().timestamp())
    log.info(
        f"ChatRet,u:{input.user_id},call_id:{input.llm_call_id},fd:{finish_data},time:{input.time_data()},model:{input.preset_model}"
    )
    yield {"event": "end", "data": finish_data.model_dump_json()}


async def save_new_history_content_v2(
    user: User,
    request: ChatRequest,
    input: ChatNextInput,
    input_token_sum: int,
    response: str = "",
    first_chunk: Optional[ChatCompletionChunk] = None,
    response_iter: Optional[AsyncIterator[ChatCompletionChunk]] = None,
    platform: str = "TMA",
    start_time: datetime = datetime.now(),
) -> bool:
    iter_ret = LiteLlmIterResponse(
        response=response,
        finish_reason="",
        prompt_tokens=0,
        completion_tokens=0,
        cache_created_tokens=0,
        cache_read_tokens=0,
    )
    if response_iter:
        try:
            iter_ret = await message_utils.process_iterator_v2(
                input, response_iter, first_chunk
            )
            response = iter_ret.response
        except Exception as e:
            await chat_result_service.error_handler(input, e)
            return False

    response = message_utils.process_prefix(response, input.role_name, False)
    error_type = message_utils.chat_error_check(input, response)
    if error_type:
        await chat_result_service.error_handler(
            input, error_type=error_type.value, extra={"content": response}
        )
        return False
    # 检查错误
    message_utils.chat_response_check(input, response)

    # 计算输出的 token 数量
    output_token_count = token_util.num_tokens_from_string(response)
    # 处理用户输入与输出的内容
    response = message_utils.process_on_edit_rules(
        response, input.regex_rules, False, input.role_name, input.request_user_name
    )
    if input.role_chat_type == RoleChatType.CHAT.value:
        response = message_utils.handle_light_chat_brackets(response)

    response = message_utils.process_user_name_on_save(
        response,
        input.request_user_name,
    )
    product_type = (
        ProductType.CHAT_CONTINUE.value
        if input.chat_continue
        else ProductType.CHAT.value
    )
    chat_product = await product_service.get_by_type_and_mid(
        product_type, input.user_chat_product_mid
    )
    if not chat_product:
        log.error(
            f"save_new_history error,user_id:{user.id},product_type: {product_type},product_mid:{input.user_chat_product_mid}"
        )
        return False

    if input.chat_continue and input.chat_continue_message_id:
        chat_history = await chat_history_dao.get_latest_by_message_id(
            input.chat_continue_message_id
        )
        if not chat_history:
            log.error(
                f"chat_continue_message_id not found:{input.chat_continue_message_id}"
            )
            return False
        chat_history.content += response
        chat_history.timestamp = int(datetime.now().timestamp())
        chat_history.input_token_count += input_token_sum
        chat_history.output_token_count += output_token_count
        chat_history.duration = int((datetime.now() - start_time).total_seconds())
        chat_history.chat_continue = bool(iter_ret.finish_reason == "length")
        await chat_history_dao.update_by_continue(chat_history)
    else:
        response = message_utils.repair_content(response, iter_ret.finish_reason)
        chat_history = ChatHistory(
            user_id=user.id,
            mode_type=request.mode_type,
            mode_target_id=request.get_mode_target_id(),
            role_id=request.role_id,
            content=response,
            conversation_id=request.conversation_id,
            message_id=input.message_id,
            type=ChatHistoryType.AI,
            timestamp=int(datetime.now().timestamp()),
            version=input.version,
            input_token_count=input_token_sum,
            output_token_count=output_token_count,
            model=input.preset_model,
            original_model=input.llm_model,
            platform=platform,
            duration=int((datetime.now() - start_time).total_seconds()),
            chat_continue=bool(iter_ret.finish_reason == "length"),
            language=input.language,
            chat_product_mid=input.user_chat_product_mid,
            product_id=str(chat_product.product_id),
            select_water_model=input.select_water_model,
        )
        await chat_history_persistence.insert_message(message=chat_history)
    log.info("save_history_success, user_id: %s, message_id: %s", user.id, input.message_id)

    # 扣费
    # consume = await deduct_product(
    #     user, chat_product, input.chat_free_benefit, request.role_id, input
    # )
    
    consume = await deduct_product_v2(input, user, chat_product, request.role_id)
    @async_ignore_catch_exception
    async def after_message_tasks():
        await ChatHistoryStatistic.create(
            user_id=user.id,
            role_id=request.role_id,
            type=ChatHistoryType.AI,
            mode_type=request.mode_type,
            mode_target_id=request.get_mode_target_id(),
            model=input.preset_model,
            conversation_id=request.conversation_id,
            message_id=input.message_id,
            input_token_count=input_token_sum,
            output_token_count=output_token_count,
            platform=platform,
            consume=consume,
            chat_product_mid=input.user_chat_product_mid,
            product_id=str(chat_product.product_id),
            llm_input_tokens=iter_ret.prompt_tokens,
            llm_output_tokens=iter_ret.completion_tokens,
            llm_cache_created_tokens=iter_ret.cache_created_tokens,
            llm_cache_read_tokens=iter_ret.cache_read_tokens,
        )
        inviter_user_id = await user_service.add_invitation_reward(user)
        await user_growth_service.after_message_tasks(
            user, response, response, inviter_user_id
        )
        log.info("after_message_tasks done, user_id: %s, message_id: %s", user.id, input.message_id)
    asyncio.create_task(after_message_tasks())
    return True


async def save_impersonate_history_new_v1(
    result: AsyncIterator[ChatCompletionChunk],
    input_token_sum: int,
    chat_input: ChatNextInput,
    first_chunk: Optional[ChatCompletionChunk] = None,
):
    iter_ret = None
    response = ""
    try:
        iter_ret = await message_utils.process_iterator_v2(
            chat_input, result, first_chunk
        )
        response = iter_ret.response
        await model_service.log_model_request(chat_input.preset_model, 1)
        chat_input.timestamp_end = int(datetime.now().timestamp())
    except Exception as e:
        log.error(
            f"save_impersonate_history_new error,model:{chat_input.preset_model},cov_id:{chat_input.conversation_id},{e}"
        )
        await model_service.log_model_request(chat_input.preset_model, 0)
        return

    error_type = message_utils.chat_error_check(chat_input, response)
    if error_type:
        await chat_result_service.error_handler(
            chat_input, error_type=error_type.value, extra={"content": response}
        )
        return

    # 计算输出的 token 数量
    output_token_count = token_util.num_tokens_from_string(response)

    chat_tips = ChatTipsHistory(
        user_id=chat_input.user_id,
        role_id=chat_input.role_id,
        content=response,
        conversation_id=chat_input.conversation_id,
        timestamp=int(datetime.now().timestamp()),
        input_token_count=input_token_sum,
        output_token_count=output_token_count,
        model=chat_input.preset_model,
        original_model=chat_input.llm_model,
        chat_product_mid=chat_input.user_chat_product_mid,
        select_water_model=chat_input.select_water_model,
    )
    await chat_history_persistence.insert_chat_tips(chat_tips=chat_tips)

    chat_product = await product_service.get_by_type_and_mid(
        ProductType.IMPERSONATE.value, chat_input.user_chat_product_mid
    )
    if not chat_product:
        log.error(
            f"save_impersonate_history_new error,user_id:{chat_input.user_id},product_type: {ProductType.IMPERSONATE.value},product_mid:{chat_input.user_chat_product_mid}"
        )
        return
    user = await user_service.get_user_by_id(chat_input.user_id)

    await deduct_product_v2(chat_input, user, chat_product, chat_input.role_id)


async def init_first_history_message(
    role: RoleConfig,
    user_id: int,
    nickname: str,
    conversation_id: str,
    platform: str = "TMA",
):
    first_message = role_util.format_first_message(role, nickname)
    if len(first_message) == 0:
        return []
    version = int(datetime.now().timestamp())
    chat_history = ChatHistory(
        user_id=user_id,
        mode_type=ChatModeType.SINGLE.value,
        mode_target_id=role.id,
        role_id=role.id,
        content=first_message,
        conversation_id=conversation_id,
        message_id=uuid.uuid4().hex,
        type=ChatHistoryType.AI,
        timestamp=int(datetime.now().timestamp()),
        version=version,
        platform=platform,
    )
    await chat_history_persistence.insert_message(message=chat_history)
    return [
        {
            "content": first_message,
            "type": "ai",
            "message_id": chat_history.message_id,
            "timestamp": int(datetime.now().timestamp()),
            "version": version,
        }
    ]

async def deduct_product_v2(
    input: ChatNextInput,
    user: User,
    chat_product: Product,
    role_id: int,
) -> int:
    if input.chat_channel == ChatChannel.FREE_BENEFIT.value:
        ret = await user_benefit_service.chat_benefit_deduct(
            user.id, chat_product, role_id
        )
        input.chat_free_benefit -= 1 if ret else 0
        log.info(f"deduct chat free benefit, user_id: {user.id}, ret: {ret}")
        return 1
    before_balance = await AccountService.get_total_balance(user.id)
    pay_order = await AccountService.create_pay_order(user.id, chat_product, role_id)
    after_balance = await AccountService.get_total_balance(user.id)
    await re_purchase_service.check_and_notify_repurchase(
        user, before_balance, after_balance
    )
    log.info(
        f"deduct chat balance, user_id: {user.id}, before_balance: {before_balance}, after_balance: {after_balance}"
    )
    return chat_product.price
