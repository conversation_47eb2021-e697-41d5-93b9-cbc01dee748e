import os
import random
from typing import Optional

from aiogram import Bo<PERSON>
from common.common_constant import BotCategor<PERSON>, ChannelCategory, GroupCategory
from persistence.models.models import TgBotConfig, TgChannelConfig, TgGroupConfig
from aiogram.client.session.aiohttp import AiohttpSession
from aiogram import Bot, types
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode

from utils import json_util
from utils import exception_util
from utils.exception_util import async_catch_exception, async_ignore_catch_tg_exception


BOT_SENDER_MAP = {}
PROXY = os.environ.get("ALL_PROXY")
SESSION = None
if PROXY:
    SESSION = AiohttpSession(proxy=PROXY)


def builder_sender_bot_by_token(token: str) -> Bot:
    return Bot(
        token=token,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML),
        session=SESSION,
    )


async def nsfw_bot(bot_id: int) -> bool:
    bot = await TgBotConfig.filter(bot_id=bot_id).first()
    if not bot:
        raise Exception(f"bot not found:{bot_id}")
    return bot.bot_nsfw


async def get_bot_config_by_tma_bot_id(tma_bot_id: str):
    return await TgBotConfig.filter(tma_bot_id=tma_bot_id).first()


async def get_sender_bot_by_id(bot_id: int) -> Bot:
    if bot_id in BOT_SENDER_MAP:
        return BOT_SENDER_MAP[bot_id]
    bot = await TgBotConfig.filter(bot_id=bot_id).first()
    if not bot:
        raise Exception(f"bot not found:{bot_id}")
    sender_bot = Bot(
        token=bot.token,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML),
        session=SESSION,
    )
    BOT_SENDER_MAP[bot_id] = sender_bot
    return sender_bot


async def get_bot_config_by_id(bot_id: int):
    bot = await TgBotConfig.filter(bot_id=bot_id).first()
    return bot


async def get_bot_config_by_token(token: str):
    bot = await TgBotConfig.filter(token=token).first()
    return bot


async def get_main_bot_by_category(
    category: BotCategory, active: bool = True, need_random=False
):
    filter = {}
    if category:
        filter["category"] = category.value
    if active:
        filter["status"] = 1
        filter["init"] = 1
    filter["main_bot"] = 1
    bots = await TgBotConfig.filter(**filter).all()
    if not bots:
        raise Exception(f"no main bot found:{category.value}")
    bots.sort(key=lambda x: x.created_at, reverse=True)
    if need_random:
        return bots[random.randint(0, len(bots) - 1)]
    return bots[0]


async def get_main_sender_bot_by_category(
    category: BotCategory, active: bool = True, need_random=False
):
    bot = await get_main_bot_by_category(category, active, need_random)
    return await get_sender_bot_by_id(bot.bot_id)


async def map_active_bot_by_ids(bot_ids: list[int]):
    bots = await TgBotConfig.filter(bot_id__in=bot_ids).all()
    bots = [x for x in bots if x.status == 1 and x.init == 1]
    return {x.bot_id: x for x in bots}


async def list_bots_by_ids(bot_ids: list[int], active: bool = True):
    filter = {}
    filter["bot_id__in"] = bot_ids
    if active:
        filter["status"] = 1
        filter["init"] = 1
    return await TgBotConfig.filter(**filter).all()


async def list_bot(category: Optional[BotCategory] = None, active: bool = True):
    filter = {}
    if category:
        filter["category"] = category.value
    if active:
        filter["status"] = 1
        filter["init"] = 1
    return await TgBotConfig.filter(**filter).all()


async def get_channel_sender(chat_id: int, need_random=False):
    channel = await TgChannelConfig.filter(chat_id=chat_id).first()
    if not channel:
        raise exception_util.param_error(f"channel not found:{chat_id}")
    bot_ids = json_util.convert_to_list(channel.helper_bot_ids)
    if not bot_ids:
        raise exception_util.param_error(f"channel not found:{chat_id}")
    bot_id = bot_ids[0] if not need_random else random.choice(bot_ids)
    return await get_sender_bot_by_id(bot_id)


async def get_group_sender(chat_id: int, need_random=False):
    group = await TgGroupConfig.filter(chat_id=chat_id).first()
    if not group:
        raise exception_util.param_error(f"group bot not found:{chat_id}")
    bot_ids = json_util.convert_to_list(group.helper_bot_ids)
    if not bot_ids:
        raise exception_util.param_error(f"group bot found:{chat_id}")
    bot_id = bot_ids[0] if not need_random else random.choice(bot_ids)
    return await get_sender_bot_by_id(bot_id)


# group interface


async def get_group_config_by_chat_id(chat_id: int):
    bot = await TgGroupConfig.filter(chat_id=chat_id).first()
    return bot


async def get_main_group(category: GroupCategory = GroupCategory.CHAT):
    group = await TgGroupConfig.filter(
        status=1, init=1, main=1, category=category.value
    ).first()
    if not group:
        raise Exception("no main group found")
    return group


async def list_group(active: bool = True, category: GroupCategory = GroupCategory.CHAT):
    filter = {}
    if active:
        filter["status"] = 1
        filter["init"] = 1
    filter["category"] = category.value
    return await TgGroupConfig.filter(**filter).all()


async def get_main_channel_by_category(category: ChannelCategory):
    channel = await TgChannelConfig.filter(
        category=category.value, status=1, init=1, main=1
    ).first()
    if not channel:
        raise Exception("no main channel found")
    return channel


async def list_channel(category: Optional[ChannelCategory] = None, active: bool = True):
    filter = {}
    if category:
        filter["category"] = category.value
    if active:
        filter["status"] = 1
        filter["init"] = 1
    return await TgChannelConfig.filter(**filter).all()


async def get_channel_by_chat_id(chat_id: int):
    bot = await TgChannelConfig.filter(chat_id=chat_id).first()
    return bot


async def get_me_by_token(token: str):
    try:
        sender_bot = builder_sender_bot_by_token(token)
        return await sender_bot.get_me()
    except Exception as e:
        return None


async def get_my_description(bot_id: int):
    try:
        sender_bot = await get_sender_bot_by_id(bot_id)
        return await sender_bot.get_my_description()
    except Exception as e:
        return None


@async_catch_exception
async def get_my_long_description(bot_id: int):
    sender_bot = await get_sender_bot_by_id(bot_id)
    return await sender_bot.get_my_short_description()


@async_catch_exception
async def get_me(bot_id: int):
    sender_bot = await get_sender_bot_by_id(bot_id)
    return await sender_bot.get_me()


@async_catch_exception
async def chat_info(
    bot_id: Optional[int] = None,
    chat_id: Optional[int] = None,
    user_name: Optional[str] = None,
):
    sender_bot = None
    if not bot_id:
        sender_bot = await get_main_sender_bot_by_category(BotCategory.HELPER)
    else:
        sender_bot = await get_sender_bot_by_id(bot_id)
    if chat_id:
        return await sender_bot.get_chat(chat_id=chat_id)
    if user_name:
        return await sender_bot.get_chat("@" + user_name)
    return None


async def map_all_chat_names():
    channels = (
        await TgChannelConfig().filter().only("chat_id", "username", "title").all()
    )
    ret = {}
    for channel in channels:
        ret[channel.chat_id] = f"{channel.title}(@{channel.username})"
    groups = await TgGroupConfig().filter().only("chat_id", "username", "title").all()
    for group in groups:
        ret[group.chat_id] = f"{group.title}(@{group.username})"
    bots = await TgBotConfig().filter().only("bot_id", "username", "first_name").all()
    for bot in bots:
        ret[bot.bot_id] = f"{bot.first_name}(@{bot.username})"
    return ret


async def swf_bots():
    bots = await TgBotConfig.filter(bot_nsfw=0).all()
    return bots

@async_ignore_catch_tg_exception
async def check_in_chat(tg_user_id: int, chat_id: int):
    helper_bot_ids = []
    group = await TgGroupConfig.filter(chat_id=chat_id).first()
    if group:
        helper_bot_ids = json_util.convert_to_list(group.helper_bot_ids)

    if not helper_bot_ids:
        channel = await TgChannelConfig.filter(chat_id=chat_id).first()
        helper_bot_ids = (
            json_util.convert_to_list(channel.helper_bot_ids) if channel else []
        )
    if not helper_bot_ids:
        return False
    sender = await get_sender_bot_by_id(helper_bot_ids[0])
    member = await sender.get_chat_member(chat_id, tg_user_id)
    if not member:
        return False
    if (
        isinstance(member, types.ChatMemberOwner)
        or isinstance(member, types.ChatMemberMember)
        or isinstance(member, types.ChatMemberAdministrator)
        or isinstance(member, types.ChatMemberRestricted)
    ):
        return True
    return False

def is_eng_only_bot(bot_config: TgBotConfig | None):
    if bot_config is not None:
        return bot_config.lang.startswith('en')
    return False

async def is_eng_only_bot_by_id(bot_id: int):
    bot_config = await TgBotConfig.filter(bot_id=bot_id).first()
    return is_eng_only_bot(bot_config)

async def is_eng_only_bot_by_tma_id(tma_bot_id: str):
    bot_config = await TgBotConfig.filter(tma_bot_id=tma_bot_id).first()
    return is_eng_only_bot(bot_config)

async def get_bot_config(bot: Bot) -> TgBotConfig | None:
    bot_config = await TgBotConfig.filter(bot_id=bot.id).first()
    return bot_config

async def get_bot_langs(bot_ids: list[int]) -> list[str]:
    bots = await TgBotConfig.filter(bot_id__in=bot_ids).all()
    return [b.lang for b in bots]