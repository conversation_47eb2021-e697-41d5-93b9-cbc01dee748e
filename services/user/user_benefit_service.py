from datetime import datetime, timedelta
import logging
from common.common_constant import (
    ChatBenefitEnum,
    ChatBenefitType,
    Language,
    ProductType,
    UserBenefitCategory,
)
from common.models.chat_model import UcbBrief, UserChatBenefitDetail
from common.role_model import ProductResponse
from persistence import redis_client
from persistence.models.models import (
    UcbRefreshRecord,
    ChatBenefit,
    ConsumeChatBenefitRecord,
    Product,
    RechargeOrder,
    RechargeProductBenefit,
    RechargeStatusEnum,
    User,
    UserChatBenefit,
)
from services import product_service
from utils import date_util
from utils.translate_util import _tl


log = logging.getLogger(__name__)


async def auto_receive_benefit(user: User):
    user_benefits = await UserChatBenefit.filter(user_id=user.id).all()
    if len(user_benefits) == 0:
        return
    lock = redis_client.acquire_lock(f"auto_receive_benefit", str(user.id))
    if not lock:
        return
    chat_benefit_ids = [user_benefit.chat_benefit_id for user_benefit in user_benefits]
    chat_benefit_config = await ChatBenefit.filter(id__in=chat_benefit_ids).all()
    chat_benefit_map = {
        chat_benefit.id: chat_benefit for chat_benefit in chat_benefit_config
    }

    async def daily_receive(user_benefit: UserChatBenefit):
        now_at = date_util.now(0)
        if user_benefit.valid_end_at.timestamp() < now_at.timestamp():
            return
        receive_at = date_util.add_utc_zone(user_benefit.received_at)
        if receive_at.day != now_at.day:
            log.info(
                f"daily_receive user_id:{user.id},used_times:{user_benefit.used_times}"
            )
            await UcbRefreshRecord.create(
                user_id=user.id,
                chat_benefit_id=user_benefit.chat_benefit_id,
                user_chat_benefit_id=user_benefit.id,
                last_received_at=user_benefit.received_at,
                last_used_times=user_benefit.used_times,
                now_received_at=now_at,
                reward_times=user_benefit.reward_times,
            )
            user_benefit.received_at = now_at
            user_benefit.used_times = 0
            await user_benefit.save()

    async def hourly_receive(user_benefit: UserChatBenefit):
        now_at = date_util.now(0)
        if user_benefit.valid_end_at.timestamp() < now_at.timestamp():
            return
        receive_at = date_util.add_utc_zone(user_benefit.received_at)
        if receive_at.hour != now_at.hour:
            user_benefit.received_at = now_at
            user_benefit.used_times = 0
            await user_benefit.save()

    for user_benefit in user_benefits:
        if user_benefit.chat_benefit_id not in chat_benefit_map:
            continue
        chat_benefit = chat_benefit_map[user_benefit.chat_benefit_id]
        if chat_benefit.type == ChatBenefitType.DAILY_RECEIVE_TIMES.value:
            await daily_receive(user_benefit)
        elif chat_benefit.type == ChatBenefitType.HOURLY_RECEIVE_TIMES.value:
            await hourly_receive(user_benefit)


# 查询权益数量
async def chat_model_count(user: User) -> int:
    await auto_receive_benefit(user)
    user_product = await product_service.get_user_chat_product(user)
    if not user_product:
        return 0
    user_id = user.id
    now_at = date_util.now(0)
    user_benefits = await UserChatBenefit.filter(user_id=user_id).all()
    user_benefits = [
        x for x in user_benefits if x.valid_end_at.timestamp() >= now_at.timestamp()
    ]
    user_benefits = [
        user_benefit
        for user_benefit in user_benefits
        if user_benefit.reward_times > user_benefit.used_times
    ]
    if len(user_benefits) == 0:
        return 0
    user_benefits.sort(key=lambda x: x.valid_end_at)
    chat_benefit_ids = [user_benefit.chat_benefit_id for user_benefit in user_benefits]
    chat_benefit_configs = await ChatBenefit.filter(id__in=chat_benefit_ids).all()
    chat_benefit_configs = [
        chat_benefit
        for chat_benefit in chat_benefit_configs
        if chat_benefit.limit_chat_product_mid == user_product.mid
    ]
    chat_benefit_map = {
        chat_benefit.id: chat_benefit for chat_benefit in chat_benefit_configs
    }
    user_benefits = [
        user_benefit
        for user_benefit in user_benefits
        if user_benefit.chat_benefit_id in chat_benefit_map
    ]
    if len(user_benefits) == 0:
        return 0

    return sum(
        [
            user_benefit.reward_times - user_benefit.used_times
            for user_benefit in user_benefits
        ]
    )


# 扣除权益
async def chat_benefit_deduct(user_id: int, product: Product, role_id: int = 0) -> bool:
    now_at = date_util.now(0)
    user_benefits = await UserChatBenefit.filter(user_id=user_id).all()
    user_benefits = [
        x for x in user_benefits if x.valid_end_at.timestamp() >= now_at.timestamp()
    ]
    user_benefits = [
        user_benefit
        for user_benefit in user_benefits
        if user_benefit.reward_times > user_benefit.used_times
    ]
    if len(user_benefits) == 0:
        log.error(
            f"chat_benefit_deduct no valid user benefit, user_id:{user_id}, product_id:{product.product_id}"
        )
        return False
    user_benefits.sort(key=lambda x: x.valid_end_at)
    chat_benefit_ids = [user_benefit.chat_benefit_id for user_benefit in user_benefits]
    chat_benefit_config = await ChatBenefit.filter(
        id__in=chat_benefit_ids, limit_chat_product_mid=product.mid
    ).all()
    chat_benefit_map = {
        chat_benefit.id: chat_benefit for chat_benefit in chat_benefit_config
    }
    user_benefits = [
        user_benefit
        for user_benefit in user_benefits
        if user_benefit.chat_benefit_id in chat_benefit_map
    ]
    if len(user_benefits) == 0:
        log.error(
            f"chat_benefit_deduct no valid user benefit, user_id:{user_id}, product_id:{product.product_id}"
        )
        return False

    user_benefit = user_benefits[0]

    user_benefit.used_times += 1
    user_benefit.sum_used_times += 1
    await user_benefit.save()

    await ConsumeChatBenefitRecord.create(
        user_id=user_id,
        chat_benefit_id=user_benefit.chat_benefit_id,
        user_chat_benefit_id=user_benefit.id,
        consume_times=1,
        model=product.model,
        product_id=product.product_id,
        role_id=role_id,
    )
    return True


# 增加权益
async def reward_chat_benefit(user_id: int, recharge_product_id: str):
    try:
        log.info(
            f"reward_chat_benefit user_id:{user_id},recharge_product_id:{recharge_product_id}"
        )
        product_benefits = await RechargeProductBenefit.filter(
            recharge_product_id=str(recharge_product_id), online=True
        ).all()
        if len(product_benefits) == 0:
            return False
        for product_benefit in product_benefits:
            await reward_chat_benefit_by_id(user_id, product_benefit.chat_benefit_id)
        return True
    except Exception as e:
        log.error(
            f"reward_chat_benefit, user_id:{user_id},recharge_product_id:{recharge_product_id},error:{e}"
        )
        return False


async def has_benefit_recharge(user_id: int) -> bool:
    recharge_orders = await RechargeOrder.filter(
        user_id=user_id,
        status=RechargeStatusEnum.SUCCEED,
        pay_fee__gt=0,
    ).all()
    if len(recharge_orders) == 0:
        return False
    order_ids = [str(order.recharge_product_id) for order in recharge_orders]
    product_benefits = await RechargeProductBenefit.filter(
        recharge_product_id__in=order_ids, online=True
    ).all()
    return len(product_benefits) > 0


async def is_benefit_recharge(recharge_product_id: str) -> bool:
    product_benefits = await RechargeProductBenefit.filter(
        recharge_product_id=str(recharge_product_id), online=True
    ).all()
    return len(product_benefits) > 0


# 增加权益
async def reward_chat_benefit_by_id(
    user_id: int,
    chat_benefit_id: int,
    benefit_type: ChatBenefitEnum = ChatBenefitEnum.RECHARGE,
):
    log.info(
        f"reward_chat_benefit_by_id user_id:{user_id},chat_benefit_id:{chat_benefit_id}"
    )
    user_benefits = await UserChatBenefit.filter(
        user_id=user_id, chat_benefit_id=chat_benefit_id
    ).all()
    chat_benefit = await ChatBenefit.filter(id=chat_benefit_id).first()
    if not chat_benefit:
        log.error(
            f"chat_benefit_id not found,user_id:{user_id},chat_benefit_id:{chat_benefit_id}"
        )
        return
    if chat_benefit.limit_times <= len(user_benefits):
        log.info(
            f"chat_benefit_id limit_times exceeded,user_id:{user_id},chat_benefit_id:{chat_benefit_id}"
        )
        return
    valid_end_at = date_util.now(0) + timedelta(days=chat_benefit.reward_valid_days)
    if chat_benefit.type == ChatBenefitType.DAILY_RECEIVE_TIMES.value:
        valid_end_at = date_util.utc_day_start() + timedelta(
            days=chat_benefit.reward_valid_days - 1, hours=23, minutes=59, seconds=59
        )
    await UserChatBenefit.create(
        user_id=user_id,
        chat_benefit_id=chat_benefit_id,
        reward_times=chat_benefit.reward_times,
        used_times=0,
        valid_start_at=date_util.now(0),
        valid_end_at=valid_end_at,
        received_at=date_util.now(0),
        benefit_type=benefit_type.value,
    )
    return


async def map_valid_by_config_ids(
    user_id: int, config_ids: list[int]
) -> dict[int, UcbBrief]:
    now_at = date_util.now(0)
    user_benefits = await UserChatBenefit.filter(
        user_id=user_id,
        chat_benefit_id__in=config_ids,
    ).all()
    user_benefits = [
        x for x in user_benefits if x.valid_end_at.timestamp() >= now_at.timestamp()
    ]
    chat_benefit_configs = await ChatBenefit.filter(id__in=config_ids).all()
    products = await Product.filter(type=ProductType.CHAT.value).all()
    product_mid_map = {x.mid: x.short_name for x in products}
    user_benefits.sort(key=lambda x: x.valid_end_at)
    ret_map = {}
    for config in chat_benefit_configs:
        ub_list = [x for x in user_benefits if x.chat_benefit_id == config.id]
        sum_remain_times = sum([x.reward_times - x.used_times for x in ub_list])
        sum_reward_times = sum([x.reward_times for x in ub_list])
        ret_map[config.id] = UcbBrief(
            sum_remain_times=sum_remain_times,
            sum_reward_times=sum_reward_times,
            reward_times=config.reward_times,
            product_short_name=product_mid_map.get(config.limit_chat_product_mid, ""),
            product_mid=config.limit_chat_product_mid,
        )
    log.info(
        f"map_valid_by_config_ids user_id:{user_id},ucb_len:{len(user_benefits)},ret_map:{ret_map}"
    )
    return ret_map


async def map_valid_by_all_product_mids(user: User) -> dict[str, UcbBrief]:
    benefit_list = await list_valid(user, "")
    products = await product_service.list_display_chat_product()

    def format_ucb_brief(product: ProductResponse):
        mid_list = [x for x in benefit_list if x.model_mid == product.mid]
        sum_remain_times = sum([x.remain_times for x in mid_list])
        sum_reward_times = sum([x.reward_times for x in mid_list])
        return UcbBrief(
            sum_remain_times=sum_remain_times,
            sum_reward_times=sum_reward_times,
            product_short_name=product.short_name,
            product_mid=product.mid,
        )
    return {product.mid: format_ucb_brief(product) for product in products}


async def list_valid(
    user: User, user_benefit_category: str = "", language: str = Language.ZH.value
) -> list[UserChatBenefitDetail]:
    user_id = user.id
    now_at = date_util.now(0)
    user_benefits = []
    if not user_benefit_category:
        user_benefits = await UserChatBenefit.filter(user_id=user_id).all()
        user_benefits = [
            x for x in user_benefits if x.valid_end_at.timestamp() >= now_at.timestamp()
        ]
    else:
        ubc = UserBenefitCategory(user_benefit_category)
        benefit_types = [x.value for x in ubc.benefit_types()]
        user_benefits = await UserChatBenefit.filter(
            user_id=user_id,
            benefit_type__in=benefit_types,
        ).all()
        user_benefits = [
            x for x in user_benefits if x.valid_end_at.timestamp() >= now_at.timestamp()
        ]
    if len(user_benefits) == 0:
        return []
    user_benefits.sort(key=lambda x: x.valid_end_at)

    benefit_ids = [user_benefit.chat_benefit_id for user_benefit in user_benefits]
    chat_benefit_config = await ChatBenefit.filter(id__in=benefit_ids).all()
    chat_benefit_map = {
        chat_benefit.id: chat_benefit for chat_benefit in chat_benefit_config
    }
    products = await Product.filter(type=ProductType.CHAT.value).all()
    product_mid_map = {product.mid: product for product in products}
    ret = []
    for user_benefit in user_benefits:
        if user_benefit.chat_benefit_id not in chat_benefit_map:
            continue
        chat_benefit = chat_benefit_map[user_benefit.chat_benefit_id]
        product = product_mid_map.get(chat_benefit.limit_chat_product_mid)
        if not product:
            continue
        next_receive_at = ""
        if chat_benefit.type == ChatBenefitType.DAILY_RECEIVE_TIMES.value:
            time = date_util.utc_day_start() + timedelta(days=1)
            next_receive_at = date_util.datetime2utc8str(time)
        detail = UserChatBenefitDetail(
            remain_times=user_benefit.reward_times - user_benefit.used_times,
            reward_times=user_benefit.reward_times,
            benefit_title=_tl(chat_benefit.title, language, ChatBenefit.__name__),
            expire_at=date_util.datetime2utc8str(user_benefit.valid_end_at),
            next_receive_at=next_receive_at,
            model_display_name=_tl(product.display_name, language, Product.__name__),
            model_mid=product.mid,
        )
        ret.append(detail)
    return ret


async def bot_user_benefit_desc(user: User) -> str:
    type_list = [
        UserBenefitCategory.RECHARGE,
        UserBenefitCategory.GIFT,
        UserBenefitCategory.FREE,
    ]
    desc_list = []
    for mid_type in type_list:
        desc = await get_user_valid_benefit_desc(user, mid_type)
        if desc:
            desc_list.append(desc)
    if len(desc_list) == 0:
        return ""
    return "\n".join(desc_list)


async def get_user_valid_benefit_desc(
    user: User, user_benefit_category: UserBenefitCategory
):
    user_benefits = await list_valid(user, user_benefit_category.value)
    if len(user_benefits) == 0:
        return ""
    products = await product_service.list_original_products(ProductType.CHAT)
    products.sort(key=lambda x: x.price)
    model_remain_times_map = {x.mid: 0 for x in products}
    for user_benefit in user_benefits:
        model_remain_times_map[user_benefit.model_mid] += user_benefit.remain_times
    valid_products = [x for x in products if model_remain_times_map[x.mid] > 0]
    if len(valid_products) == 0:
        return ""
    valid_product_desc = [
        f"{x.display_name}（剩余{model_remain_times_map[x.mid]}次）"
        for x in valid_products
    ]
    all_product_desc = ",".join(valid_product_desc)
    ubc_desc = UserBenefitCategory.to_desc_map().get(user_benefit_category.value)
    return f"{ubc_desc}：{all_product_desc}"


async def map_model_valid(user: User) -> dict[str, int]:
    benefit_list = await list_valid(user)
    # 分组
    model_map = {}
    for item in benefit_list:
        if item.model_display_name not in model_map:
            model_map[item.model_display_name] = item.remain_times
        else:
            model_map[item.model_display_name] += item.remain_times
    return model_map


async def list_consume(
    user_id: int, offset: int, limit: int, start_at: datetime, end_at: datetime
):
    consume_records = (
        await ConsumeChatBenefitRecord.filter(
            user_id=user_id,
            created_at__gte=start_at,
            created_at__lte=end_at,
        )
        .order_by("-id")
        .offset(offset)
        .limit(limit)
        .all()
    )
    total = await ConsumeChatBenefitRecord.filter(
        user_id=user_id,
        created_at__gte=start_at,
        created_at__lte=end_at,
    ).count()
    return total, consume_records


async def get_user_benefits_with_type(user_id: int, benefit_type: ChatBenefitEnum):
    return await UserChatBenefit.filter(
        user_id=user_id, benefit_type=benefit_type
    ).all()
