import logging
from datetime import UTC, datetime
from tortoise.transactions import atomic, in_transaction
from tortoise.exceptions import IntegrityError
from tortoise.functions import Sum
from common.bot_common import MessageTemplate
from common.common_constant import LlmModel, ProductType
from persistence.models.models import (
    Account,
    ActivityDiamondTask,
    ActivityDiamondTaskParticipant,
    LedgerEntry,
    OrderStatusEnum,
    ParticipationStatus,
    PayOrder,
    Product,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeStatusEnum,
    TransactionSourceEnum,
    TransactionTypeEnum,
    AwardLedger,
    ExpirableAward,
    ExpirableStatusEnum,
    User,
)
from services import bot_message_service, user_diamond_season_service
from utils import json_util

NEW_ACCOUNT_BALANCE = 1000
CHECK_IN_AMOUNT = 600
INVITATION_AMOUNT = 150
INVITEE_RECHARGE_REWARD_RATE = 0.1

log = logging.getLogger(__name__)

# account中存储用户的钻石余额
class AccountService:

    @classmethod
    async def get_account(cls, user_id: int) -> Account:
        return await Account.filter(user_id=user_id).first()

    @classmethod
    async def create_pay_order(
        cls, user_id: int, product: Product, role_id: int = 0, discount: float = 1
    ) -> PayOrder:
        now = datetime.now(UTC)
        async with in_transaction():
            acc = await Account.filter(user_id=user_id).select_for_update().first()
            gifts = (
                await ExpirableAward.filter(
                    user_id=user_id,
                    status=ExpirableStatusEnum.NORMAL,
                    balance__gt=0,
                    expires_at__gt=now,
                )
                .select_for_update()
                .all()
            )
            pay_price = int(product.price * discount)
            if (
                sum([gift.balance for gift in gifts]) + acc.total_balance
                < pay_price
            ):
                raise ValueError("余额不足")
            pay_order = PayOrder(
                user_id=user_id,
                product_id=product.product_id,
                product_price=product.price,
                amount=1,
                status=OrderStatusEnum.SUCCESS,
                total_fee=pay_price,
                role_id=role_id,
            )
            await pay_order.save()

            # 如果用户参与了进行中的聊天返钻活动，任务条件尚未达标，且该轮聊天满足要求，则消费时，优先消费「充值钻石」
            is_user_in_diamond_return_task, enroll_info = await cls.is_deduct_diamond_first(user_id, product, role_id)
            if is_user_in_diamond_return_task:
                log.info(f"user: {user_id} is in diamond return activity, role_id: {role_id}, product model: {product.model}, user will consume payed diamond first")
                gifts.sort(key=lambda x: (
                    # from_type 排序，PAYED 排在前面（用 0 和 1 来表示顺序）
                    0 if x.from_type == "PAYED" else 1,
                    # expires_at 升序排序
                    x.expires_at
                ))
                log.info(f"gifts: {gifts}")
            else:
                gifts.sort(key=lambda x: x.expires_at)

            gift_pay_amount = 0
            # 满足返钻活动要求的消耗
            payed_diamond_spending = 0  
            order_amount_mapping: dict[str, int] = {}
            for gift in gifts:
                if gift_pay_amount >= pay_price:
                    break
                remain_amount = pay_price - gift_pay_amount
                spend_amount = (
                    gift.balance if remain_amount >= gift.balance else remain_amount
                )
                gift.balance -= spend_amount
                gift.spend_amount += spend_amount
                await gift.save()
                award_leger = AwardLedger(
                    user_id=gift.user_id,
                    award_id=str(gift.award_id),
                    order_id=str(pay_order.pay_order_id),
                    amount=spend_amount,
                    pay_time=now,
                )
                await award_leger.save()
                order_amount_mapping[str(gift.out_order_id)] = spend_amount
                gift_pay_amount += spend_amount
                if gift.from_type == "PAYED":
                    payed_diamond_spending += spend_amount

            remain_amount = pay_price - gift_pay_amount
            payed_amount, free_amount = 0, 0
            if remain_amount > 0:
                new_balance = acc.total_balance - remain_amount
                leger_entry = LedgerEntry(
                    user_id=user_id,
                    transaction_id=str(pay_order.pay_order_id),
                    amount=remain_amount,
                    type=TransactionTypeEnum.DEBIT,
                    before_balance=acc.total_balance,
                    after_balance=new_balance,
                    source=TransactionSourceEnum.PAY,
                    description=f"消费{product.name}",
                )
                await leger_entry.save()

                acc.total_balance = new_balance
                remain_award_balance = acc.award_balance - remain_amount
                if remain_award_balance < 0:
                    acc.award_balance = 0
                    acc.charge_balance += remain_award_balance
                else:
                    acc.award_balance = remain_award_balance

                free_amount += remain_amount
                await acc.save()

            recharge_order_ids = [x for x in order_amount_mapping.keys() if x != "G1"]
            recharge_orders = await RechargeOrder.filter(
                recharge_order_id__in=recharge_order_ids
            ).all()
            order_payed = {
                str(x.recharge_order_id): x.pay_fee > 0 for x in recharge_orders
            }
            for out_order_id, amount in order_amount_mapping.items():
                if order_payed.get(out_order_id, False):
                    payed_amount += amount
                else:
                    free_amount += amount

            pay_order.free_amount = free_amount
            pay_order.payed_amount = payed_amount
            if is_user_in_diamond_return_task and payed_diamond_spending > 0:
                extra_info = {
                    "payed_diamond_spending": payed_diamond_spending,
                    "diamond_task_id": enroll_info.task_id
                }
                pay_order.extra_info = extra_info
            await pay_order.save()

            # 用户消费时，更新他在钻石活动中的进度
            # 此处没用if判断，是因为如果用户已达标，但后续消耗还是消耗的「充值钻石」，也还是记录到活动进度里
            await cls._handel_diamond_season_activity(user_id, product, role_id, payed_diamond_spending, enroll_info)

            return pay_order

    @classmethod  
    async def _get_user_diamond_activity_enroll_info(cls, user_id: int, product: Product, role_id: int):
        if product.type != ProductType.CHAT.value:
            return None
        diamond_task, _ = await user_diamond_season_service.get_current_diamond_task()
        if diamond_task is None:
            return None
        # 必须是在聊指定的角色卡。 -1表示支持所有角色卡
        if role_id not in diamond_task.role_ids and -1 not in diamond_task.role_ids:
            return None

        # 使用指定模型的聊天消耗才会计入活动
        allowed_chat_models = json_util.convert_to_list(diamond_task.allowed_chat_models)
        if product.mid not in allowed_chat_models:
            return None

        enroll_info = await ActivityDiamondTaskParticipant.filter(user_id=user_id,task_id=diamond_task.task_id).first()
        return enroll_info

    # 如果用户参与了进行中的聊天消耗钻活动，任务条件尚未达标，且该轮聊天满足要求，则消费时，优先消费「充值钻石」
    @classmethod  
    async def is_deduct_diamond_first(cls, user_id: int, product: Product, role_id: int):
        enroll_info = await cls._get_user_diamond_activity_enroll_info(user_id, product, role_id)
        need_deduct_diamond = enroll_info is not None and enroll_info.status == ParticipationStatus.ENROLLED.value
        return need_deduct_diamond, enroll_info

    # 用户消费时，更新他在钻石活动中的进度
    @classmethod  
    async def _handel_diamond_season_activity(cls, user_id: int, product: Product, role_id: int, pay_price: int, enroll_info: ActivityDiamondTaskParticipant|None):
        if enroll_info is None or pay_price <= 0:
            return

        diamond_task = await ActivityDiamondTask.filter(task_id=enroll_info.task_id).first()
        if diamond_task is None:
            return None
        enroll_info.diamond_consumption += pay_price
        if enroll_info.diamond_consumption >= diamond_task.required_diamond_amount and enroll_info.status == ParticipationStatus.ENROLLED.value:
            enroll_info.status = ParticipationStatus.COMPLETED.value
            enroll_info.finish_at = datetime.now(UTC)
            # 防止task做了数据变更
            enroll_info.task_end_at = diamond_task.end_at

        await enroll_info.save()

    @classmethod
    async def get_balance(cls, user_id: int):
        acc = await Account.filter(user_id=user_id).first()
        if acc is None:
            return 0
        return acc.total_balance

    @classmethod
    async def get_effective_rewards(cls, user_id: int) -> list[ExpirableAward]:
        now = datetime.now(UTC)
        gifts = (
            await ExpirableAward.filter(
                user_id=user_id,
                status=ExpirableStatusEnum.NORMAL,
                expires_at__gt=now,
            )
            .order_by("-id")
            .all()
        )
        return gifts

    @classmethod
    async def get_total_balance(cls, user_id: int):
        now = datetime.now(UTC)
        acc = await Account.filter(user_id=user_id).first()
        balance = 0
        if acc is not None:
            balance = acc.total_balance
        gifts = await ExpirableAward.filter(
            user_id=user_id,
            status=ExpirableStatusEnum.NORMAL,
            expires_at__gt=now,
        ).all()
        gift_balance = sum([gift.balance for gift in gifts])
        return balance + gift_balance

    @classmethod
    async def get_payed_total_balance(cls,user_id:int):
        now = datetime.now(UTC)
        acc = await Account.filter(user_id=user_id).first()
        balance = 0
        if acc is not None:
            balance = acc.total_balance
        gifts = await ExpirableAward.filter(
            user_id=user_id,
            status=ExpirableStatusEnum.NORMAL,
            expires_at__gt=now,
            from_type="PAYED"
        ).all()
        gift_balance = sum([gift.balance for gift in gifts])
        return balance + gift_balance

    @classmethod
    async def get_reward_total_balance(cls,user_id:int):
        now = datetime.now(UTC)
        gifts = await ExpirableAward.filter(
            user_id=user_id,
            status=ExpirableStatusEnum.NORMAL,
            expires_at__gt=now,
            from_type="REWARD"
        ).all()
        gift_balance = sum([gift.balance for gift in gifts])
        return gift_balance

    # 通过用户id获取用户的总消费金额
    @classmethod
    async def get_total_consumed_amount(cls, user_id: int):
        result = await PayOrder.filter(user_id=user_id, status=OrderStatusEnum.SUCCESS).annotate(total_fee_sum=Sum('total_fee')).values('total_fee_sum')

        return result[0]['total_fee_sum'] if result[0]['total_fee_sum'] is not None else 0

    @classmethod
    async def get_reward_total_consumed_amount(cls, user_id: int):
        result = await PayOrder.filter(user_id=user_id, status=OrderStatusEnum.SUCCESS).annotate(total_fee_sum=Sum('free_amount')).values('total_fee_sum')

        return result[0]['total_fee_sum'] if result[0]['total_fee_sum'] is not None else 0

    # 通过用户id获取用户的钻石消耗记录
    @classmethod
    async def get_pay_orders_paging(cls, user_id: int, offset:int, limit: int, start_time: datetime, end_time: datetime):
        filter ={}
        filter['user_id'] = user_id
        filter['total_fee__gt'] = 0
        filter['status'] = OrderStatusEnum.SUCCESS
        filter['created_at__gte'] = start_time
        filter['created_at__lte'] = end_time
        # query pay_order table
        if limit > 0:
            result = await PayOrder.filter(**filter).order_by('-id').offset(offset).limit(limit).all()
        else:
            result = await PayOrder.filter(**filter).order_by('-id').all()
        total = await PayOrder.filter(**filter).count()
        return result, total

    @classmethod
    async def get_expired_diamonds_history(cls, user_id: int, start_time: datetime, end_time: datetime):
        filter ={}
        filter['user_id'] = user_id
        filter['status'] = ExpirableStatusEnum.NORMAL
        filter['balance__gt'] = 0
        filter['expires_at__gte'] = start_time
        filter['expires_at__lte'] = end_time
        # query expirable_award table
        return await ExpirableAward.filter(**filter).order_by('-id').all()

    @classmethod
    async def get_expired_diamonds_history_paging(cls, user_id: int, offset:int, limit: int, start_time: datetime, end_time: datetime):
        filter ={}
        filter['user_id'] = user_id
        filter['status'] = ExpirableStatusEnum.NORMAL
        filter['balance__gt'] = 0
        filter['expires_at__gte'] = start_time
        filter['expires_at__lte'] = end_time
        # query expirable_award table
        result = await ExpirableAward.filter(**filter).order_by('-id').offset(offset).limit(limit).all()
        total = await ExpirableAward.filter(**filter).count()
        return result, total

    @classmethod
    async def create_account(cls, user_id: int) -> tuple[Account, bool]:
        acc = await Account.filter(user_id=user_id).first()
        if acc is not None:
            return acc, False
        async with in_transaction():
            try:
                acc = Account(
                    user_id=user_id,
                    award_balance=0,
                    charge_balance=0,
                    total_balance=0,
                )
                await acc.save()

            except IntegrityError as e:
                # duplicate error
                if e.args[0].args[0] == 1062:
                    acc = await Account.filter(user_id=user_id).first()
                    return acc, False
                else:
                    raise
            return acc, True

    @classmethod
    async def create_account_with_reward(cls, user_id: int) -> tuple[Account, bool]:
        now = datetime.now(UTC)
        acc = await Account.filter(user_id=user_id).first()
        if acc is not None:
            return acc, False
        async with in_transaction():
            try:
                acc = Account(
                    user_id=user_id,
                    award_balance=NEW_ACCOUNT_BALANCE,
                    charge_balance=0,
                    total_balance=NEW_ACCOUNT_BALANCE,
                )
                await acc.save()

                recharge = RechargeOrder(
                    user_id=user_id,
                    amount=NEW_ACCOUNT_BALANCE,
                    status=RechargeStatusEnum.SUCCEED,
                    recharge_channel=RechargeChannelEnum.ACCOUNT_CREATION,
                    out_order_id=f"create:{user_id}",
                    finished_at=now,
                )
                await recharge.save()

                leger_entry = LedgerEntry(
                    user_id=user_id,
                    transaction_id=str(recharge.recharge_order_id),
                    amount=NEW_ACCOUNT_BALANCE,
                    type=TransactionTypeEnum.CREDIT,
                    before_balance=0,
                    after_balance=NEW_ACCOUNT_BALANCE,
                    source=TransactionSourceEnum.RE_CHARGE,
                    description=f"新用户注册",
                )
                await leger_entry.save()
            except IntegrityError as e:
                # duplicate error
                if e.args[0].args[0] == 1062:
                    acc = await Account.filter(user_id=user_id).first()
                    return acc, False
                else:
                    raise
            return acc, True


async def get_total_balance(user_id: int):
    now = datetime.now(UTC)
    acc = await Account.filter(user_id=user_id).first()
    balance = 0
    if acc is not None:
        balance = acc.total_balance
    gifts = await ExpirableAward.filter(
        user_id=user_id,
        status=ExpirableStatusEnum.NORMAL,
        expires_at__gt=now,
    ).all()
    gift_balance = sum([gift.balance for gift in gifts])
    return balance + gift_balance
