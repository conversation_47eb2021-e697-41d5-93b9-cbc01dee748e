from datetime import datetime
from itertools import groupby
import json
import logging
import os
import re

from aiogram import Bot
from aiogram.enums import ParseMode
from ai import lite_llm_bot, new_chat_bot
from common.common_constant import BotCategory, DfyApiKey, Language, LlmModel, ProductType
from common.models.chat_model import ChatHistory, ChatHistoryType
from common.role_model import (
    UserRoleShareBriefDetail,
    UserRoleShareDetail,
    UserRoleShareRequest,
    UserShareCheckResponse,
)
from persistence import chat_history_dao
from persistence.models.models import (
    Product,
    RoleConfig,
    RoleOperationConfig,
    ShareDescRecord,
    User,
    UserRoleShare,
)
from persistence.redis_client import redis_client
from presets import role_fill_preset
from services import product_service, tg_config_service
from services.account_service import AccountService
from services.bot_services import helper_bots
from services.role import role_loader_service
from utils import env_const, json_util, message_utils, role_util, str_util

log = logging.getLogger(__name__)

SHARE_MSG = """<b>有大佬分享了一段精彩聊天记录！</b>
📪聊天记录名称: {title}
📄记录描述: {truncated_desc}

🗣️角色卡名: <a href="https://t.me/{tma_bot_user_name}/tavern?startapp=rd_{role_id}">{card_name}（可点击查看）</a>
👤分享者: <a href="https://t.me/{tma_bot_user_name}/tavern?startapp=uid_{user_id}">{nickname}（可点击查看）</a>

🔗<a href="https://t.me/{tma_bot_user_name}/tavern?startapp=sd_{share_id}">查看完整对话，小程序点击这里开聊</a>👈
🔗<a href="https://t.me/{bot_user_name}?start=roleshare_{share_id}">查看完整对话，TG直聊点击这里开聊</a>👈

"""


# 用户分享自己的角色卡聊天记录
async def add_user_role_share_info(
    req: UserRoleShareRequest, user_id: int, history_list: list[ChatHistory]
):
    if not history_list:
        return None
    last_msg = history_list[-1]
    record = await UserRoleShare.filter(
        user_id=user_id,
        role_id=req.role_id,
        conversation_id=req.conversation_id,
        last_message_id=last_msg.message_id,
        last_message_version=last_msg.version,
    ).first()
    if record:
        log.warning(f"replicate share: req: {req}")
        return None
    message_count = await chat_history_dao.count_by_user_id_conv_id(user_id,req.conversation_id)
    content_snapshot = [history.model_dump() for history in history_list]
    try:
        new_record = await UserRoleShare.create(
            user_id=user_id,
            role_id=req.role_id,
            title=req.title,
            description=req.description,
            conversation_id=req.conversation_id,
            last_message_id=last_msg.message_id,
            last_message_version=last_msg.version,
            content_snapshot=content_snapshot,
            message_count = message_count,
        )
        return new_record
    except Exception as e:
        log.warning(f"add_user_role_share_info error: {e}")
        return None


async def is_replicate_share_record(
    user_id: int, role_id: int, conversation_id: str, message_id: str, version: int
) -> bool:
    record = await UserRoleShare.filter(
        user_id=user_id,
        role_id=role_id,
        conversation_id=conversation_id,
        last_message_id=message_id,
        last_message_version=version,
    ).first()
    if record:
        return True
    return False


# 获取用户分享的角色卡聊天记录
async def get_role_share_by_id(share_id: int):
    share_info = await UserRoleShare.filter(id=share_id).first()
    if not share_info:
        return None
    content_snapshot = share_info.content_snapshot
    history_list: list[ChatHistory] = [
        ChatHistory.model_validate(content) for content in content_snapshot
    ]
    return UserRoleShareDetail.from_model(share_info, history_list)


# 发送角色卡分享消息到分享群
async def send_share_message(
    user: User, role: RoleConfig, title: str, description: str, share_id: int
):
    bot: Bot = helper_bots[0]
    avatar = _handle_avatar(role.role_avatar, role.image_nsfw)

    processed_desc = "\n".join(description.split("\n")[:3])
    if len(processed_desc) > 95:
        truncated_desc = processed_desc[:95].rsplit(" ", 1)[0] + "..."
    else:
        truncated_desc = processed_desc

    card_name = role.card_name
    if len(card_name) > 6:
        card_name = card_name[:6] + "..."
    nickname = user.nickname
    if len(nickname) > 6:
        nickname = nickname[:6] + "..."

    tma_bot = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    chat_bot = await tg_config_service.get_main_bot_by_category(BotCategory.CHAT_BOT)
    message_text = SHARE_MSG.format(
        title=title,
        truncated_desc=truncated_desc,
        tma_bot_user_name=tma_bot.username,
        role_id=role.id,
        card_name=card_name,
        user_id=user.id,
        nickname=nickname,
        bot_user_name=chat_bot.username,
        share_id=share_id,
    )

    for group_id in env_const.USER_SHARE_GROUP_IDS:
        try:
            log.info(
                f"send_share_message: role_id: {role.id}, role_name: {role.role_name},role_avatar: {avatar}, user_id: {user.id},  share_id: {share_id}, group_id: {group_id}"
            )
            gts = group_id.split("_")
            topic_id = int(gts[1]) if len(gts) > 1 else None
            # await bot.send_photo(
            #     chat_id=group_id,
            #     photo=avatar,
            #     caption=message_text,
            #     parse_mode=ParseMode.HTML,
            #     message_thread_id=topic_id
            # )
            await bot.send_message(
                chat_id=group_id,
                text=message_text,
                parse_mode=ParseMode.HTML,
                message_thread_id=topic_id,
            )
        except Exception as e:
            log.error(f"send_share_message error: {e}")
            continue


def _handle_avatar(avatar: str, spoiler: bool):
    avatar = str_util.format_avatar(avatar)
    if spoiler:
        avatar = str_util.handle_spoiler_avatar(avatar)
    return avatar


def get_current_share_count(user_id: int) -> int:
    today = datetime.now().strftime("%Y-%m-%d")
    redis_key = f"user:{user_id}:share_count:{today}"
    count = redis_client.get(redis_key)
    if count is not None:
        # 将字节字符串解码为普通字符串，然后转换为整数
        count = int(count.decode("utf-8"))
    else:
        count = 0
    return count


def increase_share_count(user_id: int) -> int:
    today = datetime.now().strftime("%Y-%m-%d")
    redis_key = f"user:{user_id}:share_count:{today}"
    count = redis_client.incr(redis_key)
    # expire in 24 hours
    redis_client.expire(redis_key, 3600 * 24)
    return int(str(count))

async def get_share_list_by_user_id(user_id: int, offset: int, limit: int):
    share_list = await UserRoleShare.filter(user_id=user_id).order_by('-created_at').offset(offset).limit(limit)
    total = await UserRoleShare.filter(user_id=user_id).count()
    share_brief_list = [UserRoleShareBriefDetail.from_model(share) for share in share_list]
    return share_brief_list, total

async def get_share_list_by_role_id(
    role_id: int, offset: int, limit: int
) -> tuple[list[UserRoleShareDetail], int]:
    share_list = (
        await UserRoleShare.filter(role_id=role_id)
        .order_by("-created_at")
        .offset(offset)
        .limit(limit)
    )
    total = await UserRoleShare.filter(role_id=role_id).count()
    share_detail_list = []
    for share_info in share_list:

        content_snapshot = share_info.content_snapshot
        history_list: list[ChatHistory] = [
            ChatHistory.model_validate(content) for content in content_snapshot
        ]
        share_detail = UserRoleShareDetail.from_model(share_info, history_list)
        share_detail_list.append(share_detail)
    return share_detail_list, total


async def generate_share_desc(
    user: User,
    messages: list[ChatHistory],
    language: str,
    mode_type: str,
    mode_target_id: int,
    request_id: str,
    share_product: Product
) -> ShareDescRecord:
    # 按照 message_id 分组，每个组保留version最大的
    messages = sorted(messages, key=lambda x: x.message_id)
    messages = [
        max(g, key=lambda x: x.version)
        for k, g in groupby(messages, key=lambda x: x.message_id)
    ]
    messages = sorted(messages, key=lambda x: x.timestamp)
    role = await role_loader_service.get_by_id(mode_target_id)
    role_operation = RoleOperationConfig(**json_util.convert_to_dict(role.data_config))
    request_user_name = (
        role_operation.user_role_name
        if role_operation.user_role_name
        else user.nickname
    )
    role_config = role_util.format_role_config(role, request_user_name)
    role_name = role_config.role_name

    contents = []
    for message in messages:
        content = message.content
        content = message_utils.process_prefix(content, role_name, True)
        content = message_utils.remove_status_block_and_tag(content)
        prefix = (
            role_name if message.type == ChatHistoryType.AI.value else request_user_name
        )
        contents.append(prefix + ":" + content)
    system_message = role_fill_preset.SYSTEM_SHARE_DESCRIPTION
    language = Language.val_and_region(language)

    system_message = system_message.replace("{{language}}", language)
    user_message = role_fill_preset.USER_SHARE_DESCRIPTION

    user_message = user_message.replace("{{chat_history}}", "\n".join(contents))

    # ret = await new_chat_bot.run_task(
        # LlmModel.CLAUDE_3_HAIKU.value, system_message, user_message, 200
    # )
    ret = await lite_llm_bot.run_task(LlmModel.CLAUDE_3_HAIKU.value, system_message, user_message, max_tokens=200)

    record = ShareDescRecord(
        user_id=user.id,
        request_id=request_id,
        mode_type=mode_type,
        mode_target_id=mode_target_id,
        message_ids=[message.message_id for message in messages],
        description=ret,
    )
    await record.save()
    await AccountService.create_pay_order(user.id, share_product)
    return record


async def check_share_content(share_id: int):
    user_share = await UserRoleShare.filter(id=share_id).first()
    if not user_share:
        return UserShareCheckResponse(
            check_success=True, message="No chat history found"
        )
    if user_share.check_result:
        return UserShareCheckResponse(
            **json_util.convert_to_dict(user_share.check_result)
        )
    dfy_ret = await check_share_count_by_dfy(share_id)
    moderation_ret = await check_content_by_moderation(share_id)
    check_success = bool(dfy_ret.check_success and moderation_ret.check_success)
    share_check = UserShareCheckResponse(
        check_success=check_success,
        forbidden_words=dfy_ret.forbidden_words,
        ban_categories=moderation_ret.ban_categories,
        category_scores=moderation_ret.category_scores,
    )
    await UserRoleShare.filter(id=share_id).update(check_result=share_check.model_dump())
    return share_check


async def check_share_count_by_dfy(share_id: int):
    share_info = await UserRoleShare.filter(id=share_id).first()
    if not share_info:
        return UserShareCheckResponse(
            check_success=True, message="No chat history found"
        )
    content_snapshot = share_info.content_snapshot
    history_list: list[ChatHistory] = [
        ChatHistory.model_validate(content) for content in content_snapshot
    ]
    if not history_list:
        return UserShareCheckResponse(
            check_success=True, message="No chat history found"
        )

    contents = [share_info.title, share_info.description]
    contents.extend([history.content for history in history_list])
    content = "\n".join(contents)
    response = await new_chat_bot.dfy_run_task(
        content, DfyApiKey.SHARE_CONTENT_CHECK.value
    )
    if not response:
        return UserShareCheckResponse(
            check_success=True, message="No response content found"
        )
    res_json = json.loads(response)
    check_result = res_json.get("result", True)
    un_support_words = res_json.get("un_support_words", [])
    return UserShareCheckResponse(
        check_success=check_result,
        forbidden_words=un_support_words,
    )


# async def check_share_content_by_share_id(share_id: int, llm_model: str):
#     share_info = await UserRoleShare.filter(id=share_id).first()
#     if not share_info:
#         return UserShareCheckResponse(
#             check_success=True, message="No chat history found"
#         )
#     content_snapshot = share_info.content_snapshot
#     history_list: list[ChatHistory] = [
#         ChatHistory.model_validate(content) for content in content_snapshot
#     ]
#     if not history_list:
#         return UserShareCheckResponse(
#             check_success=True, message="No chat history found"
#         )

#     contents = [share_info.title, share_info.description]
#     contents.extend([history.content for history in history_list])
#     content = "\n".join(contents)
#     content = re.sub(r"<.*?>", "", content)
#     user_content = role_fill_preset.SHARE_CHECK_USER_ZH_V1.replace(
#         "{{chat_history}}", content
#     )
#     system_content = role_fill_preset.SHARE_CHECK_SYSTEM_ZH_V1
#     response_content = await new_chat_bot.run_task(
#         llm_model, system_content, user_content, 1000
#     )
#     if not response_content:
#         return UserShareCheckResponse(
#             check_success=True, message="No response content found"
#         )
#     # 利用正则response_content中查找所有的<item>标签内容
#     forbidden_words = []
#     forbidden_words = re.findall(r"<item>(.*?)</item>", response_content, re.S)
#     if not forbidden_words:
#         return UserShareCheckResponse(
#             check_success=True,
#             forbidden_words=[],
#         )

#     ret_list = []
#     for forbidden_word in forbidden_words:
#         descriptions = re.search(r"<description>(.*?)</description>", forbidden_word)
#         chats = re.search(r"<chat>(.*?)</chat>", forbidden_word)
#         if not descriptions or not chats:
#             continue
#         ret_list.append(
#             {
#                 "description": descriptions.group(1),
#                 "chat": chats.group(1),
#             }
#         )

#     return UserShareCheckResponse(
#         check_success=False,
#         forbidden_words=ret_list,
#     )


async def check_content_by_moderation(share_id: int):
    share_info = await UserRoleShare.filter(id=share_id).first()
    if not share_info:
        return UserShareCheckResponse(
            check_success=True, message="No chat history found"
        )
    content_snapshot = share_info.content_snapshot
    history_list: list[ChatHistory] = [
        ChatHistory.model_validate(content) for content in content_snapshot
    ]
    if not history_list:
        return UserShareCheckResponse(
            check_success=True, message="No chat history found"
        )

    contents = [share_info.title, share_info.description]
    contents.extend([history.content for history in history_list])
    content = "\n".join(contents)
    content = re.sub(r"<.*?>", "", content)
    categories, scores_map = new_chat_bot.moderation_input_check(content)
    # value=True的key
    log.info(
        f"check_share_content_by_share_id: {json.dumps(categories)}, {json.dumps(scores_map)}"
    )
    ban_categories = []
    category_scores = {}
    if categories and scores_map:
        ban_categories = [k for k, v in categories.items() if v]
        category_scores = {k: scores_map[k] for k in ban_categories}
    ban_categories_list = [
        "violence/graphic",
        "sexual/minors",
        "self-harm/instructions",
        "illicit/violent",
        "hate/threatening",
    ]
    check_success = not bool([k for k in ban_categories if k in ban_categories_list])
    check_ret = UserShareCheckResponse(
        check_success= check_success,
        ban_categories=ban_categories,
        category_scores=category_scores,
    )
    return check_ret
