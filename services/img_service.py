from datetime import datetime
from typing import Optional

import pytz
from persistence.models.models_bot_image import BotImgGenTask, BotImgBasicProfile, ImgGenStatus

from common.image_bot_model import GenImageBaseProfileBO
class ImageBotService:


    @classmethod
    async def update_b_profile_resolution(cls, tg_id:int,img_def:str) -> GenImageBaseProfileBO:
        
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(tg_id=tg_id,img_gen_profile={"style":"","resolution":img_def,"privacy":"public"})
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["resolution"] = img_def
            await bot_img_profile.save(update_fields=["img_gen_profile"])
        
        return GenImageBaseProfileBO.from_model(bot_img_profile)
    
    @classmethod
    async def update_b_profile_style(cls, tg_id:int,style:str) -> GenImageBaseProfileBO:
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(tg_id=tg_id,img_gen_profile={"style":style,"resolution":"low","privacy":"public"})
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["style"] = style
            await bot_img_profile.save(update_fields=["img_gen_profile"])
        
        return GenImageBaseProfileBO.from_model(bot_img_profile)
    
    @classmethod
    async def get_basic_profile(cls, tg_id:int) -> GenImageBaseProfileBO:
        # 获取用户画图基础信息
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        
        if not bot_img_profile:
            return GenImageBaseProfileBO(style="")
        return GenImageBaseProfileBO.from_model(bot_img_profile)
    @classmethod
    async def add_img_gen_task(cls, tg_id:int, prompt:str, basic_profile:GenImageBaseProfileBO) -> BotImgGenTask:
        # 添加画图任务
        req_json = basic_profile.model_dump()
        req_json["prompt"] = prompt
        img_gen_task = await BotImgGenTask.create(tg_id=tg_id,prompt=prompt,req_json=req_json,status=ImgGenStatus.PROCESSING)
        
        return img_gen_task
    
    @classmethod
    async def get_img_gen_task_by_id(cls,task_id:int) -> Optional[BotImgGenTask]:
        # 获取画图任务
        img_gen_task = await BotImgGenTask.get_or_none(id=task_id)
    
        return img_gen_task
    
    @classmethod
    async def get_img_gen_task_by_tg_id(cls,tg_id:int) -> Optional[BotImgGenTask]:
        # 获取画图任务
        img_gen_task = await BotImgGenTask.filter(tg_id=tg_id).filter(status=ImgGenStatus.PROCESSING).first()
    
        return img_gen_task
    @classmethod
    async def check_in_progress_img_gen_task(cls,tg_id:int) -> bool:
        # 检查是否有进行中的画图任务
        img_gen_task = await BotImgGenTask.filter(tg_id=tg_id).filter(status=ImgGenStatus.PROCESSING).first()
        if img_gen_task:
            return True
        return False
    @classmethod
    async def update_img_gen_task(cls, req_id:int, status:ImgGenStatus, gen_result:Optional[dict]=None, error_message:Optional[str]=None) -> Optional[BotImgGenTask]:
        # 更新画图任务
        img_gen_task = await BotImgGenTask.get_or_none(id=req_id)
        if not img_gen_task:
            return None
        
        img_gen_task.status = status
        img_gen_task.completed_at = datetime.now(tz=pytz.utc)
        if gen_result:
            img_gen_task.gen_result = gen_result
        if error_message:
            img_gen_task.error_message = error_message
        await img_gen_task.save(update_fields=["status","gen_result","error_message","completed_at"])

      
        