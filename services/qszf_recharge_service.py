from decimal import Decimal
import hashlib
import logging
import requests
import os
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
)
from utils import env_const
from services import out_recharge_common

qszf_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/qszf/notify'
qszf_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/qszf/result'

APP_PAY_ID = env_const.QSZF_APP_ID
APP_PAY_KEY = env_const.QSZF_APP_KEY
APP_PAY_HOST = env_const.QSZF_HOST

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.QSZF, bot_id)

async def update_out_order_id(recharge_order_id: str, out_order_id: str):
    return await out_recharge_common.update_out_order_id(recharge_order_id, out_order_id)

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success(recharge_order_id, out_order_id, raw_data)

def create_qszf_order(order: RechargeOrder, type: str, client_ip: str):
    amount = f'{(Decimal(order.pay_fee) / 1000 / 100):.2f}'
    if order.pay_fee < 1000000:
        amount = '10'

    params = {
        'appId': APP_PAY_ID,
        'merchantOrderNo': order.recharge_order_id,
        'amount': amount,
        'type': type,
        'notifyUrl': qszf_recharge_notify_url,
        'returnUrl': qszf_recharge_return_url,
        'device': 'wap',
        'signType': 'MD5',
        'version': '3.0',
        'currency': 'CNY',
        'clientIp': client_ip,
        'subject': '充值',
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={APP_PAY_KEY}'
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()

    # post params as application/x-www-form-urlencoded
    resp = requests.post(f'{APP_PAY_HOST}/api/in/createOrder', 
                        data=params, proxies=env_const.RECHARGE_PROXY)
    logging.info(f'qszf_recharge_response: {resp.text}')
    return resp.json()