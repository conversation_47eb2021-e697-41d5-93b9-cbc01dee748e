from common.entity import RegexRuleRequest, UserRegexRuleResponse
from persistence.models.models import RegexOption, RegexRule


async def get_regex_rule_by_id(rule_id: str) -> RegexRule | None:
    return await RegexRule.filter(rule_id=rule_id).first()


async def add_regex_rule(rule: RegexRuleRequest) -> RegexRule:
    rule_model = RegexRule()
    rule.copy_to_model(rule_model)
    await rule_model.save()
    return rule_model


async def update_regex_rule(rule: RegexRule) -> RegexRule:
    await rule.save()
    return rule


async def get_effective_regex_rules() -> list[RegexRule]:
    return await RegexRule.filter(enabled=True).all()


async def get_all_regex_rules() -> list[RegexRule]:
    return await RegexRule.all()


async def delete_regex_rule(rule: RegexRule):
    await rule.delete()


async def list_by_option(
    option: RegexOption | str,
) -> list[RegexRule]:
    option_str = option.value if isinstance(option, RegexOption) else option
    if not option_str:
        return []
    all_regex = await get_effective_regex_rules()
    return [rule for rule in all_regex if option_str in rule.options]


async def list_res_by_option(option: RegexOption) -> list[UserRegexRuleResponse]:
    all_regex = await get_all_regex_rules()
    return [
        UserRegexRuleResponse.from_model(rule)
        for rule in all_regex
        if option.value in rule.options
    ]
