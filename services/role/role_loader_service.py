from typing import Optional
from common.common_constant import (
    AuthorDefaultName,
    ChatModeType,
    Language,
    ProductType,
    RoleTag,
)
from common.role_model import (
    RoleDataConfig,
    RoleEditDetail,
    RoleFilterRequest,
    UserRoleBrief,
)
from common.translate_model import TranslateRole
from persistence.models.models import RoleConfig, RoleOperationConfig, RoleRankStat
from services import product_service, translate_service, user_service
from services.role import character_book_service, role_audit_service
from utils import char_book_util, date_util, json_util, role_util, str_util


async def map_card_name_by_ids(role_ids: list[int]) -> dict[int, str]:
    role_configs = (
        await RoleConfig.filter(id__in=role_ids).only("id", "card_name").all()
    )
    return {role.id: role.card_name for role in role_configs}


async def map_role_name_by_ids(role_ids: list[int]) -> dict[int, str]:
    role_configs = (
        await RoleConfig.filter(id__in=role_ids).only("id", "role_name").all()
    )
    return {role.id: role.role_name for role in role_configs}


async def list_by_ids(role_ids: list[int]) -> list[RoleConfig]:
    return await RoleConfig.filter(id__in=role_ids).all()


async def list_public() -> list[RoleConfig]:
    return await RoleConfig.filter(status=True, privacy=True).all()


async def list_public_with_fields(fields: list[str]):
    return await RoleConfig.filter(status=True, privacy=True).only(*fields).all()


async def list_ids_by_user_index(
    nsfw: bool, sub_tags: Optional[list[str]] = None
) -> list[int]:
    filter = {}
    filter["status"] = True
    filter["privacy"] = True
    if not nsfw:
        filter["nsfw"] = False
    if sub_tags:
        filter["sub_tags__contains"] = sub_tags
    role_configs = await RoleConfig.filter(**filter).only("id").all()
    return [role.id for role in role_configs]


async def list_ids_by_user_filter(filter: RoleFilterRequest) -> list[int]:
    sql_filter = {}
    sql_filter["status"] = True
    sql_filter["privacy"] = True
    if not filter.nsfw:
        sql_filter["nsfw"] = False
    if filter.sub_tags:
        sql_filter["sub_tags__contains"] = filter.sub_tags
    if filter.chat_types:
        sql_filter["chat_type__in"] = filter.chat_types
    if filter.play_type:
        sql_filter["play_type"] = filter.play_type
    if filter.real_role != None:
        sql_filter["real_role"] = filter.real_role
    if not filter.product_id:
        role_configs = await RoleConfig.filter(**sql_filter).only("id").all()
        return [role.id for role in role_configs]

    role_configs = (
        await RoleConfig.filter(**sql_filter).only("id", "excluded_product_ids").all()
    )
    return [
        role.id
        for role in role_configs
        if not (filter.product_id in role.excluded_product_ids)
    ]


async def map_id2uid_by_user_filter(filter: RoleFilterRequest) -> dict[int, int]:
    sql_filter = {}
    sql_filter["status"] = True
    sql_filter["privacy"] = True
    if not filter.nsfw:
        sql_filter["nsfw"] = False
    if filter.sub_tags:
        sql_filter["sub_tags__contains"] = filter.sub_tags
    if filter.chat_types:
        sql_filter["chat_type__in"] = filter.chat_types
    if filter.play_type:
        sql_filter["play_type"] = filter.play_type
    if filter.real_role != None:
        sql_filter["real_role"] = filter.real_role
    if not filter.product_id:
        role_configs = await RoleConfig.filter(**sql_filter).only("id", "uid").all()
        return {x.id: x.uid for x in role_configs}
    role_configs = (
        await RoleConfig.filter(**sql_filter)
        .only("id", "excluded_product_ids", "uid")
        .all()
    )
    role_configs = [
        role
        for role in role_configs
        if not (filter.product_id in role.excluded_product_ids)
    ]
    return {x.id: x.uid for x in role_configs}


async def list_public_ids_by_params(
    nsfw: Optional[bool] = None, real_role: Optional[bool] = None
) -> list[int]:
    filter = {}
    filter["status"] = True
    filter["privacy"] = True
    if nsfw is not None:
        filter["nsfw"] = nsfw
    if real_role is not None:
        filter["real_role"] = real_role
    role_configs = await RoleConfig.filter(**filter).only("id").all()
    return [role.id for role in role_configs]


# 获取指定用户所发布的所有角色的id和创建时间
async def list_public_id_create_time_by_user_id(
    user_id: int, author_name_shown: bool
) -> list[dict]:
    filter = {}
    filter["uid"] = user_id
    filter["status"] = True
    filter["privacy"] = True
    role_result = (
        await RoleConfig.filter(**filter)
        .only("id", "operation_config", "created_at")
        .order_by("-created_at")
        .all()
    )

    return [
        {
            "mode_target_id": role.id,
            "created_at": role.created_at.timestamp(),
            "mode_type": ChatModeType.SINGLE.value,
        }
        for role in role_result
        if RoleOperationConfig(
            **json_util.convert_to_dict(role.operation_config)
        ).author
        == author_name_shown
    ]


async def get_by_id(role_id: int) -> RoleConfig:
    return await RoleConfig.get(id=role_id)


async def map_role_names(role_ids: list[int]):
    role_configs = await RoleConfig.filter(id__in=role_ids).all()
    return {role.id: role.role_name for role in role_configs}


async def load_by_id(role_id: int) -> RoleConfig | None:
    return await RoleConfig.filter(id=role_id).first()


async def map_translated_role_names(
    role_ids: list[int], language: str
) -> dict[int, str]:
    role_maps = await load_translated_roles(role_ids, language, "", format=False)
    return {role.id: role.role_name for role in role_maps if role}


async def load_translated_roles(
    role_ids: list[int], language: str, nickname: str, format: bool = True
) -> list[RoleConfig]:
    role_configs = await RoleConfig.filter(id__in=role_ids).all()
    if not role_configs:
        return []
    role_config_map = {role.id: role for role in role_configs}
    role_configs = [role_config_map.get(role_id) for role_id in role_ids]
    trans_role_maps: dict[int, TranslateRole] = (
        await translate_service.map_role_tasks_by_ids(language, role_ids)
    )
    trans_sub_tag_map: dict[str, str] = await translate_service.map_all_sub_tag(
        language
    )
    ret: list[RoleConfig] = []
    for role_config in role_configs:
        if not role_config:
            continue
        if language != role_config.def_language:
            role_config = role_util.translate_role_config(
                role_config, trans_role_maps.get(role_config.id, None)
            )
        role_config = role_util.translate_role_tag(role_config, trans_sub_tag_map)
        if format:
            role_config = role_util.format_role_config(role_config, nickname)
        role_config.tags = RoleTag.translate_by_source(role_config.tags, language)
        ret.append(role_config)
    return ret


async def list_by_translated(nsfw: bool, language: str):
    role_configs = []
    if nsfw:
        role_configs = await RoleConfig.filter(status=True, privacy=True).all()
    else:
        role_configs = await RoleConfig.filter(
            status=True, privacy=True, nsfw=False
        ).all()

    role_ids = [x.id for x in role_configs]
    trans_role_maps: dict[int, TranslateRole] = (
        await translate_service.map_role_tasks_by_ids(language, role_ids)
    )
    trans_sub_tag_map: dict[str, str] = await translate_service.map_all_sub_tag(
        language
    )
    ret: list[RoleConfig] = []
    for role_config in role_configs:
        if language != role_config.def_language:
            role_config = role_util.translate_role_config(
                role_config, trans_role_maps.get(role_config.id, None)
            )
        role_config = role_util.translate_role_tag(role_config, trans_sub_tag_map)
        role_config.tags = RoleTag.translate_by_source(role_config.tags, language)
        ret.append(role_config)
    return ret


async def load_translated_role(
    role_id: int, language: str, nickname: str, format: bool = True
) -> RoleConfig | None:
    ret = await load_translated_roles([role_id], language, nickname, format)
    if ret:
        return ret[0]
    return None


async def latest_card_created_at() -> int:
    role_config = (
        await RoleConfig.filter(status=1, privacy=1).order_by("-created_at").first()
    )
    if role_config:
        created_at = date_util.add_utc_zone(role_config.created_at)
        return int(created_at.timestamp())
    return 0


async def load_by_tags(tags: str):
    return await RoleConfig.filter(tags=tags, status=True).all()


async def list_public_by_sub_tags(sub_tags: list[str]):
    ret_list = (
        await RoleConfig.filter(status=True, privacy=True).only("id", "sub_tags").all()
    )
    ids = [
        role.id
        for role in ret_list
        if role.sub_tags and any([x in role.sub_tags for x in sub_tags])
    ]
    return await RoleConfig.filter(id__in=ids).all()


async def map_brief_by_ids_with_admin(ids: list[int]):
    role_configs = await RoleConfig.filter(id__in=ids).all()
    audit_map = await role_audit_service.map_by_mode(ChatModeType.SINGLE.value, ids)
    user_ids = [x.uid for x in role_configs]
    user_nickname_map = await user_service.map_nickname_by_admin(user_ids)
    role_author_name = {
        role.id: user_nickname_map.get(role.uid, AuthorDefaultName.ANONYMOUS.value)
        for role in role_configs
    }
    ret_list = [
        UserRoleBrief.from_config_and_audit(
            role,
            audit_map.get(role.id, None),
            role_author_name.get(role.id, AuthorDefaultName.ANONYMOUS.value),
            admin=True,
        )
        for role in role_configs
    ]
    return {role.id: role for role in ret_list}


async def get_brief_by_id_with_admin(id: int):
    maps = await map_brief_by_ids_with_admin([id])
    return maps.get(id, None)


async def list_user_roles_by_admin():
    return await RoleConfig.filter(status=True, privacy=False, uid__gt=0).all()


async def list_user_public_roles_by_admin():
    return await RoleConfig.filter(status=True, privacy=True, uid__gt=0).all()


async def list_deleted_public_roles_by_admin():
    return await RoleConfig.filter(status=False, privacy=True).all()


async def map_user_brief_by_filter(
    ids: list[int],
    nickname: str,
    language: str = Language.ZH.value,
    translate: bool = True,
    author: bool = True,
    audit: bool = True,
    format: bool = True,
):
    role_configs = []
    if translate:
        role_configs = await load_translated_roles(ids, language, nickname)
    else:
        role_configs = await RoleConfig.filter(id__in=ids).all()
    if format:
        role_configs = [
            role_util.format_role_config(role, nickname) for role in role_configs
        ]

    audit_map = {}
    if audit:
        audit_map = await role_audit_service.map_by_mode(ChatModeType.SINGLE.value, ids)

    role_author_name = {}
    role_author_avatar = {}
    if author:
        user_ids = [x.uid for x in role_configs if x.uid > 0]
        user_nickname_map, user_avatar_dict = await user_service.map_nickname_avatar(
            user_ids, language
        )
        role_author_name = role_util.map_role_authors(role_configs, user_nickname_map)

        role_author_avatar = {
            x.id: user_avatar_dict.get(x.uid, "") for x in role_configs
        }
    ret_list: list[UserRoleBrief] = []
    for role in role_configs:
        brief = UserRoleBrief.from_config_and_audit(
            role, audit_map.get(role.id, None), ""
        )
        brief.author_name = role_author_name.get(role.id, "")
        brief.author_avatar = role_author_avatar.get(role.id, "")
        ret_list.append(brief)
    return {role.id: role for role in ret_list}


def get_def_author_name(
    mode_type: str,
    mode_target_id: int,
    default_name: str,
    language: str = Language.ZH.value,
):
    if mode_type != ChatModeType.SINGLE.value:
        return default_name
    def_author_name = {
        177: {
            Language.ZH.value: "ST官方竞赛角色扮演方向优胜者",
            Language.EN.value: "ST Official RolePlay Winner",
            Language.ZH_TW.value: "ST官方競賽角色扮演方向優勝者",
        },
    }
    return def_author_name.get(mode_target_id, {}).get(language, default_name)


async def role_token_sum(role_id: int):
    role_config = await RoleConfig.get(id=role_id)
    if not role_config:
        return 0
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    role_token_count = role_util.num_token_dict(data_config)
    if role_config.book_id:
        book = await character_book_service.get_edit_book_by_id(role_config.book_id)
        if book:
            role_token_count["role_book"] = char_book_util.sum_token(book, True)
    return sum(role_token_count.values())


async def support_product_short_names(role_id: int):
    role_config = await RoleConfig.get(id=role_id)
    if not role_config:
        return []
    products = await product_service.list_original_products(ProductType.CHAT)
    products.sort(key=lambda x: x.price)
    if role_config.excluded_product_ids:
        products = [
            product
            for product in products
            if product.mid not in role_config.excluded_product_ids
        ]

    return [product.short_name for product in products]


async def list_role_ids_by_rank(sort_type: str):
    stat = await RoleRankStat.filter(sort_type=sort_type).order_by("-id").first()
    if not stat:
        return []
    role_ids = stat.role_ids
    return json_util.convert_to_list(role_ids)
