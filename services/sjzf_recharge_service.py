from decimal import Decimal
import hashlib
import logging
import requests
import os
import re
from datetime import datetime, timedelta, UTC
from tortoise.transactions import in_transaction
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeStatusEnum,
)
from utils import env_const
from services import out_recharge_common

sjzf_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/sjzf/notify'
sjzf_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/sjzf/result'

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.SJZF, bot_id)

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success(recharge_order_id, out_order_id, raw_data)

def create_sjzf_order(order: RechargeOrder, type: str, client_ip: str):
    if type == 'wechat':
        type = 'wxpay'

    params = {
        'pid': env_const.SJZF_APP_ID,
        'out_trade_no': order.recharge_order_id,
        'money': f'{(Decimal(order.pay_fee) / 1000 / 100):.2f}',
        'type': type,
        'notify_url': sjzf_recharge_notify_url,
        'return_url': sjzf_recharge_return_url,
        'clientip': client_ip,
        'name': '充值',
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + env_const.SJZF_APP_KEY
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()
    params['sign_type'] = 'MD5'

    # post params as application/x-www-form-urlencoded
    resp = requests.post(env_const.SJZF_HOST, data=params, proxies=env_const.RECHARGE_PROXY, verify=False)
    logging.info(f'sjzf_recharge_response: {resp.text}')
    resp_text = resp.text
    pattern = r"window\.location\.replace\(['\"]([^'\"]+)['\"]\)"
    match = re.search(pattern, resp_text)
    if not match:
        raise ValueError("SJZF 响应中未找到重定向路径")
    path = match.group(1)
    path_segments = path.split('/')
    path_segments = [p for p in path_segments if p]
    return {
        "code": 1,
        "trade_no": path_segments[-1],
        "payurl": f'{env_const.SJZF_REPLACE_DOMAIN}{'/'.join(path_segments)}/'
    }