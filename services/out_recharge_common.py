import logging
import uuid
from datetime import datetime, timedelta, UTC
from pydantic import BaseModel
from tortoise.transactions import in_transaction
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeStatusEnum,
)
from typing import Dict, Any

class RechargeRequest(BaseModel):
    recharge_id: str
    type: str = 'alipay'
    isIOS: bool = False
    channel: str = 'channel1'

async def create_recharge_order(user_id: int, recharge_product_id: str, recharge_channel: RechargeChannelEnum, pay_type: str
                                ) -> RechargeOrder | None:
    recharge_product = await RechargeProduct.filter(recharge_product_id=recharge_product_id).first()
    if recharge_product is None:
        return None
    return await create_recharge_order_with_product(user_id, recharge_product, recharge_channel, pay_type = pay_type)

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct,
                                             recharge_channel: RechargeChannelEnum, bot_id: int = 0, pay_type: str = '') -> RechargeOrder | None:
    try:
        order = RechargeOrder(
            user_id=user_id,
            out_order_id=str(uuid.uuid4()),
            amount=recharge_product.amount + recharge_product.reward_amount,
            pay_fee=recharge_product.cny_price,
            pay_currency='CNY',
            recharge_channel=recharge_channel,
            status=RechargeStatusEnum.INIT,
            recharge_product_id=recharge_product.recharge_product_id,
            from_bot_id=bot_id,
            pay_type=pay_type,
        )
        await order.save()
        return order
    except Exception as e:
        logging.exception(e)
        return None

async def update_out_order_id(recharge_order_id: str, out_order_id: str):
    async with in_transaction():
        order = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order is None or order.status != RechargeStatusEnum.INIT:
            return
        order.out_order_id = out_order_id
        await order.save()

async def update_order_status(recharge_order_id: str, status: RechargeStatusEnum):
    async with in_transaction():
        order = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order is None:
            return
        order.status = status
        await order.save()

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str,
                      order_id: int | None = None) -> RechargeOrder:
    async with in_transaction():
        if order_id is not None:
            order: RechargeOrder = await RechargeOrder.get(id=order_id)
        else:
            order: RechargeOrder = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order.status == RechargeStatusEnum.SUCCEED:
            return order
        recharge_product: RechargeProduct = await RechargeProduct.filter(recharge_product_id=order.recharge_product_id).first() # type: ignore

        history_orders = await RechargeOrder.filter(
            user_id=order.user_id, status=RechargeStatusEnum.SUCCEED, 
            pay_fee__gt=0).all()
        if len(history_orders) <= 0:
            recharge_product.reward_amount = recharge_product.fc_reward_amount

        now = datetime.now(UTC)
        order.out_order_id = out_order_id
        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = now
        order.raw_response = raw_data
        await order.save()

        # payed amount
        payed_award = ExpirableAward(user_id=order.user_id,
                                        out_order_id = order.recharge_order_id,
                                        total_amount=recharge_product.amount,
                                        spend_amount=0,
                                        balance=recharge_product.amount,
                                        status=ExpirableStatusEnum.NORMAL,
                                        expires_at=datetime(2500, 1, 1),
                                        claim_at=now,
                                        from_type='PAYED')
        await payed_award.save()
        if recharge_product.reward_amount > 0:
            reward_award = ExpirableAward(user_id=order.user_id,
                                            out_order_id = order.recharge_order_id,
                                            total_amount=recharge_product.reward_amount,
                                            spend_amount=0,
                                            balance=recharge_product.reward_amount,
                                            status=ExpirableStatusEnum.NORMAL,
                                            expires_at=now + timedelta(days=recharge_product.charged_expire_delta),
                                            claim_at=now)
            await reward_award.save()
        return order

async def get_recent_channel_orders(channel: str, limit: int) -> list[RechargeOrder]:
    dt = datetime.now(UTC) - timedelta(hours=1)
    return (
        await RechargeOrder.filter(recharge_channel=channel, created_at__gt=dt)
        .order_by("-id")
        .limit(limit)
        .all()
    )

class RechargeChannelHandler:
    """充值渠道处理基类"""
    
    def __init__(self, channel_enum: RechargeChannelEnum):
        self.channel_enum = channel_enum
        
    async def create_order(self, user_id: int, recharge_id: str, type_name: str, client_ip: str) -> Dict[str, Any]:
        """创建充值订单，返回包含pay_url的字典"""
        raise NotImplementedError()
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> Dict[str, Any]:
        """具体的支付处理逻辑，由子类实现"""
        raise NotImplementedError()
    
    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        """更新外部订单ID，由子类实现"""
        raise NotImplementedError()