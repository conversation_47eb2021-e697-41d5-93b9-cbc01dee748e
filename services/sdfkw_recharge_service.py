from decimal import Decimal
import hashlib
import logging
import requests
import os
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
)
from utils import env_const
from services import out_recharge_common

recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/sdfkw/notify'
recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/sdfkw/result'

APP_PAY_ID = env_const.SDFKW_APP_ID
APP_PAY_KEY = env_const.SDFKW_APP_KEY
APP_PAY_HOST = env_const.SDFKW_HOST

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.FFPAY, bot_id)

async def update_out_order_id(recharge_order_id: str, out_order_id: str):
    return await out_recharge_common.update_out_order_id(recharge_order_id, out_order_id)

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success(recharge_order_id, out_order_id, raw_data)

def create_sdfkw_order(order: RechargeOrder, type: str, client_ip: str):
    to_type = 'wxpay' if type == 'wechat' else type
    params = {
        'pid': APP_PAY_ID,
        'type': to_type,
        'out_trade_no': str(order.recharge_order_id),
        'notify_url': recharge_notify_url,
        'return_url': recharge_return_url,
        'name': '充值',
        'money': f'{(Decimal(order.pay_fee) / 1000 / 100):.2f}',
        'sitename': 'AI伴侣'
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + APP_PAY_KEY
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.lower()
    params['sign_type'] = 'MD5'
    params['return_type'] = 'json'

    # post params as application/x-www-form-urlencoded
    resp = requests.post(APP_PAY_HOST, data=params, proxies=env_const.RECHARGE_PROXY)
    logging.info(f'sdfkw_recharge_response: {resp.text}')
    return resp.json()