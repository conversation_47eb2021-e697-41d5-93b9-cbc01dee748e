import asyncio
import datetime
import logging

from aiogram import Bo<PERSON>
from common.bot_common import (
    JOIN_CHANNEL_SUCCESS_MSG_TEMPLATE,
    JOIN_GROUP_SUCCESS_MSG_TEMPLATE,
    Button,
    MessageTemplate,
)
from common.common_constant import (
    ApiSource,
    BotCategory,
    BotReplace,
    BotType,
    ChatBenefitEnum,
    ChatModeType,
    Language,
    RewardType,
    TaskResetPeriod,
    TaskStatus,
    WelfareTaskType,
)
from common.welfare_model import UserTask
from persistence.models.models import (
    ChatJoinRecord,
    RechargeChannelEnum,
    User,
    UserRewardRecord,
    UserTaskRecord,
    WelfareTask,
    WelfareTaskReward,
)
from services import (
    bot_message_service,
    bot_services,
    gift_award_service,
    tg_config_service,
    tg_message_service,
    user_active_service,
    user_growth_service,
    user_service,
)
from services.role import role_audit_service
from services.user import user_benefit_service
from utils import date_util
from utils.translate_util import _tl

log = logging.getLogger(__name__)


async def join_task_chat_ids():
    welfare_tasks = await WelfareTask.filter(
        task_type=WelfareTaskType.CHAT_JOIN.value, online=True
    ).all()
    if not welfare_tasks:
        return []
    return [
        int(x.target_id) for x in welfare_tasks if x.target_id and x.target_id.isdigit()
    ]


async def handle_chat_join(chat_id: int, user_id: int, title: str):
    log.info(f"handle_chat_join chat_id:{chat_id},user_id:{user_id}")
    user = await user_service.get_by_id(user_id)
    if not user:
        return

    record = await ChatJoinRecord.filter(chat_id=chat_id, user_id=user_id).first()
    if not record:
        await ChatJoinRecord.create(chat_id=chat_id, user_id=user_id)
    welfare_task = await WelfareTask.filter(
        task_type=WelfareTaskType.CHAT_JOIN.value, target_id=str(chat_id), online=1
    ).first()
    if not welfare_task:
        return
    ret = await user_growth_service.create_join_chat_task(user_id, chat_id, title)
    if not ret:
        return
    message_template = MessageTemplate(
        tips=welfare_task.push_tips,
        buttons=[
            Button(
                text="开始陪聊",
                url=f"https://t.me/{BotReplace.MAIN_TMA_BOT.value}/tavern",
            )
        ],
    )
    message_template = await bot_message_service.format_template_replace(
        message_template
    )

    sender, message = await bot_message_service.send_user_template_message(
        user, message_template
    )
    if not sender or not message:
        log.warning(f"send_user_template_message failed:{user_id},{chat_id}")
        return
    log.info(f"send_user_template_message success:{user_id},{chat_id}")
    if sender and message:
        await tg_message_service.add_deleted_message(
            message,
            bot_id=sender.id,
            expire_delta=datetime.timedelta(minutes=5),
            user_id=user_id,
        )


async def list_tasks_by_types(task_type: list[WelfareTaskType], online: bool = True):
    task_type_list = [x.value for x in task_type]
    tasks = await WelfareTask.filter(task_type__in=task_type_list, online=online).all()
    tasks.sort(key=lambda x: x.order)
    return tasks


async def list_tasks(task_type: WelfareTaskType, online: bool = True):
    tasks = await WelfareTask.filter(task_type=task_type.value, online=online).all()
    tasks.sort(key=lambda x: x.order)
    return tasks


async def list_task_rewards(task_id: str):
    task_rewards = await WelfareTaskReward.filter(task_id=task_id, online=True).all()
    return task_rewards


async def list_rewards_by_ids(reward_ids: list[int]):
    return await WelfareTaskReward.filter(id__in=reward_ids).all()


async def list_welfare_index_tasks(api_source: ApiSource):

    welfare_tasks = await WelfareTask.filter(online=1).all()
    if not welfare_tasks:
        return []
    welfare_tasks = [
        x
        for x in welfare_tasks
        if x.support_register_sources and api_source.value in x.support_register_sources
    ]
    welfare_tasks.sort(key=lambda x: x.order)
    return welfare_tasks


async def display_user_group_join_tasks(
    user_id: int,
    task_map: dict[str, WelfareTask],
    auto_receive_welfare: bool = False,
    language: str = Language.ZH.value,
) -> list[UserTask]:
    join_tasks = [
        x for x in task_map.values() if x.task_type == WelfareTaskType.CHAT_JOIN.value
    ]
    if not join_tasks:
        return []
    ret_tasks = [UserTask.init(x) for x in join_tasks]
    chat_join_records = await ChatJoinRecord.filter(user_id=user_id).all()
    chat_join_ids = [record.chat_id for record in chat_join_records]
    done_chat_join_ids = await user_growth_service.list_chat_id_by_join_chat_task(
        user_id=user_id
    )
    tg_info = await user_service.get_tg_user_by_id(user_id)

    async def check_receive_chat_join_reward(welfare_task: WelfareTask):
        tg_id = tg_info.tg_id if tg_info else None
        chat_id = int(welfare_task.target_id)
        if chat_id in chat_join_ids:
            await handle_chat_join(chat_id, user_id, task.title)
            return True
        if tg_id and await tg_config_service.check_in_chat(tg_id, chat_id):
            await handle_chat_join(chat_id, user_id, task.title)
            return True
        return False

    for task in ret_tasks:
        welfare_task = task_map.get(task.taskId)
        if not welfare_task:
            continue
        if int(welfare_task.target_id) in done_chat_join_ids:
            task.status = TaskStatus.DONE.value
            task.actionType = welfare_task.done_action_type
            task.linkUrl = welfare_task.done_link_url
            task.btn = welfare_task.btn_done_desc
            continue
        if (
            welfare_task.task_type == WelfareTaskType.CHAT_JOIN.value
            and auto_receive_welfare
        ):
            asyncio.create_task(check_receive_chat_join_reward(welfare_task))
            # await check_receive_chat_join_reward(welfare_task)
    for task in ret_tasks:
        task.title = _tl(task.title, language, WelfareTask.__name__)
        task.desc = _tl(task.desc, language, WelfareTask.__name__)
        task.btn = _tl(task.btn, language, WelfareTask.__name__)
        task.btnDoneDesc = _tl(task.btnDoneDesc, language, WelfareTask.__name__)
    return ret_tasks


async def display_user_invitation_tasks(
    user_id: int, task_map: dict[str, WelfareTask], language: str = Language.ZH.value
):
    invitation_tasks = [
        x for x in task_map.values() if x.task_type == WelfareTaskType.INVITATION.value
    ]
    if not invitation_tasks:
        return []
    ret_tasks = [UserTask.init(x) for x in invitation_tasks]
    for task in ret_tasks:
        welfare_task = task_map.get(task.taskId)
        if not welfare_task:
            continue
        task.linkUrl = await bot_services.create_web_share_link(user_id)
    for task in ret_tasks:
        task.title = _tl(task.title, language, WelfareTask.__name__)
        task.desc = _tl(task.desc, language, WelfareTask.__name__)
        task.btn = _tl(task.btn, language, WelfareTask.__name__)
        task.btnDoneDesc = _tl(task.btnDoneDesc, language, WelfareTask.__name__)
    return ret_tasks


async def display_user_check_in_tasks(
    user: User,
    tg_bot_id: str,
    task_map: dict[str, WelfareTask],
    language: str = Language.ZH.value,
):
    check_in_tasks = [
        x
        for x in task_map.values()
        if x.task_type
        in [WelfareTaskType.CHECK_IN.value, WelfareTaskType.WEB_CHECK_IN.value]
    ]
    if not check_in_tasks:
        return []
    ret_tasks = [UserTask.init(x) for x in check_in_tasks]
    check_in_finished = await user_growth_service.check_check_in_task(user.id)
    link_url = await user_active_service.get_check_in_url(user, tg_bot_id)
    link_url = link_url + "?start=ct" if link_url else ""

    for task in ret_tasks:
        welfare_task = task_map.get(task.taskId)
        if not welfare_task:
            continue
        if task.taskType == WelfareTaskType.CHECK_IN.value:
            task.linkUrl = link_url if link_url else welfare_task.link_url
        if check_in_finished:
            task.status = TaskStatus.DONE.value
            task.btn = welfare_task.btn_done_desc
            task.actionType = welfare_task.done_action_type
            task.linkUrl = welfare_task.done_link_url
        task.title = _tl(task.title, language, WelfareTask.__name__)
        task.desc = _tl(task.desc, language, WelfareTask.__name__)
        task.btn = _tl(task.btn, language, WelfareTask.__name__)
        task.btnDoneDesc = _tl(task.btnDoneDesc, language, WelfareTask.__name__)

    return ret_tasks


# check if user has received the benefits
async def check_user_received_benefits(user_id: int):
    tasks = await list_tasks_by_types([WelfareTaskType.DIRECT_RECEIVE])
    if not tasks:
        return False
    task_ids = [x.task_id for x in tasks]
    return await UserTaskRecord.filter(user_id=user_id, task_id__in=task_ids).exists()


# 是否存在可以领取奖励的人物
async def exist_receive_tasks(user_id: int):
    task_list = await list_tasks_by_types([WelfareTaskType.DIRECT_RECEIVE])
    task_maps = {x.task_id: x for x in task_list}
    display_tasks = await display_user_receive_tasks(user_id, task_maps)
    if not display_tasks:
        return False
    return any(task.status == TaskStatus.UN_RECEIVE.value for task in display_tasks)


async def display_user_receive_tasks(
    user_id: int, task_map: dict[str, WelfareTask], language: str = Language.ZH.value
):
    welfare_tasks = [
        x
        for x in task_map.values()
        if x.task_type == WelfareTaskType.DIRECT_RECEIVE.value
    ]
    if not welfare_tasks:
        return []

    async def format_status(welfare_task: WelfareTask, user_task: UserTask):
        if welfare_task.reset_period == TaskResetPeriod.AFTER_24H.value:
            user_task_record = (
                await UserTaskRecord.filter(
                    user_id=user_id, task_id=welfare_task.task_id
                )
                .order_by("-finished_at")
                .first()
            )
            if not user_task_record:
                user_task.status = TaskStatus.UN_RECEIVE.value
                return user_task
            finished_at = user_task_record.finished_at
            if finished_at < date_util.utc_now() - datetime.timedelta(days=1):
                user_task.status = TaskStatus.UN_RECEIVE.value
                return user_task
            user_task.status = TaskStatus.DONE.value
            user_task.btn = _tl(
                welfare_task.btn_done_desc, language, WelfareTask.__name__
            )
            user_task.actionType = welfare_task.done_action_type
            user_task.linkUrl = welfare_task.done_link_url
            next_time = finished_at + datetime.timedelta(days=1)
            user_task.nextTime = date_util.datetime2utc8str(
                next_time, "%Y-%m-%d %H:%M:%S"
            )
            format_template = _tl(
                "下次领取时间：{{next_time}}（香港时间）",
                language,
                WelfareTask.__name__,
            )
            user_task.subTitle = format_template.replace(
                "{{next_time}}", user_task.nextTime
            )
            user_task.sub_title = format_template.replace(
                "{{next_time}}", user_task.nextTime
            )
            return user_task
        return user_task

    async def format_desc(welfare_task: WelfareTask, user_task: UserTask):
        rewards = await list_task_rewards(welfare_task.task_id)
        if not rewards:
            return
        config_ids = [
            int(x.target_id)
            for x in rewards
            if x.reward_type == RewardType.CHAT_BENEFIT.value and x.target_id
        ]
        configs_map = await user_benefit_service.map_valid_by_config_ids(
            user_id, config_ids
        )
        tips = []
        for config_id in config_ids:
            ubc = configs_map.get(config_id)
            if not ubc:
                continue
            remain = ubc.sum_remain_times
            reward = ubc.sum_reward_times
            if user_task.status != TaskStatus.DONE.value:
                remain = ubc.reward_times
                reward = ubc.reward_times
            format_template = _tl(
                "剩余{{remain}}/{{reward}}次",
                language,
                WelfareTask.__name__,
            )
            product_short_name = _tl(
                ubc.product_short_name, language, WelfareTask.__name__
            )
            tips.append(
                f"{product_short_name}({format_template.replace('{{remain}}', str(remain)).replace('{{reward}}', str(reward))})"
            )
        user_task.desc = "、".join(tips) if tips else user_task.desc

    ret_tasks = [UserTask.init(x) for x in welfare_tasks]
    for task in ret_tasks:
        welfare_task = task_map.get(task.taskId)
        if not welfare_task:
            continue
        task.title = _tl(task.title, language, WelfareTask.__name__)
        task.desc = _tl(task.desc, language, WelfareTask.__name__)
        task.btn = _tl(task.btn, language, WelfareTask.__name__)
        task.btnDoneDesc = _tl(task.btnDoneDesc, language, WelfareTask.__name__)
        await format_status(welfare_task, task)
        await format_desc(welfare_task, task)
    return ret_tasks


async def user_display_tasks(
    user: User, tg_bot_id: str, api_source: ApiSource, language: str = Language.ZH.value
):
    task_list = await list_welfare_index_tasks(api_source)
    task_map = {x.task_id: x for x in task_list}
    task_order_map = {x.task_id: x.order for x in task_list}
    ret_user_task = await display_user_group_join_tasks(
        user.id, task_map, auto_receive_welfare=True, language=language
    )
    ret_user_task.extend(
        await display_user_invitation_tasks(user.id, task_map, language)
    )
    ret_user_task.extend(
        await display_user_check_in_tasks(user, tg_bot_id, task_map, language)
    )
    ret_user_task.extend(await display_user_receive_tasks(user.id, task_map, language))
    if not ret_user_task:
        return []
    ret_user_task.sort(key=lambda x: task_order_map.get(x.taskId, 0))
    return ret_user_task


async def complete_task(user_id: int, task_id: str, receive: bool = True):
    welfare_task = await WelfareTask.filter(task_id=task_id, online=True).first()
    if not welfare_task:
        log.error(f"complete_task task not found:{task_id}")
        return False, []
    unique_id = ""
    if welfare_task.task_type == WelfareTaskType.DIRECT_RECEIVE.value:
        user_task_record = (
            await UserTaskRecord.filter(user_id=user_id, task_id=welfare_task.task_id)
            .order_by("-finished_at")
            .first()
        )
        if (
            user_task_record
            and user_task_record.finished_at
            > date_util.utc_now() - datetime.timedelta(days=1)
        ):
            return False, []
        unique_id = date_util.datetime2str(datetime.datetime.now(), "%Y%m%d")
    if unique_id:
        user_task_record = await UserTaskRecord.create(
            user_id=user_id,
            task_id=task_id,
            task_type=welfare_task.task_type,
            task_target_id=welfare_task.target_id,
            finished_at=date_util.utc_now(),
            status=TaskStatus.DONE.value,
            unique_id=unique_id,
        )
    if receive:
        rewards = await reward_task(user_id, task_id)
        return True, rewards
    return True, []


async def get_check_in_url(user_id: int, tma_bot_id: str):
    if not tma_bot_id or tma_bot_id == "0":
        return ""
    user = await user_service.get_by_id(user_id)
    if not user:
        return ""
    try:
        chat_main_bot = await tg_config_service.get_main_bot_by_category(
            BotCategory.CHAT_BOT
        )
        tma_main_bot = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
        if tma_main_bot.tma_bot_id == tma_bot_id:
            return chat_main_bot.url

        return tma_main_bot.url
    except Exception as e:
        log.error(f"get_check_in_url error:{e}")
        return ""


async def reward_task(user_id: int, task_id: str) -> list[WelfareTaskReward]:
    welfare_task = await WelfareTask.filter(task_id=task_id, online=True).first()
    if not welfare_task:
        log.error(f"reward_task task not found:{task_id}")
        return []
    rewards = await WelfareTaskReward.filter(task_id=task_id, online=True)
    if not rewards:
        log.error(f"reward_task reward not found:{task_id}")
        return []
    for reward in rewards:
        log.info(
            f"reward_task reward:user_id:{user_id},task_id:{task_id},reward_id:{reward.id}"
        )
        user_reward_record = await UserRewardRecord.create(
            user_id=user_id,
            task_id=task_id,
            task_type=welfare_task.task_type,
            task_target_id=welfare_task.target_id,
            reward_id=reward.id,
            reward_type=reward.reward_type,
            reward_target_id=reward.target_id,
            reward_target_channel=reward.target_channel,
            reward_count=reward.reward_count,
        )
        if reward.reward_type == RewardType.CHAT_BENEFIT.value:
            benefit_channel = ChatBenefitEnum.WELFARE
            if reward.target_channel in [x.value for x in ChatBenefitEnum]:
                benefit_channel = ChatBenefitEnum(reward.target_channel)
            await user_benefit_service.reward_chat_benefit_by_id(
                user_id, int(reward.target_id), benefit_channel
            )
        if reward.reward_type == RewardType.COIN.value:
            channel = RechargeChannelEnum.WELFARE
            if reward.target_channel in [x.value for x in RechargeChannelEnum]:
                channel = RechargeChannelEnum(reward.target_channel)
            timedelta = datetime.timedelta(days=31)
            out_order_id = f"task_reward:_{task_id}_{user_reward_record.id}"
            await gift_award_service.add_award_balance_with_charge_order(
                user_id,
                reward.reward_count,
                channel,
                timedelta,
                out_order_id,
            )
    return rewards


# start==================  publish task service start
async def receive_audit_reward(
    user_id: int,
    mode_type: str,
    mode_target_id: int,
):
    log.info(
        f"receive_audit_reward, user_id:{user_id}, mode_type:{mode_type}, mode_target_id:{mode_target_id}"
    )
    user = await user_service.get_by_id(user_id)
    if not await user_publish_task_switch(user):
        return
    receive_audits = await role_audit_service.list_receive_award_audits(
        user_id,
    )
    task_type = (
        WelfareTaskType.PUBLISH_CARD
        if receive_audits
        else WelfareTaskType.FIRST_PUBLISH_CARD
    )
    task = await list_tasks(task_type)
    if not task:
        return
    rewards = await reward_task(user_id, task[0].task_id)
    if not rewards:
        log.error(f"receive_audit_reward task not found:user_id:{user_id}")
        return
    receive_reward_ids = [x.id for x in rewards]
    await role_audit_service.update_receive_reward(
        user_id=user_id,
        mode_type=mode_type,
        mode_target_id=mode_target_id,
        receive_reward_ids=receive_reward_ids,
    )


async def reward_desc(uid: int, language: str = Language.ZH.value):
    role_audits = await role_audit_service.list_receive_award_audits(uid)
    if not role_audits:
        return {}
    reward_ids = []
    for role_audit in role_audits:
        if not role_audit.receive_reward_ids or role_audit.read_receive_reward:
            continue
        reward_ids.extend(role_audit.receive_reward_ids)
    reward_ids = list(set(reward_ids))
    if not reward_ids:
        return {}
    rewards = await list_rewards_by_ids(reward_ids)
    reward_map = {x.id: x for x in rewards}
    if not rewards:
        return {}
    reward_desc = {}
    for role_audit in role_audits:
        if not role_audit.receive_reward_ids or role_audit.read_receive_reward:
            continue
        role_rewards = [
            reward_map.get(reward_id)
            for reward_id in role_audit.receive_reward_ids
            if reward_map.get(reward_id)
        ]
        if not role_rewards:
            continue
        titles = [
            _tl(x.title, language, WelfareTaskReward.__name__)
            for x in role_rewards
            if x
        ]
        reward_desc[role_audit.mode_target_id] = ",".join(titles)
    return reward_desc


async def read_received_reward(user_id: int):
    receive_audits = await role_audit_service.list_receive_award_audits(
        user_id,
    )
    if not receive_audits:
        return
    receive_audits = [
        x for x in receive_audits if x.receive_reward and not x.read_receive_reward
    ]
    if not receive_audits:
        return
    ids = [x.id for x in receive_audits]
    log.info(f"read_received_reward, user_id:{user_id}, ids:{ids}")
    await role_audit_service.update_read_receive_reward(ids)


async def publish_task_switch():
    task_types = [
        WelfareTaskType.PUBLISH_CARD,
        WelfareTaskType.FIRST_PUBLISH_CARD,
    ]
    tasks = await list_tasks_by_types(task_types)
    return bool(tasks)


async def user_publish_task_switch(user: User):
    task_types = [
        WelfareTaskType.PUBLISH_CARD,
        WelfareTaskType.FIRST_PUBLISH_CARD,
    ]
    tasks = await list_tasks_by_types(task_types)
    if not tasks:
        return False
    if user.created_at > date_util.utc_now() - datetime.timedelta(days=3):
        return False
    return True


async def user_publish_task_tips(
    user: User, has_reward: bool, language: str = Language.ZH.value
):
    task_type = (
        WelfareTaskType.PUBLISH_CARD
        if has_reward
        else WelfareTaskType.FIRST_PUBLISH_CARD
    )
    reward_task = await list_tasks(task_type)
    publish_card_tips = ""
    if (
        reward_task
        and user.created_at.timestamp() + 3600 * 24 > date_util.now(0).timestamp()
    ):
        reward_task = []
    publish_card_tips = reward_task[0].desc if reward_task else ""
    if not publish_card_tips:
        return ""
    publish_card_tips = _tl(publish_card_tips, language)
    return publish_card_tips


# end==================  publish task service end


async def bot_receive_benefit(user: User):
    log.info(f"bot_receive_benefit:user_id{user.id}")
    task_list = await list_tasks_by_types([WelfareTaskType.DIRECT_RECEIVE])
    task_map = {x.task_id: x for x in task_list}
    if not task_list:
        return "没有任务奖励可领取，请稍后再试"
    tips = []
    await complete_task(user.id, task_list[0].task_id)
    user_tasks = await display_user_receive_tasks(user.id, task_map)
    if not user_tasks:
        return "没有任务奖励可领取，请稍后再试"
    user_task = user_tasks[0]
    if user_task.status == TaskStatus.DONE.value:
        tips = f"免费权益已发放，{user_task.subTitle}\n奖励列表：{user_task.desc}"
    else:
        log.error(
            f"bot_receive_benefit status error:user_id{user.id},status:{user_task.status}"
        )
        tips = "领取失败，请稍后尝试，或者请联系客服"
    return tips


async def receive_benefit(user: User):
    log.info(f"receive_benefit:user_id{user.id}")
    task_list = await list_tasks_by_types([WelfareTaskType.DIRECT_RECEIVE])
    if not task_list:
        return False
    tips = []
    ret, rewards = await complete_task(user.id, task_list[0].task_id)
    return ret
