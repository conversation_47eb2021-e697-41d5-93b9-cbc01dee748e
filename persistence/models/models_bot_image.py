
from datetime import datetime, timezone
from enum import Enum, IntEnum, StrEnum
from tortoise.models import Model
from tortoise import fields

class ImgGenStatus(str,Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class BaseTimeModel(Model):
    created_at = fields.DatetimeField(auto_now_add=True,index=True)
    updated_at = fields.DatetimeField(auto_now=True,index=True)
    class Meta:
        abstract = True

class BotImgGenTask(BaseTimeModel):
    id = fields.IntField(pk=True)
    tg_id = fields.BigIntField(index=True)
    user_id = fields.BigIntField(index=True,default=0)
    prompt = fields.TextField()
    req_json = fields.JSONField(default={})
    status = fields.CharEnumField(ImgGenStatus,default=ImgGenStatus.PENDING)
    priority = fields.IntField(default=0,description="优先级,越大越优先")
    started_at = fields.DatetimeField(null=True)
    completed_at = fields.DatetimeField(null=True)
    gen_result = fields.JSONField(null=True)
    error_message = fields.TextField(null=True)
    retry_count = fields.IntField(default=0,description="重试次数")
    # 新增字段
    locked_by = fields.CharField(max_length=64, null=True)  
    locked_at = fields.DatetimeField(null=True)  
    last_heartbeat = fields.DatetimeField(null=True)  

    class Meta:
        table = "bot_img_gen_task"


class BotImgBasicProfile(BaseTimeModel):
    id = fields.IntField(pk=True)
    tg_id = fields.BigIntField(index=True)
    user_id = fields.BigIntField(index=True,default=0)
    img_gen_profile = fields.JSONField(default={})
    class Meta:
        table = "bot_img_basic_profile"
        unique_together = (("tg_id", "user_id"),)
        description = "用户画图基础信息,包括style,清晰度dpx,隐私等"
