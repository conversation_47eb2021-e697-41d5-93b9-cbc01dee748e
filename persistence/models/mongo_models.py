from datetime import datetime
from typing import Optional
from pydantic import BaseModel

from common.common_constant import AuditStatus
from utils import date_util


# db model
class RolePublishHistory(BaseModel):

    user_id: int
    mode_type: str
    mode_target_id: int
    publish_version: int
    submit_at: datetime
    published_at: datetime
    publish_data: dict  # 该次审核最终发布的数据
    original_data: dict = {}  # 该次审核时作者提交的原始数据
    status: str = AuditStatus.APPROVED.value
    reason: str = ""

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class RoleBroadcastHistory(BaseModel):
    role_id: int
    chat_id: int
    message_id: int
    photo_file_id: str = ""
    caption: str = ""
    ai_rewrite: bool = False

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class VolEngineContextHistory(BaseModel):
    context_id: str  # Unique identifier for the context
    cache_id: str  # Unique identifier for the cache
    third_model_id: str  # The model ID from the third-party service
    request_model: str  # The model used for the request
    cache_content: str = ""  # The content of the message
    context_id: str = ""  # The context ID for the request
    user_id: int = 0
    role_id: int = 0
    message_id: str = ""
    conv_id: str = ""

    created_at: datetime = date_util.now()
    updated_at: datetime = date_util.now()