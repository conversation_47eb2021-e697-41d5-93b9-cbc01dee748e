from typing import Any, Dict, List
import logging

from common.common_constant import ChatModeType
from common.models.chat_model import (
    ChatH<PERSON>ory,
    ChatHistoryStatus,
    ChatHistoryType,
    ChatTipsHistory,
)
from common.role_model import RecentChat
from persistence import mongo_client
from persistence.presets import <PERSON><PERSON><PERSON>
from utils import json_util


logger = logging.getLogger(__name__)

collection = mongo_client.TavernCollection.CHAT_HISTORY
tips_collection = mongo_client.TavernCollection.CHAT_TIPS_HISTORY


async def insert_message(message: ChatHistory):
    await collection.insert_one(message.model_dump())


async def get_last_message_id_by_conv_id(user_id: int, conversation_id: str):
    # 只获取message_id
    result = (
        await collection.find(
            {
                "user_id": user_id,
                "conversation_id": conversation_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            },
            {"message_id": 1},
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    if len(result) == 0:
        return ""
    return result[0]["message_id"]


async def get_last_message_by_conv_id(
    user_id: int, conversation_id: str
) -> ChatHistory | None:
    result = (
        await collection.find(
            {
                "user_id": user_id,
                "conversation_id": conversation_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            },
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    if len(result) == 0:
        return None
    return ChatHistory(**json_util.remove_null_dict(result[0]))


async def get_history(
    user_id: int, role_id: int, conversation_id: str
) -> list[ChatHistory]:
    history: List[Dict[str, Any]] = await collection.find(
        {
            "user_id": user_id,
            "role_id": role_id,
            "conversation_id": conversation_id,
            "status": {"$ne": ChatHistoryStatus.DELETED},
        }
    ).to_list(None)
    history = [json_util.remove_null_dict(x) for x in history]
    return [ChatHistory(**x) for x in history]


async def list_user_history(conversation_id: str) -> list[ChatHistory]:
    history: List[Dict[str, Any]] = await collection.find(
        {
            "conversation_id": conversation_id,
            "status": {"$ne": ChatHistoryStatus.DELETED},
        }
    ).to_list(None)
    history = [json_util.remove_null_dict(x) for x in history]
    return [ChatHistory(**x) for x in history]


async def list_by_message_ids(message_ids: list[str]):
    history: List[Dict[str, Any]] = await collection.find(
        {
            "message_id": {"$in": message_ids},
            "status": {"$ne": ChatHistoryStatus.DELETED},
        }
    ).to_list(None)
    history = [json_util.remove_null_dict(x) for x in history]
    return [ChatHistory(**x) for x in history]


async def get_latest_by_message_id(message_id: str):
    history = (
        await collection.find(
            {
                "message_id": message_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            }
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(None)
    )
    if not history:
        return None
    return ChatHistory(**json_util.remove_null_dict(history[0]))


async def get_by_message_id_and_version(message_id: str, version: int):
    history = await collection.find_one(
        {
            "message_id": message_id,
            "version": version,
            "status": {"$ne": ChatHistoryStatus.DELETED},
        }
    )
    if not history:
        return None
    return ChatHistory(**json_util.remove_null_dict(history))


async def get_recent_roles(user_id: int) -> list[int]:
    fetch_result = await collection.aggregate(
        [
            {"$match": {"user_id": user_id, "mode_type": ChatModeType.SINGLE.value}},
            {"$sort": {"_id": -1}},
            {"$group": {"_id": "$role_id", "oid": {"$first": "$_id"}}},
            {"$sort": {"oid": -1}},
            {"$project": {"_id": "$_id"}},
            {"$limit": 15},
        ]
    ).to_list(None)
    return [x["_id"] for x in fetch_result]


async def get_last_conversation_id(user_id: int, role_id: int) -> str:
    result = (
        await collection.find(
            {
                "user_id": user_id,
                "role_id": role_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            }
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    if len(result) == 0:
        return ""
    return result[0]["conversation_id"]


async def get_latest_conversation_id(
    user_id: int, mode_type: str, target_id: int
) -> str:
    result = (
        await collection.find(
            {
                "user_id": user_id,
                "mode_type": mode_type,
                "mode_target_id": target_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            }
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    if len(result) == 0:
        return ""
    return result[0]["conversation_id"]


async def get_group_latest_conversation_id(
    user_id: int, mode_type: str, target_id: int
) -> str:
    result = (
        await collection.find(
            {
                "user_id": user_id,
                "mode_type": mode_type,
                "mode_target_id": target_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            }
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    if len(result) == 0:
        return ""
    return result[0]["conversation_id"]


async def get_single_latest_conversation_id(user_id: int, role_id: int) -> str:
    result = (
        await collection.find(
            {
                "user_id": user_id,
                "role_id": role_id,
                "mode_type": {"$ne": "group"},
                "status": {"$ne": ChatHistoryStatus.DELETED},
            }
        )
        .sort("timestamp", -1)
        .limit(1)
        .to_list(1)
    )
    if len(result) == 0:
        return ""
    return result[0]["conversation_id"]


async def update_message_status(
    user_id: int,
    message_ids: list[str],
    status: int,
) -> int:
    result = await collection.update_many(
        {
            "user_id": user_id,
            "message_id": {"$in": message_ids},
        },
        {"$set": {"status": status}},
    )
    return result.modified_count


async def update_status_by_mode_type_and_target_id(
    user_id: int, mode_type: str, mode_target_id: int, status: int
):
    result = await collection.update_many(
        {
            "user_id": user_id,
            "mode_type": mode_type,
            "mode_target_id": mode_target_id,
        },
        {"$set": {"status": status}},
    )
    return result.modified_count

async def update_content_by_message_id_and_version(
    message_id: str, version: int, content: str
):
    result = await collection.update_one(
        {
            "message_id": message_id,
            "version": version,
        },
        {
            "$set": {
                "content": content,
            }
        },
    )
    return result.modified_count


async def update_by_continue(chat_history: ChatHistory):
    result = await collection.update_one(
        {
            "message_id": chat_history.message_id,
            "version": chat_history.version,
        },
        {
            "$set": {
                "content": chat_history.content,
                "timestamp": chat_history.timestamp,
                "input_token_count": chat_history.input_token_count,
                "output_token_count": chat_history.output_token_count,
                "duration": chat_history.duration,
                "chat_continue": chat_history.chat_continue,
            }
        },
    )
    return result.modified_count


async def update_voice_url_by_message_id_and_version(
    message_id: str, version: int, voice_url: str
):
    result = await collection.update_one(
        {
            "message_id": message_id,
            "version": version,
        },
        {
            "$set": {
                "voice_url": voice_url,
            }
        },
    )
    return result.modified_count


async def delete_all_by_user_id_for_admin(user_id: int):
    result = await collection.delete_many(
        {
            "user_id": user_id,
        }
    )
    return result.deleted_count


async def get_history_by_conversation_id(conversation_id: str):
    history = await collection.find(
        {
            "conversation_id": conversation_id,
            "status": {"$ne": ChatHistoryStatus.DELETED},
        }
    ).to_list(None)

    history = [json_util.remove_null_dict(x) for x in history]
    return [ChatHistory(**x) for x in history]


async def get_recent_roles_and_group(user_id: int):
    fetch_ret = await collection.aggregate(
        [
            {
                "$match": {
                    "user_id": user_id,
                    "status": {"$ne": ChatHistoryStatus.DELETED},
                }
            },
            {
                "$group": {
                    "_id": {
                        "mode_type": "$mode_type",
                        "mode_target_id": "$mode_target_id",
                    },
                    "timestamp": {"$max": "$timestamp"},
                    "record_count": {"$sum": 1},
                }
            },
            {"$sort": {"timestamp": -1}},
            {
                "$project": {
                    "_id": 0,
                    "mode_type": "$_id.mode_type",
                    "mode_target_id": "$_id.mode_target_id",
                    "timestamp": "$timestamp",
                    "record_count": "$record_count",
                }
            },
        ]
    ).to_list(None)
    return [RecentChat(**x) for x in fetch_ret]


async def count_ai_msg_by_role_id(user_id: int, role_id: int) -> int:
    return await collection.count_documents(
        {"user_id": user_id, "type": ChatHistoryType.AI.value, "role_id": role_id}
    )


async def count_human_msg(user_id: int) -> int:
    return await collection.count_documents(
        {"user_id": user_id, "type": ChatHistoryType.HUMAN.value}
    )


async def count_ai_msg(user_id: int) -> int:
    return await collection.count_documents(
        {"user_id": user_id, "type": ChatHistoryType.AI.value}
    )


async def count_by_user_id_conv_id(user_id: int, conv_id: str) -> int:
    return await collection.count_documents(
        {"user_id": user_id, "conversation_id": conv_id}
    )


async def list_by_export(
    user_id: int, mode_type: str, mode_target_id: int, conversation_id: str
) -> list[ChatHistory]:
    history: List[Dict[str, Any]] = await collection.find(
        {
            "user_id": user_id,
            "mode_type": mode_type,
            "mode_target_id": mode_target_id,
            "conversation_id": conversation_id,
            "status": {"$ne": ChatHistoryStatus.DELETED},
        }
    ).to_list(None)
    history = [json_util.remove_null_dict(x) for x in history]
    return [ChatHistory(**x) for x in history]


async def list_all_model_by_conv_id(user_id: int, conversation_id: str) -> list[str]:
    history: List[Dict[str, Any]] = await collection.find(
        {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "model": {"$exists": True, "$ne": None, "$ne": ""},
        },
        {"model": 1, "timestamp": 1, "_id": 0},
    ).to_list(None)
    tips_history: List[Dict[str, Any]] = await tips_collection.find(
        {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "model": {"$exists": True, "$ne": None, "$ne": ""},
        },
        {"model": 1, "timestamp": 1, "_id": 0},
    ).to_list(None)
    history.extend(tips_history)
    history.sort(key=lambda x: x["timestamp"])
    return [x["model"] for x in history if x["model"]]


async def list_tips_all_model_by_conv_id(
    user_id: int, conversation_id: str
) -> list[str]:
    history: List[Dict[str, Any]] = await tips_collection.find(
        {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "model": {"$exists": True, "$ne": None, "$ne": ""},
        },
        {"model": 1, "timestamp": 1, "_id": 0},
    ).to_list(None)
    history.sort(key=lambda x: x["timestamp"])
    return [x["model"] for x in history if x["model"]]


async def list_tips_water_models(user_id: int, conversation_id: str) -> list[str]:
    history: List[Dict[str, Any]] = await tips_collection.find(
        {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "model": {"$exists": True, "$ne": None, "$ne": ""},
        },
        {"model": 1, "timestamp": 1, "_id": 0, "select_water_model": 1},
    ).to_list(None)
    history.sort(key=lambda x: x["timestamp"])
    ret = []
    for x in history:
        select_water_model = x.get("select_water_model")
        model = x.get("model")
        if not select_water_model and not model:
            continue
        ret.append(select_water_model if select_water_model else model)
    return ret


async def list_water_models(user_id: int, conversation_id: str) -> list[str]:
    history: List[Dict[str, Any]] = await collection.find(
        {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "model": {"$exists": True, "$ne": None, "$ne": ""},
            "type": ChatHistoryType.AI.value,
        },
        {"model": 1, "timestamp": 1, "_id": 0, "select_water_model": 1},
    ).to_list(None)
    history.sort(key=lambda x: x["timestamp"])
    ret = []
    for x in history:
        select_water_model = x.get("select_water_model")
        model = x.get("model")
        if not select_water_model and not model:
            continue
        ret.append(select_water_model if select_water_model else model)
    return ret


async def list_chat_all_model_by_conv_id(
    user_id: int, conversation_id: str
) -> list[str]:
    history: List[Dict[str, Any]] = await collection.find(
        {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "model": {"$exists": True, "$ne": None, "$ne": ""},
        },
        {"model": 1, "timestamp": 1, "_id": 0},
    ).to_list(None)
    history.sort(key=lambda x: x["timestamp"])
    return [x["model"] for x in history if x["model"]]


async def list_chat_llm_models(
    user_id: int, conversation_id: str, scenario: int
) -> list[str]:
    if scenario == Scenario.CHAT.value:
        return await list_water_models(user_id, conversation_id)
    elif scenario == Scenario.IMPERSONATE.value:
        return await list_tips_water_models(user_id, conversation_id)
    return []


async def insert_chat_tips(chat_tips: ChatTipsHistory):
    await tips_collection.insert_one(chat_tips.model_dump())


async def list_conversation_ids(user_id: int, mode_type: str, mode_target_id: int):
    history = await collection.aggregate(
        [
            {
                "$match": {
                    "user_id": user_id,
                    "mode_type": mode_type,
                    "mode_target_id": mode_target_id,
                    "status": {"$ne": ChatHistoryStatus.DELETED},
                }
            },
            {
                "$group": {
                    "_id": "$conversation_id",
                    "min_timestamp": {"$min": "$timestamp"},
                    "max_timestamp": {"$max": "$timestamp"},
                }
            },
            {
                "$project": {
                    "conversation_id": "$_id",
                    "min_timestamp": 1,
                    "max_timestamp": 1,
                    "_id": 0,
                }
            },
        ]
    ).to_list(None)
    return history


async def count_by_admin(user_id: int):
    count = await collection.count_documents({"user_id": user_id})
    return count


async def update_user_id_by_admin(old_user_id: int, new_user_id: int):
    result = await collection.update_many(
        {
            "user_id": old_user_id,
        },
        {"$set": {"user_id": new_user_id}},
    )
    return result.modified_count


async def get_user_history(user_id: int, limit: int):
    history: List[Dict[str, Any]] = (
        await collection.find(
            {
                "user_id": user_id,
                "status": {"$ne": ChatHistoryStatus.DELETED},
            }
        )
        .limit(limit)
        .to_list(None)
    )
    history = [json_util.remove_null_dict(x) for x in history]
    return [ChatHistory(**x) for x in history]
