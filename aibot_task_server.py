import logging
import os
import time
from fastapi import FastAP<PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from common import loggers
from common.common_constant import Env
from tasks.aibot import cal_group_msg_task,send_group_msg_task,del_group_msg_task
from utils import env_util

load_dotenv()

import sentry_sdk
import uvicorn

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

# Initialize FastAPI app and BotManager
sentry_sdk.init(
    dsn="https://<EMAIL>:8443/10",

    traces_sample_rate=0.05,
)

# cors_origins = os.getenv("CORS_ORIGINS", "").split(",")

app = FastAPI()
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=cors_origins,
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
#     expose_headers=[
#         "Conversation-Id",
#         "Message-Id",
#         "Message-Version",
#         "Human-Message-Id",
#     ],
# )
scheduler = AsyncIOScheduler()

scheduler_auto = AsyncIOScheduler()

router = APIRouter()

@router.get("/tasks")
async def tasks():
    tasks = scheduler.get_jobs()
    r = []
    for task in tasks:
        job = {'name': task.name, 'func': task.func_ref,
               'next_run_time': task.next_run_time.strftime('%Y-%m-%d %H:%M:%S')}
        r.append(job)

    return r

@router.get("/task/run")
async def task(task_name: str):
    jobs = scheduler.get_jobs()
    for job in jobs:
        if job.name == task_name:
            await job.func()
            return {"status": "ok"}
    return {"status": "error", "msg": "task not found"}


app.include_router(router)


Tortoise.init_models(["persistence.models.models_bot_group","persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models_bot_group","persistence.models.models"]},
    generate_schemas=True,
)


@app.on_event("startup")
async def startup_event():

    # 每小时执行一次消息统计
    scheduler.add_job(cal_group_msg_task.cal_g_user_hours_msg_task, CronTrigger(minute=5))
    
    # scheduler.add_job(cal_group_msg_task.cal_g_user_hours_msg_task, CronTrigger(minute=45)) 
    
    # #每小时发送上一个小时的消息统计
    scheduler.add_job(cal_group_msg_task.send_last_hour_msg_stat_task, CronTrigger(minute=30))

    #0点30分发送昨天天的消息统计
    scheduler.add_job(cal_group_msg_task.send_last_day_msg_stat_task, CronTrigger(hour=0,minute=30))
    
    
    #执行邀请的统计
    scheduler.add_job(cal_group_msg_task.cal_user_invite_share_task, CronTrigger(minute=15))
    
    #执行分享的统计
    scheduler.add_job(cal_group_msg_task.cal_user_share_stat_task, CronTrigger(minute=20))
    
    #每天早上发送分享的统计
    scheduler.add_job(cal_group_msg_task.send_last_day_share_stat_task, CronTrigger(hour=0,minute=25))
    
    #每天早上发送邀请的统计
    scheduler.add_job(cal_group_msg_task.send_last_day_invite_stat_task, CronTrigger(hour=0,minute=28))
    
    #定时删除过期的的图片消息
    
    #load task
    scheduler.add_job(del_group_msg_task.load_group_photo_msg_task, CronTrigger(second=30))
    
    #del task
    scheduler.add_job(del_group_msg_task.del_group_photo_msg_task, CronTrigger(second=10))

    # #晚上10点执行
    # scheduler.add_job(init_config_task.init_role_orders, CronTrigger(hour=21, minute=58))

    scheduler.start()

    #定时发送群消息
    scheduler_auto.add_job(send_group_msg_task.load_group_msg_task, CronTrigger(second=10),args=[scheduler_auto])

    scheduler_auto.start()
    
    
    

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9220)
