import functools
import logging
from typing import Any, Optional
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from aiogram.exceptions import TelegramForbiddenError
from aiogram.exceptions import TelegramBadRequest
from openai import APIConnectionError, APIError

from common.common_constant import ErrorCode, LiteLlmErrorType
from utils.response_util import CommonResponse

log = logging.getLogger(__name__)


class ParamException(HTTPException):
    def __init__(self, status_code: int = 400, detail: Any = "param error"):
        super().__init__(status_code=status_code, detail=detail)
class InnerException(HTTPException):
    def __init__(self, status_code: int = 500, detail: Any = "inner error"):
        super().__init__(status_code=status_code, detail=detail)


class VerifyException(HTTPException):
    def __init__(self, status_code: int = 200, detail: Optional[CommonResponse] = None):
        super().__init__(
            status_code=status_code,
            detail=detail.model_dump_json() if detail else {},
        )


def verify_exception(
    error_code: int = ErrorCode.COMMON_ERROR.value,
    message: str = "",
    error_key: str = "",
):
    return VerifyException(
        status_code=200,
        detail=CommonResponse(
            error_code=error_code, message=message, error_key=error_key, data=None
        ),
    )


def common_json_error(message: str, error_code: int = -1):
    return {"error_code": error_code, "message": message}


def http_forbidden(message: str):
    return HTTPException(status_code=403, detail=common_json_error(message))

def inner_error(message: str):
    return InnerException(detail=common_json_error(message, error_code=500))

def http_auth(message: str):
    return HTTPException(
        status_code=401,
        detail=common_json_error(message),
        headers={"WWW-Authenticate": "Bearer"},
    )


def param_error(message: str):
    return ParamException(detail=common_json_error(message))


def async_catch_exception(func, *args, **kwargs):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            log.error("async_catch_exception error: %s", e)
            return None

    return wrapper


# 忽略Tg异常
def async_ignore_catch_tg_exception(func, *args, **kwargs):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except TelegramForbiddenError as e:
            log.warning("async_catch_exception error: %s", e)
            return None
        except TelegramBadRequest as e:
            log.warning("async_catch_exception error: %s", e)
            return None

    return wrapper


def async_ignore_catch_exception(func, *args, **kwargs):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            log.warning("async_catch_exception error: %s", e)
            return None

    return wrapper


# def catch_exception(func):
#     @functools.wraps(func)
#     def wrapper(*args, **kwargs):
#         try:
#             return func(*args, **kwargs)
#         except Exception as e:
#             log.error("catch_exception error: %s", e)
#             return None

#     return wrapper

# def catch_telegram_exception(func):
#     @functools.wraps(func)
#     def wrapper(*args, **kwargs):
#         try:
#             return func(*args, **kwargs)
#         except Exception as e:
#             log.error("catch_telegram_exception error: %s", e)
#             return None

#     return wrapper


def llm_exception_to_type(e: Exception):
    error_type = LiteLlmErrorType.REQUEST_ERROR.value
    if isinstance(e, APIConnectionError):
        error_type = LiteLlmErrorType.CONNECTION_ERROR.value
    elif isinstance(e, TimeoutError):
        error_type = LiteLlmErrorType.TIMEOUT.value
    elif isinstance(e, StopAsyncIteration):
        error_type = LiteLlmErrorType.STOP_ERROR.value
    elif isinstance(e, APIError):
        error_type = LiteLlmErrorType.API_ERROR.value
    return error_type
