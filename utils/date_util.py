from datetime import datetime, timedelta, timezone

import pytz


# 时区信息，这里使用UTC时间存储，转换过程中，需要先查看是否有时区信息，没有则加上


# 获取当前时间，参数可以动态传递时区信息
def now(tz_offset: int = 0):
    ret = datetime.now(timezone.utc)
    if tz_offset != 0:
        ret = ret.astimezone(timezone(timedelta(hours=tz_offset)))
    return ret


def utc_now():
    return datetime.now(timezone.utc)


def utc_day_start():
    return datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)


def utc8_to_utc(dt: datetime):
    if dt.tzinfo is None:
        dt = pytz.timezone("Asia/Shanghai").localize(dt)
    utc_time = dt.astimezone(pytz.utc)
    return utc_time


def utc2utc8(dt):
    if dt.tzinfo is None:
        dt = pytz.utc.localize(dt)
    china_time = dt.astimezone(pytz.timezone("Asia/Shanghai"))
    return china_time


def utc8_day_start(days=0, hours=0):
    return utc2utc8(utc_day_start()) + timedelta(days=days) + timedelta(hours=hours)


def utc8_day_end(days=0, hours=0):
    return (
        utc8_day_start(days=days, hours=hours)
        + timedelta(days=1)
        - timedelta(seconds=1)
    )


# utc8_str_tz = china_time.strftime("%Y-%m-%d %H:%M:%S")
# dt.astimezone(timezone.utc)
# return dt.astimezone(pytz.timezone("Asia/Shanghai"))


def add_utc_zone(dt):
    return dt.astimezone(timezone.utc)


def diff_days(start: datetime, end: datetime):
    return (end - start).days


def timestamp2datetime(timestamp: int, utc: timezone = timezone.utc):
    return datetime.fromtimestamp(timestamp, utc)


def datetime2str(dt: datetime, format="%Y-%m-%d %H:%M:%S"):
    return dt.strftime(format)


def datetime2utc8str(dt: datetime, format="%Y-%m-%d %H:%M"):
    return datetime2str(utc2utc8(dt), format)


def datetime2timestamp(dt: datetime):
    dt = dt.astimezone(timezone.utc)
    return int(dt.timestamp())


def timestamp2str(timestamp: int, format="%Y-%m-%d %H:%M:%S"):
    mid = timestamp2datetime(timestamp)
    return datetime2str(mid, format)


def timestamp2utc8str(timestamp: int, format="%Y-%m-%d %H:%M:%S"):
    mid = timestamp2datetime(timestamp)
    return datetime2str(utc2utc8(mid), format)


def week_start(tz_offset: int = 0, weeks: int = 0):
    ret = datetime.now(timezone.utc)
    if tz_offset != 0:
        ret = ret.astimezone(timezone(timedelta(hours=tz_offset)))
    ret = ret.replace(hour=0, minute=0, second=0, microsecond=0)
    ret = ret - timedelta(days=ret.weekday()) + timedelta(days=weeks * 7)
    return ret


def week_end(tz_offset: int = 0, weeks: int = 0):
    ret = week_start(tz_offset, weeks)
    ret = ret + timedelta(days=7) - timedelta(seconds=1)
    return ret


def month_start(tz_offset: int = 0, months: int = 0):
    ret = datetime.now(timezone.utc)
    if tz_offset != 0:
        ret = ret.astimezone(timezone(timedelta(hours=tz_offset)))
    ret = ret.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    if months > 0:
        ret = ret+ timedelta(days=months * 31)
        ret = ret.replace(day=1)
        return ret
    ret = ret+ timedelta(days=31)
    ret = ret.replace(day=1)
    ret = ret - timedelta(days=1)
    ret = ret + timedelta(days=months * 31)
    return ret.replace(day=1)

def month_end(tz_offset: int = 0,months: int = 0):
    ret = month_start(tz_offset, months)
    ret = ret + timedelta(days=31)
    ret = ret.replace(day=1)
    ret = ret - timedelta(seconds=1)
    return ret
def str2datetime(date_str: str, format="%Y-%m-%d %H:%M:%S"):
    return datetime.strptime(date_str, format)
