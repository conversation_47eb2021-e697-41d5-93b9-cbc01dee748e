from datetime import timedelta
import os
from dotenv import load_dotenv
load_dotenv()

TMA_URL=os.environ['TMA_URL']
TMA_DIRECT_URL=os.environ['TMA_DIRECT_URL']
CHAT_BOT_URL=os.environ['CHAT_BOT_URL']
CHAT_BOT_HANDLE=f'@{CHAT_BOT_URL.split('/')[-1]}'

TMA_BOT_URL=os.environ['TMA_BOT_URL']
TMA_HANDLE=f'@{TMA_BOT_URL.split('/')[-1]}'

P91PAY_HOST=os.environ['91PAY_HOST']
P91PAY_APP_ID=os.environ['91PAY_APP_ID']
P91PAY_APP_KEY=os.environ['91PAY_APP_KEY']

TMPAY_HOST=os.environ['TMPAY_HOST']
TMPAY_APP_ID=os.environ['TMPAY_APP_ID']
TMPAY_APP_KEY=os.environ['TMPAY_APP_KEY']

GROUP_HELP_BOT_ID=int(os.environ['GROUP_HELP_BOT_ID'])

PHOTO_ROLES=os.environ['PHOTO_ROLES'].split(',')
DEFAULT_PHOTO_ROLE=os.environ['DEFAULT_PHOTO_ROLES'].split(',')

COMPETITOR_LINKS=os.environ['COMPETITOR_LINKS'].split(',')

ROLE_SUB_CHANNEL_STRS=os.environ['ROLE_SUB_CHANNELS']
ROLE_SUB_CHANNELS = [x.split(':') for x in ROLE_SUB_CHANNEL_STRS.split(',')]

TMA_URL_FOR_ROLE_CHANNEL=os.environ['TMA_URL_FOR_ROLE_CHANNEL']
CHAT_URL_FOR_ROLE_CHANNEL=os.environ['CHAT_URL_FOR_ROLE_CHANNEL']

CHECK_IN_GROUP_OLD_ID=int(os.environ.get('CHECK_IN_GROUP_OLD_ID', '0'))
CHECK_IN_GROUP_ID=int(os.environ['TARGET_JOIN_GROUP_ID'])

TARGET_BACKUP_JOIN_GROUP_ID=int(os.environ.get('TARGET_BACKUP_JOIN_GROUP_ID', '0'))

SFW_BOT_IDS=[int(bid) for bid in os.environ.get('SFW_BOT_IDS', '').split(',')]

WEB_VERSION_URL=os.environ['WEB_URL']

LOGIN_BOT_TOKEN=os.environ['LOGIN_BOT_TOKEN']

SFW_TMA_IDS=os.environ.get('SFW_TMA_IDS', '').split(',')

USER_SHARE_CHAT_BOT=os.environ['USER_SHARE_CHAT_BOT']

USER_SHARE_TMA_BOT=os.environ['USER_SHARE_TMA_BOT']

RECHARGE_PROXY_VAL=os.environ.get('RECHARGE_PROXY')

if RECHARGE_PROXY_VAL:
    RECHARGE_PROXY = {
        'http': RECHARGE_PROXY_VAL,
        'https': RECHARGE_PROXY_VAL
    }
else:
    RECHARGE_PROXY = None

NSFW_ROLE_CHANNELS=os.environ.get('NSFW_ROLE_CHANNELS', '').split(',')

WELFARE_CHANNEL_ID=int(os.environ['WELFARE_CHANNEL_ID'])
WELFARE_BACKUP_CHANNEL_ID=int(os.environ.get('WELFARE_BACKUP_CHANNEL_ID', '0'))

USER_SHARE_GROUP_IDS=[id_str for id_str in os.environ['USER_SHARE_GROUP_IDS'].split(',')]

MAX_SHARE_COUNT=int(os.environ['MAX_SHARE_COUNT'])

BOTS_TO_TRANSFER=os.environ.get('BOTS_TO_TRANSFER', None)
if BOTS_TO_TRANSFER:
    BOT_IDS_TO_TRANSFER=[int(bid) for bid in BOTS_TO_TRANSFER.split(',')]
else:
    BOT_IDS_TO_TRANSFER=[]

_ROLE_CHANNELS_WITH_NAME=os.environ.get('ROLE_CHANNELS_WITH_NAME', None)
if _ROLE_CHANNELS_WITH_NAME:
    ROLE_CHANNEL_NAME_MAP={x.split(':')[0]:x.split(':')[1] for x in _ROLE_CHANNELS_WITH_NAME.split(',')} 
else:
    ROLE_CHANNEL_NAME_MAP={}

_ROLE_CHANNELS_TAG_WITH_NAME=os.environ.get('ROLE_CHANNELS_TAG_WITH_NAME', None)
if _ROLE_CHANNELS_TAG_WITH_NAME:
    ROLE_CHANNEL_TAG_WITH_NAME=_ROLE_CHANNELS_TAG_WITH_NAME.split(',')
else:
    ROLE_CHANNEL_TAG_WITH_NAME=[]

DIFY_URL=os.environ['DIFY_URL']
DIFY_ROLE_REWRITE_TOKEN=os.environ['DIFY_ROLE_REWRITE_TOKEN']

ROLE_CHANNEL_ADV_ID=int(os.environ['ROLE_CHANNEL_ADV_ID'])
ROLE_CHANNEL_ADV_CHAT_ID=int(os.environ['ROLE_CHANNEL_ADV_CHAT_ID'])

ADV_ROLE_CHANNEL_CHAT_MAP={int(x.split(':')[0]):int(x.split(':')[1]) for x in os.environ['ADV_ROLE_CHANNEL_CHAT_MAP'].split(',')}

ADV_ROLE_CHAT_CID_MAP={int(x.split(':')[0]):int(x.split(':')[1]) for x in os.environ['ADV_ROLE_CHAT_CID_MAP'].split(',')}

USDT_EVM_RECIPIENT_ADDRESS = os.environ['ALCHEMY_RECIPIENT_ADDRESS']

OKLINK_API_KEY=os.environ['OKLINK_API_KEY']
MORALIS_API_KEY=os.environ['MORALIS_API_KEY']

FFPAY_HOST=os.environ['FFPAY_HOST']
FFPAY_APP_ID=os.environ['FFPAY_APP_ID']
FFPAY_APP_KEY=os.environ['FFPAY_APP_KEY']

SDFKW_HOST=os.environ['SDFKW_HOST']
SDFKW_APP_ID=os.environ['SDFKW_APP_ID']
SDFKW_APP_KEY=os.environ['SDFKW_APP_KEY']

QSZF_HOST=os.environ['QSZF_HOST']
QSZF_APP_ID=os.environ['QSZF_APP_ID']
QSZF_APP_KEY=os.environ['QSZF_APP_KEY']

SJZF_HOST=os.environ['SJZF_HOST']
SJZF_REPLACE_DOMAIN=os.environ['SJZF_REPLACE_DOMAIN']
SJZF_APP_ID=os.environ['SJZF_APP_ID']
SJZF_APP_KEY=os.environ['SJZF_APP_KEY']

JLBZF_HOST=os.environ['JLBZF_HOST']
JLBZF_APP_ID=os.environ['JLBZF_APP_ID']
JLBZF_APP_KEY=os.environ['JLBZF_APP_KEY']

XJTZF_HOST=os.environ['XJTZF_HOST']
XJTZF_APP_ID=os.environ['XJTZF_APP_ID']
XJTZF_APP_KEY=os.environ['XJTZF_APP_KEY']

WEB_INVITE_HOST=os.environ['WEB_INVITE_HOST']

DECAY_RECHARGE_PRODUCT_1=os.environ['DECAY_RECHARGE_PRODUCT_1']
DECAY_RECHARGE_PRODUCT_2=os.environ['DECAY_RECHARGE_PRODUCT_2']