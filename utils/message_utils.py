from datetime import datetime
import logging
import math
import random
from typing import AsyncIterator, Optional
import unicodedata
from langchain_core.messages.base import BaseMessage
from langchain_core.messages import BaseMessageChunk
from litellm import ChatCompletionChunk, CustomStreamWrapper
from common.common_constant import (
    ChatFinishReason,
    ChatModeType,
    Env,
    ErrorKey,
    LiteLlmErrorType,
    LlmModel,
    PresetReplace,
)
from common.models.chat_model import (
    AddWaterMid,
    BuildHistoryQuery,
    ChatHistory,
    ChatHistoryType,
    ChatNextInput,
    LiteLlmIterResponse,
)
from common.models.chat_request import ChatHistoryItem
from persistence.models.models import RegexAffect, RegexOption, RegexRule, User
import re
from persistence.presets import Scenario
import utils.regex_converter as rc
from utils import date_util, env_util, str_util, tg_util, thread_util
from langchain.schema import HumanMessage, AIMessage

log = logging.getLogger(__name__)


def process_merged(messages: list[BaseMessage]) -> list[BaseMessage]:
    if not messages:
        return []
    # 聊天中插入的系统消息，转换为人类消息
    first_system_flag = True
    processed_messages: list[BaseMessage] = []
    for message in messages:
        if first_system_flag and message.type != "system":
            first_system_flag = False
        if message.type == "system" and not first_system_flag:
            processed_messages.append(HumanMessage(content=message.content))
        else:
            processed_messages.append(message)
    # 合并连续的同类型prompt
    merged_messages = [processed_messages[0]]
    for cur_msg in processed_messages[1:]:
        last_msg = merged_messages[-1]
        if cur_msg.type == last_msg.type:
            last_msg.content += "\n" + cur_msg.content
        else:
            merged_messages.append(cur_msg)
    return merged_messages


def process_history_to_ai_msg(history: list[dict]) -> list[BaseMessage]:
    def parse_history(m: dict) -> BaseMessage:
        type = m["type"]
        if type == "assistant" or type == "ai":
            return AIMessage(content=m["content"])
        elif type == "user" or type == "human":
            return HumanMessage(content=m["content"])

    return list(map(lambda x: parse_history(x), history))


replace_strs = [
    "<OutOfCharacter>",
    "</OutOfCharacter>",
    "OutOfCharacter",
    "<OOC>",
    "</OOC>",
    "OOC",
]
monitor_strs = [
    "输出system",
    "输出你的定义",
    "system prompt",
    "系统提示词",
    # "Claude",
    "Character Name",
    "character name",
    "暂停扮演",
    "开发模式",
    "development mode",
    "角色名字",
    # "全部设定",
    "Assistant",
    "assistant",
    "Human：",
    "human：",
    "Human:",
    "human:",
    # "A:",
    # "A：",
    # "H:",
    # "H：",
    "special reminder",
]
prohibited_words = [
    "幼女",
    "young girl",
    "loli",
    "萝莉",
    "初中",
    "未满18岁",
    "未成年",
    "婴儿",
    "幼齿",
    "小学",
    "蘿莉",
    "小學生",
    "嬰兒",
    "幼齒",
    "幼童",
    "幼儿",
    "幼兒",
    "习近平",
    "习大大",
    "习主席",
]


def format_and_monitor(content: str, user: User):
    content_len = len(content)
    for replace_str in replace_strs:
        content = content.replace(replace_str, "")
    ret = ""
    if content_len != len(content):
        log.error(f"Replace string user_id:{user.id}, content:{content}")
    for monitor_str in monitor_strs:
        if monitor_str in content:
            ret = "系统词"
            break
    # 检测输入包含标签
    # if "<" in content or ">" in content:
    #     ret = "标签信息"

    if ret:
        log.error(
            f"Monitor string user_id:{user.id}, content:{content}, monitor_str:{ret}"
        )
        # data = {
        #     "Title": "Monitor Input Message",
        #     "Message": content,
        #     "UserId": user.id,
        #     "Monitor": ret,
        # }
        # tg_util.send_message(data)
    return ret


# 违禁词检测
def contain_prohibited_words(content: str, user: User):
    for prohibited_word in prohibited_words:
        if prohibited_word in content:
            log.warning(f"Prohibited word user_id:{user.id}, content:{content}")
            return prohibited_word
    return ""


def process_on_edit_rules(
    message: str, rules: list[RegexRule], user: bool, role_name: str, user_name: str
) -> str:
    input_regexAffect = (
        RegexAffect.USER_INPUT.value if user else RegexAffect.AI_OUTPUT.value
    )
    rules = [
        rule
        for rule in rules
        if rule.enabled
        and RegexOption.RUN_ON_EDIT.value in rule.options
        and input_regexAffect in rule.affects
    ]
    if len(rules) == 0:
        return message
    for rule in rules:
        rule.regex = str_util.format_char(rule.regex, role_name)
        rule.regex = str_util.format_char(rule.regex, user_name)
    for rule in rules:
        message = run_js_regex(rule.regex, rule.replacement, message)
    return message


# 检查是否需要自动重试
def process_ai_message_on_auto_retry_by_rule(
    message: str, rules: list[RegexRule]
) -> bool:
    ai_rules = [
        rule
        for rule in rules
        if RegexOption.AUTO_RETRY.value in rule.options
        and RegexAffect.AI_OUTPUT.value in rule.affects
    ]
    for rule in ai_rules:
        new_content = run_js_regex(rule.regex, rule.replacement, message)
        if new_content != message:
            return True
    ai_rep_rules = [
        rule
        for rule in rules
        if RegexOption.AUTO_RETRY.value in rule.options
        and RegexAffect.AI_OUTPUT_REP.value in rule.affects
    ]
    message = re.sub(r"<StatusBlock>(.*)", "", message, flags=re.DOTALL)
    for rule in ai_rep_rules:
        new_content = run_js_regex(rule.regex, rule.replacement, message)
        if new_content != message:
            return True
    return False


def process_new_regex_rules(
    rules: list[RegexRule],
    content: str,
    type: str,
    role_name: str,
    user_name: str,
    idx: int,
) -> str:
    for rule in rules:
        max_depth = rule.max_depth if rule.max_depth != -1 else idx
        if type == "human" and RegexAffect.USER_INPUT not in rule.affects:
            continue
        if type == "ai" and RegexAffect.AI_OUTPUT not in rule.affects:
            continue
        if not bool(rule.min_depth <= idx <= max_depth):
            continue

        regex = str_util.format_char(rule.regex, role_name)
        regex = str_util.format_user(regex, user_name)
        content = run_js_regex(rule.regex, rule.replacement, content)
    return content


def run_js_regex(regex: str, replacement: str, content: str):
    try:
        if not regex or not content:
            return content
        if re.findall(r"\$(\d+)", replacement):
            replacement = re.sub(r"\$(\d+)", r"\\\1", replacement)
        reg = rc.of_js_reg(regex)
        content = reg.sub(replacement, content)
        return content
    except Exception as e:
        log.error(f"Regex error: {e}, regex: {regex}, replacement: {replacement}")
        return content


def process_prefix(content: str, role_name: str, is_first_token: bool):
    if not content or not role_name:
        return content
    prefix_list = [
        f"\n'{role_name}：",
        f"\n'{role_name}:",
        f"\n{role_name}：",
        f"\n{role_name}:",
        f"\n<{role_name}>",
    ]
    if is_first_token and content.startswith(">"):
        content = content[1:]
    max_prefix_len = max([len(x) for x in prefix_list])
    if len(content) < max_prefix_len:
        return content
    for prefix in prefix_list:
        if prefix in content:
            content = content.replace(prefix, "\n")
            break
    if is_first_token == True and content.startswith("'"):
        content = content[1:]
    if "{reply}" in content:
        content = content.replace("{reply}", "")
    if "<|im_start|>assistant<|im_sep|>" in content:
        content = content.replace("<|im_start|>assistant<|im_sep|>", "")
    if "<|im_start|>" in content:
        content = content.replace("<|im_start|>", "")
    if "<|im_end|>" in content:
        content = content.replace("<|im_end|>", "")
    # 去掉<|*****|>内容
    content = re.sub(r"<\|.*?\|>", "", content)
    return content


def repair_content(response: str, finish_reason: str = ""):
    if not response:
        return response
    response = re.sub(r"<ca>.*?</ca>", "", response, flags=re.DOTALL)

    response = response.replace("Assistant", "'Assistant'")
    response = response.replace("assistant", "'assistant'")
    split_rets = re.split("<StatusBlock>", response)
    rep_content = split_rets[0].strip()
    sb_content = "<StatusBlock>" + split_rets[1] if len(split_rets) > 1 else ""
    sb_content = sb_content.strip()
    if "<rep>" not in rep_content:
        rep_content = "<rep>\n" + rep_content
    if finish_reason == ChatFinishReason.LENGTH.value:
        return f"{rep_content}\n{sb_content}"

    if "</rep>" not in rep_content:
        rep_content += "\n</rep>"
    if not sb_content:
        return rep_content
    if "</StatusBlock>" in sb_content:
        return f"{rep_content}\n{sb_content}"
    # 计算```数量
    code_count = sb_content.count("```")
    if code_count % 2 == 1:
        return f"{rep_content}\n{sb_content}\n```\n</StatusBlock>"
    return f"{rep_content}\n{sb_content}\n</StatusBlock>"


def remove_prefix_user_name(content: str, user_name: str):
    prefix_list = [f"{user_name}：", f"{user_name}:", f"<{user_name}>"]
    content = content.strip()
    for prefix in prefix_list:
        if content.startswith(prefix):
            return content[len(prefix) :]
    return content


# def process_user_name(
#     content: str,
#     request_user_name: str,
#     display_user_name: str,
# ):
#     return process_user_name_on_display(
#         content=content,
#         request_user_name=request_user_name,
#         display_user_name=display_user_name,
#     )


def process_user_name_on_display(
    content: str,
    request_user_name: str,
    display_user_name: str,
    history_nicknames: list[str],
):
    if request_user_name:
        content = content.replace(request_user_name, display_user_name)
    if request_user_name == "Charlie":
        charlie_list = [
            "Charlie",
            "charlie",
            "查理",
            "查尔斯",
            "查爾斯",
            "Charles",
            "查理斯",
            "charles",
            "CHARLIE",
            "CHARLES",
        ]
        for charlie in charlie_list:
            content = content.replace(charlie, display_user_name)
    if PresetReplace.USER.value in content:
        # 替换掉预设的用户名称
        content = content.replace(PresetReplace.USER.value, display_user_name)
    elif history_nicknames:
        pattern = re.compile("|".join(map(re.escape, history_nicknames)))
        content = pattern.sub(display_user_name, content)
    return content


def process_user_name_on_preset(
    content: str, request_user_name: str, history_nicknames: list[str]
):
    if PresetReplace.USER.value in content:
        # 替换掉预设的用户名称
        content = content.replace(PresetReplace.USER.value, request_user_name)
    elif history_nicknames:
        pattern = re.compile("|".join(map(re.escape, history_nicknames)))
        content = pattern.sub(request_user_name, content)
    return content


def process_user_name_on_save(
    content: str,
    request_user_name: str,
):
    replace_user_name = PresetReplace.USER.value
    if request_user_name in content:
        content = content.replace(request_user_name, replace_user_name)
    if request_user_name == "Charlie":
        charlie_list = [
            "Charlie",
            "charlie",
            "查理",
            "查尔斯",
            "查爾斯",
            "Charles",
            "查理斯",
            "charles",
            "CHARLIE",
            "CHARLES",
        ]
        for charlie in charlie_list:
            content = content.replace(charlie, replace_user_name)
    return content


# 返回给前端时，需要去掉消息类型是 ai 的消息中的角色名前缀
def remove_history_prefix(role_name: str, history: list[dict]):
    prefix = f"{role_name}:"
    for msg in history:
        if msg["type"] != "ai":
            continue
        content = msg["content"].lstrip()
        if content.startswith(prefix):
            msg["content"] = content[len(prefix) :].lstrip()
    return history


def remove_prefix_role_name(content: str, role_name: str):
    if not content or not role_name:
        return content
    prefix_list = [f"{role_name}:", f"{role_name}：", f"<{role_name}>"]
    for prefix in prefix_list:
        if content.startswith(prefix):
            return content[len(prefix) :].lstrip()
    return content


# def contain_first_message(history: list, copywriting: bool) -> bool:
#     if not history:
#         return False
#     if not copywriting and history[0]["type"] == "ai":
#         return True
#     if copywriting and history[0]["type"] == "human":
#         return True
#     return False


async def process_iterator(
    result: AsyncIterator[BaseMessageChunk], input: ChatNextInput
):
    response = ""
    chunk = None
    async for chunk in result:
        response += str(chunk.content)
        if input.output_interrupt:
            break
    if input.output_interrupt:
        raise Exception("Output interrupted")
    finish_reason = ""
    if chunk and chunk.response_metadata:
        finish_reason = chunk.response_metadata.get("finish_reason", "")
    if response and response.endswith("</Status"):
        response += "Block>"
        finish_reason = "stop"
    return response, finish_reason


async def process_iterator_v1(
    result: AsyncIterator[ChatCompletionChunk], input: ChatNextInput
):
    response = ""
    chunk = None
    first_token = True
    finish_reason = ""
    async for chunk in result:
        if chunk.choices and chunk.choices[0].finish_reason and not finish_reason:
            finish_reason = chunk.choices[0].finish_reason
        if (
            not chunk
            or not chunk.choices
            or not chunk.choices[0].delta
            or not chunk.choices[0].delta.content
        ):
            continue

        if first_token:
            first_token = False
            input.timestamp_first_token = int(datetime.now().timestamp())
        response += str(chunk.choices[0].delta.content)
        if input.output_interrupt:
            break
    if input.output_interrupt:
        raise Exception("Output interrupted")
    suffix = calculate_suffix(response)
    response = response + suffix if suffix else response
    finish_reason = parse_finish_reason(finish_reason, response)
    iter_ret = LiteLlmIterResponse(
        response=response,
        finish_reason=finish_reason,
    )

    if chunk and chunk.usage:
        usage = chunk.usage.model_dump()
        cache_create = usage.get("cache_creation_input_tokens", 0)  # type: ignore
        cache_read = usage.get("cache_read_input_tokens", 0)  # type: ignore
        iter_ret.cache_created_tokens = cache_create
        iter_ret.cache_read_tokens = cache_read
    if chunk and chunk.usage and chunk.usage.completion_tokens:
        iter_ret.completion_tokens = chunk.usage.completion_tokens
        iter_ret.prompt_tokens = chunk.usage.prompt_tokens
    return iter_ret


async def process_iterator_v2(
    input: ChatNextInput,
    result: AsyncIterator[ChatCompletionChunk],
    first_chunk: Optional[ChatCompletionChunk] = None,
):
    response = chunk_content(first_chunk) if first_chunk else ""
    chunk = None
    first_token = True
    finish_reason = chunk_finish_reason("", first_chunk) if first_chunk else ""
    async for chunk in result:
        if chunk.choices and chunk.choices[0].finish_reason and not finish_reason:
            finish_reason = chunk.choices[0].finish_reason
        if (
            not chunk
            or not chunk.choices
            or not chunk.choices[0].delta
            or not chunk.choices[0].delta.content
        ):
            continue

        if first_token:
            first_token = False
            input.timestamp_first_token = int(datetime.now().timestamp())
        response += str(chunk.choices[0].delta.content)
        if input.output_interrupt:
            break
    if input.output_interrupt:
        raise Exception("Output interrupted")
    suffix = calculate_suffix(response)
    response = response + suffix if suffix else response
    finish_reason = parse_finish_reason(finish_reason, response)
    iter_ret = LiteLlmIterResponse(
        response=response,
        finish_reason=finish_reason,
    )

    if chunk and chunk.usage:
        usage = chunk.usage.model_dump()
        cache_create = usage.get("cache_creation_input_tokens", 0)  # type: ignore
        cache_read = usage.get("cache_read_input_tokens", 0)  # type: ignore
        iter_ret.cache_created_tokens = cache_create if cache_create else input.created_token
        iter_ret.cache_read_tokens = cache_read if cache_read else input.read_token
    if chunk and chunk.usage and chunk.usage.completion_tokens:
        iter_ret.completion_tokens = chunk.usage.completion_tokens
        iter_ret.prompt_tokens = chunk.usage.prompt_tokens
    return iter_ret


def garbled_check_by_stream(chunk_content: str, pre_count: int, model: str):
    if model != LlmModel.CLAUDE_3_5_HAIKU.value:
        return False, pre_count
    glen = str_util.garbled_code_len(chunk_content)
    pre_count += glen
    if pre_count >= 5:
        return True, pre_count
    return False, pre_count


def garbled_check_by_history(response: str, model: str):
    try:
        if model != LlmModel.CLAUDE_3_5_HAIKU.value:
            return False
        check_content = response if len(response) <= 20 else response[:20]
        if check_content.startswith("<rep>"):
            check_content = check_content[5:]
        glen = str_util.garbled_code_len(check_content)
        if glen >= 2:
            return True
        return False
    except Exception as e:
        log.error(f"garbled_check_by_history error:{e}")
        return False


def garbled_check_by_history_v1(response: str):
    try:
        check_content = response if len(response) <= 200 else response[:200]
        if check_content.startswith("<rep>"):
            check_content = check_content[5:]
        count = 0
        for char in check_content:
            category = unicodedata.category(char)
            if category.startswith("S"):
                name = unicodedata.name(char)
                count += 1 if name == "REPLACEMENT CHARACTER" else 0
        if count >= 2:
            return True
    except Exception as e:
        log.warning(f"garbled_check_by_history_v1 error:{e}")
    return False


def remove_status_block_and_tag(content: str):
    if not content:
        return content
    if "<sp>" in content or "<StatusBlock>" in content:
        # remove all <StatusBlock> content
        content = re.sub(
            r"<StatusBlock>.*?</StatusBlock>", "", content, flags=re.DOTALL
        )
        # remove all <sp> content
        content = re.sub(r"<sp>.*?</sp>", "", content, flags=re.DOTALL)
        # remove all xml tags in fm
    content = re.sub(r"<[^>]+>", "", content)
    return content


def remove_status_block(content: str):
    if not content:
        return content
    if "<StatusBlock>" in content and "</StatusBlock>" in content:
        # remove all <StatusBlock> content
        content = re.sub(
            r"<StatusBlock>.*?</StatusBlock>", "", content, flags=re.DOTALL
        )
    elif "<StatusBlock>" in content:
        # remove all <StatusBlock> content
        content = re.sub(r"<StatusBlock>.*", "", content, flags=re.DOTALL)
    return content


# 检查是否是有效的回复,如果回复中包含大量空格，认为是无效回复
def check_valid_response(response: str):
    try:
        if len(response) < 100:
            return True
        check_content = response[60:100]
        space_count = check_content.count(" ")
        if space_count >= 10:
            return False
        return True
    except Exception as e:
        log.error(f"check_valid_response error:{e}")
        return True


# 处理轻量级聊天的括号，增加换行
def handle_light_chat_brackets(content: str):

    if not content:
        return content

    def replace(message: str):
        pattern = r'(?<!["\'>\n\r^])[\(（]'
        message = re.sub(pattern, r"\n\g<0>", message)
        pattern = r'(?<!["\'>\n\r^])[\)）]'
        message = re.sub(pattern, r"\g<0>\n", message)
        return message

    if "<StatusBlock>" in content:
        content_list = content.split("<StatusBlock>")
        content = replace(content_list[0])
        for i in range(1, len(content_list)):
            content = content + "<StatusBlock>" + replace(content_list[i])
        return content
    return replace(content)


# 快速处理内容，检查是否有<wit>标签，如果有，等待处理<wit>标签
# buffer_content始终缓存 buffer_len长度的内容
def fast_process_content(buffered_content: str, buffer_len: int):
    content_len = len(buffered_content)
    if "<wit>" in buffered_content and "</wit>" not in buffered_content:
        return buffered_content, True

    if "<wit>" in buffered_content and "</wit>" in buffered_content:
        buffered_content = re.sub(
            r"<wit>.*?</wit>", "", buffered_content, flags=re.DOTALL
        )
        return buffered_content, True
    if content_len <= buffer_len:
        return buffered_content, True
    return buffered_content, False


def build_water_mid(input: ChatNextInput) -> AddWaterMid:
    water_mid = AddWaterMid(
        llm_model=input.llm_model,
        add_water=input.add_water,
        preset_model=input.preset_model,
        config_skip_count=input.out_skip_count,
        config_sleep_min=input.out_sleep_min,
        config_sleep_max=input.out_sleep_max,
    )
    return water_mid


async def process_stream_content(
    buffered_content: str,
    input: ChatNextInput,
    is_first_token: bool,
    water_mid: AddWaterMid,
):
    if is_first_token and buffered_content and buffered_content.startswith(">"):
        buffered_content = buffered_content[1:]
    rule_config_len = max(len(input.role_name) + 5, 20)
    buffered_content = process_on_edit_rules(
        buffered_content,
        input.regex_rules,
        False,
        input.role_name,
        input.request_user_name,
    )
    buffered_content = process_prefix(buffered_content, input.role_name, is_first_token)
    buffered_content = process_user_name_on_display(
        buffered_content, input.request_user_name, input.display_user_name, []
    )
    water_mid = await thread_util.skip_now(water_mid)
    if water_mid.skip_output:
        return buffered_content, True

    return fast_process_content(buffered_content, rule_config_len)


# 快速检查是否有报错，如果有报错，直接中断返回(只检查一次)
def fast_process_error_check(input: ChatNextInput, response: str, fast_check: bool):
    if fast_check:
        return True, ""
    if (
        input.request_model == LlmModel.CLAUDE_3_5_HAIKU.value
        and garbled_check_by_history_v1(response)
    ):
        log.warning(
            f"InvalidProcessChatRes,uid:{input.user_id},model:{input.preset_model}"
        )
        return True, ErrorKey.CHAT_CONTENT_ERROR.value
    if "<OOC>" in response:
        log.warning(
            f"InvalidProcessChatRes OocError,uid:{input.user_id},model:{input.preset_model}"
        )
        return True, ErrorKey.CHAT_CONTENT_ERROR.value
    if env_util.get_current_env() != Env.PROD and "0000000" in response:
        log.warning(
            f"InvalidProcessChatRes StagEnv,uid:{input.user_id},model:{input.preset_model}"
        )
        return True, ErrorKey.CHAT_CONTENT_ERROR.value
    return True, ""


def check_invalid_char(response: str):
    check_lang_char = response[:100] if len(response) > 100 else response
    # 不包含中文，直接返回，不做检测
    if not re.search(r"[\u4e00-\u9fa5]", check_lang_char):
        return False
    if re.search(r"[^,，.。\"‘\'\(\):：\r\n!！;；+、·]{100,}", response):
        return True
    return False


def chat_error_check(input: ChatNextInput, response: str) -> LiteLlmErrorType | None:
    response = response.strip()
    response_len = len(response)
    apologize_str = [
        "你好，这个问题我无法回答，很遗憾不能帮助你",
        "抱歉，您的问题我无法识别",
        "抱歉，这个问题未找到相关结果",
        "抱歉，我无法满足该请求",
    ]
    for mid_str in apologize_str:
        if mid_str in response:
            return LiteLlmErrorType.APOLOGIZE
    if input.chat_continue:
        return None

    if (
        not input.chat_continue
        and input.chat_scenario == Scenario.CHAT.value
        and response_len < 20
    ):
        return LiteLlmErrorType.TOO_SHORT

    if (
        input.request_model == LlmModel.CLAUDE_3_5_HAIKU.value
        and garbled_check_by_history_v1(response)
    ):
        return LiteLlmErrorType.GIBBERISH

    if "<OOC>" in response:
        return LiteLlmErrorType.OOC_ERROR

    if check_invalid_char(response):
        return LiteLlmErrorType.INVALID_CHAR

    if not input.chat_continue:
        # 匹配 <rep>和</rep>之间的内容
        main_content = re.search(r"<rep>(.*?)</rep>", response, re.DOTALL)
        main_content = main_content.group(1) if main_content else ""
        if "<rep>" in response and "</rep>" in response and len(main_content) < 20:
            return LiteLlmErrorType.REP_TOO_SHORT
    if env_util.get_current_env() == Env.STAG and "0000000000" in response:
        return LiteLlmErrorType.INVALID_CHAR
    if process_ai_message_on_auto_retry_by_rule(response, input.regex_rules):
        return LiteLlmErrorType.AUTO_RETRY_RULE
    return None


def chat_response_check(input: ChatNextInput, response: str):
    try:
        response = response.strip()
        response_len = len(response)
        uid = input.user_id
        req_m = input.request_model
        mid = re.search(r"(^[:punct:])\1{10}", response)
        if mid:
            mc = mid.group(0)
            log.error(f"ResErr CharErr,uid:{uid},model:{req_m},char:{mc}")

        if (
            not input.chat_continue
            and input.chat_scenario == Scenario.CHAT.value
            and response_len < 30
        ):
            log.error(f"ResErr,Less30,uid:{uid},model:{req_m}")

        # A: \n B:的情况
        check_response = re.sub(r"<StatusBlock>(.*)", "", response, flags=re.DOTALL)
        if (
            f"\n{input.request_user_name}：" in check_response
            or f"\n{input.request_user_name}:" in check_response
        ):
            log.error(
                f"ResponseError,uid:{uid},model:{req_m},role_id:{input.role_id},response:{check_response}"
            )

        # if not response.startswith("<rep>") and not response.startswith("<ca>") and not response.startswith("<output>"):
        #     log.error(f"ResponseError,uid:{uid},model:{req_m}")
        # if (
        #     "<StatusBlock>" in response
        #     and "</StatusBlock>" not in response
        #     and input.finish_reason != "length"
        # ):
        #     log.error(f"ResponseError,uid:{uid},model:{req_m},response:\n{response}")
        # 有超过连续50个不同汉字与字母，中间没有标点符号
        # if re.search(r"[^\s,，.。\"‘\'\(\):：\r\n!！]{100,}", response):
        # log.error(f"ResponseError,uid:{uid},model:{req_m},response:\n{response}")

        # if process_ai_message_on_auto_retry_by_rule(response, input.regex_rules):
        #     log.error(f"ResErr,AutoRetryRule,uid:{uid},model:{req_m}")
    except Exception as e:
        pass


def bot_message_display_format(
    role_name: str, message: str, status_block_switch: bool = True
) -> str:
    if not message:
        return message
    message = process_prefix(message, role_name, False)
    message = re.sub(r"<!--.*?-->", "", message, flags=re.DOTALL)
    message = re.sub(r"<wit>.*?</wit>", "", message, flags=re.DOTALL)
    # 获取 <StatusBlock>后所有内容
    status_block = None
    status_block = (
        re.search(r"<StatusBlock>(.*)", message, flags=re.DOTALL)
        if status_block_switch
        else None
    )
    if status_block:
        status_block = status_block.group(1)
        status_block = re.sub(r"<[^>]*>", "", status_block, flags=re.DOTALL)
        status_block = status_block.replace("```", "")
        status_block = status_block.replace("<", "&lt;").replace(">", "&gt;")
        status_block = status_block.strip()
        status_block = f"<blockquote>\n{status_block}\n</blockquote>"

    message = re.sub(r"<StatusBlock>(.*)", "", message, flags=re.DOTALL)
    message = re.sub(r"<[^>]*>", "", message, flags=re.DOTALL)
    message = message.replace("<", "&lt;").replace(">", "&gt;")
    # message = re.sub(re.compile(r'"(.*?)"'), r'<b>"\1"</b>', message)
    # message = re.sub(re.compile(r'".*"'), r'<b>\1</b>', message)

    message = f"{message.strip()}\n\n{status_block}" if status_block else message
    message = re.sub(r"[\n]{2,}", "\n\n", message)

    # message = message.replace("<", "&lt;").replace(">", "&gt;")
    return message


# 解析结束原因
def parse_finish_reason(finish_reason: str, all_content: str):
    if not finish_reason:
        return "stop"
    if finish_reason != "length":
        return finish_reason
    all_content = all_content.strip().rstrip("\n")
    if (
        all_content.endswith("</rep>")
        or all_content.endswith("</ca>")
        or all_content.endswith("</StatusBlock>")
    ):
        return "stop"
    return "length"


def chunk_finish_reason(finish_reason: str, chunk: ChatCompletionChunk):
    if finish_reason:
        return finish_reason
    if chunk and chunk.choices and chunk.choices[0].finish_reason and not finish_reason:
        return chunk.choices[0].finish_reason
    return finish_reason


def chunk_content(chunk: ChatCompletionChunk):
    if (
        chunk
        and chunk.choices
        and chunk.choices[0].delta
        and chunk.choices[0].delta.content
    ):
        return str(chunk.choices[0].delta.content)
    return ""


def bot_chunk_continue(content: str, chunk: ChatCompletionChunk, edit_count: int):
    if "<!--" in content and "-->" not in content:
        return True
    if "<wit>" in content and "</wit>" not in content:
        return True

    if "\n" not in chunk_content(chunk):
        return True
    if "<StatusBlock>" in content and "</StatusBlock>" not in content:
        return True
    if edit_count >= 10:
        return True
    return False


# 计算需要补的后缀
def calculate_suffix(content: str):
    if not content:
        return ""
    if content.endswith("</Status"):
        return "Block>"
    if content.endswith("<StatusBlock"):
        return ">"
    return ""


def require_remove_status_block(query: BuildHistoryQuery, message: ChatHistory):
    if not query.usb_switch:
        return True
    if (
        message.message_id == query.usb_message_id
        and query.usb_message_version == message.version
    ):
        return False
    if (
        query.usb_switch
        and query.usb_saved_time
        and message.timestamp < query.usb_saved_time
    ):
        return True
    return False


def add_name_to_content(content: str, type: str, role_name: str, final_user_name: str):
    prefix_name = final_user_name if type == ChatHistoryType.HUMAN.value else role_name
    content = content.strip()
    if content.startswith("<rep>"):
        content = content[5:].strip()
        if not content.startswith(prefix_name):
            content = f"<rep>\n{prefix_name}: " + content.strip()
        else:
            content = "<rep>\n" + content
    else:
        content = f"{prefix_name}: {content}"
    return content.strip()


def process_user_name(
    query: BuildHistoryQuery, messages: list[ChatHistory], history_nicknames: list[str]
):
    if query.use_display_user_name:
        for single_history in messages:
            single_history.content = process_user_name_on_display(
                single_history.content,
                query.request_user_name,
                query.nickname,
                history_nicknames,
            )
    if query.use_request_user_name:
        for single_history in messages:
            single_history.content = process_user_name_on_preset(
                single_history.content, query.request_user_name, history_nicknames
            )
    return messages


# 第一条AI消息，关闭状态栏或者有自定义消息，需要去掉状态栏
def get_request_first_ai_message(
    query: BuildHistoryQuery, history: list[ChatHistoryItem], input: ChatNextInput
):
    if not history or history[0].type != ChatHistoryType.AI.value:
        return ""
    if input.mode_type != ChatModeType.SINGLE.value:
        return ""
    if not input.first_message:
        return ""
    if not query.usb_switch or query.usb_saved_time:
        return remove_status_block(input.first_message)
    return input.first_message


# 修复最后的状态栏
def repair_last_status_block(
    chat_list: list[ChatHistoryItem],
    query: BuildHistoryQuery,
    last_status_block: str,
    status_block_type: str,
):
    if not chat_list or not last_status_block:
        return chat_list
    last_status_block = str_util.format_status_block(
        last_status_block, status_block_type, True
    )
    if not query.usb_switch:
        return chat_list
    for i in range(len(chat_list) - 1, -1, -1):
        if chat_list[i].type == ChatHistoryType.AI.value:
            content = chat_list[i].content
            if content.endswith("</rep>") and "<StatusBlock>" not in content:
                # 如果最后一条AI消息没有状态栏，添加状态栏
                chat_list[i].content += "\n" + last_status_block
            break
    return chat_list


def chat_history_to_item(chat_history: ChatHistory) -> ChatHistoryItem:
    return ChatHistoryItem(
        content=chat_history.content,
        type=chat_history.type,
        timestamp=chat_history.timestamp,
        version=chat_history.version,
        voice_url=chat_history.voice_url,
        message_id=chat_history.message_id,
        role_id=chat_history.role_id,
        photo_url=chat_history.photo_url,
        photo_id=chat_history.photo_id,
        retry_photos=chat_history.retry_photos,
        can_continue_replay=False,
    )
