from datetime import timedelta
import os
from dotenv import load_dotenv
load_dotenv()

TARGET_JOIN_GROUP_ID = int(os.environ['TARGET_JOIN_GROUP_ID'])
TARGET_JOIN_GROUP_LINK = os.environ['TARGET_JOIN_GROUP_LINK']
TARGET_JOIN_GROUP_LINK_HANDLE=f'@{TARGET_JOIN_GROUP_LINK.split('/')[3]}'

CHECK_IN_BOT_NAME = os.environ['CHECK_IN_BOT_NAME']
GROUP_CHECK_IN_THREAD_ID = int(os.environ['TARGET_CHECK_IN_GROUP_MSG_THREAD_ID'])

ROLE_CHANNEL_LINK = os.environ['ROLE_CHANNEL_LINK']
ROLE_CHANNEL_HANDLE=f'@{ROLE_CHANNEL_LINK.split('/')[-1]}'
ROLE_CHANNEL_ID = os.environ['ROLE_CHANNEL_ID']

# HUAN_MENG_AI_CHANNEL_LINK = os.environ['HUAN_MENG_AI_CHANNEL_LINK']

JOIN_GROUP_AWARD_AMOUNT = 500
JOIN_CHANNEL_AWARD_AMOUNT = 500
GROUP_CHECK_IN_AWARD_AMOUNT = 300
FC_REWARD_AMOUNT = 10000

JOIN_AWARD_EXPIRES = timedelta(days=31)
GROUP_CHECK_IN_AWARD_EXPIRES = timedelta(days=31)
FC_REWARD_EXPIRES = timedelta(days=7)
ACTIVITY_DIAMOND_SEASON_AWARD_EXPIRES = timedelta(days=31)

SOURCE_JOIN_CHAT_IDS = [int(c) for c in os.environ['SOURCE_JOIN_CHAT_IDS'].split(',')]

ROLE_CHAT_AWARD_EXPIRES = timedelta(days=31)
ROLE_CHAT_AWARD_AMOUNT = 1000

CHECK_IN_GROUP_LINK = os.environ['TARGET_CHECK_IN_GROUP_LINK']

CHECK_IN_WITH_LINK_REWARD_AMOUNT = 200
CHECK_IN_COMPETITOR_LINK_AMOUNT = 50
CHECK_IN_IN_ROLE_CHANNEL_REWARD_AMOUNT = 100
