import logging
import os
import time
from fastapi import FastAPI, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from common.common_constant import Env
from tasks import (
    activity_diamond_season_notify,
    amount_expire_notify,
    channel_stats,
    fc_notify_task,
    go_tma_daily_notify,
    go_tma_new_user_notify,
    init_config_task,
    like_task,
    recall_message,
    share_task,
    tg_message_task,
    translate_task,
    statistic_vip_job,
    delete_check_in_messages,
    recharge_channel_detection,
    recharge_product_decay
)
from utils import env_util

load_dotenv()

import sentry_sdk
import uvicorn

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

dsn = os.getenv("SENTRY_DSN")
sentry_sdk.init(
    dsn=dsn,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
)

cors_origins = os.getenv("CORS_ORIGINS", "").split(",")

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Conversation-Id",
        "Message-Id",
        "Message-Version",
        "Human-Message-Id",
    ],
)
scheduler = AsyncIOScheduler()

router = APIRouter()


@router.post("/task/run")
async def task_run(task_name: str = ""):
    jobs = scheduler.get_jobs()
    job_names = [job.func_ref for job in jobs]
    for job in jobs:
        if job.func_ref == task_name:
            await job.func()
            return {"status": "ok", "jobs": job_names}
    return {"status": "error", "msg": "task not found", "jobs": job_names}

@router.get("/task/send_task_reward/run")
async def task(partition: int):
    await activity_diamond_season_notify.send_task_reward(partition)
    return {"status": "error", "msg": "task not found"}


app.include_router(router)

Tortoise.init_models(["persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models"]},
    generate_schemas=True,
)


@app.on_event("startup")
async def startup_event():

    scheduler.add_job(translate_task.run_translate_task, "interval", minutes=12)
    scheduler.add_job(tg_message_task.delete_tg_message, "interval", seconds=60)
    scheduler.add_job(
        statistic_vip_job.run_cal_vip_job_daily, CronTrigger(hour=9, minute=40)
    )
    scheduler.add_job(fc_notify_task.send_fc_reward_notify, "interval", seconds=60 * 30)
    scheduler.add_job(recall_message.recall_message, CronTrigger(hour=21, minute=13))
    scheduler.add_job(
        amount_expire_notify.recall_expires, CronTrigger(hour=23, minute=0)
    )
    scheduler.add_job(channel_stats.start, "interval", seconds=60 * 14)

    scheduler.add_job(
        go_tma_daily_notify.tma_daily_notify, CronTrigger(hour=21, minute=5)
    )
    scheduler.add_job(
        go_tma_new_user_notify.tma_new_user_notify, "interval", seconds=60 * 15
    )

    # 晚上10点执行
    scheduler.add_job(
        init_config_task.init_role_orders, CronTrigger(hour=21, minute=58)
    )

    scheduler.add_job(like_task.incr_role_like_count, CronTrigger(hour=1, minute=40))

    scheduler.add_job(
        delete_check_in_messages.delete_check_in_messages, "interval", minutes=3
    )

    scheduler.add_job(share_task.check_content, "interval", seconds=60)
    
    scheduler.add_job(activity_diamond_season_notify.send_season_warming_up_notice, "interval", seconds=60)
    scheduler.add_job(activity_diamond_season_notify.send_warming_up_notice_to_user_group, "interval", seconds=60)
    scheduler.add_job(activity_diamond_season_notify.send_task_start, "interval", seconds=60)
    scheduler.add_job(activity_diamond_season_notify.send_task_end, "interval", seconds=60)
    scheduler.add_job(activity_diamond_season_notify.check_task_to_be_lottery, "interval", seconds=60)
    scheduler.add_job(activity_diamond_season_notify.peform_lottery, "interval", seconds=60)
    
    if env_util.get_current_env() == Env.STAG:
        cron_setting_for_failure_msg = "*" 
        cron_setting_for_reward_msg = "*" 
    else:
        cron_setting_for_failure_msg = "1, 31" # 每小时的1分和31分run
        cron_setting_for_reward_msg = "2, 12, 22, 32, 42, 52" # 每小时的1分和31分run

    # 聊天返钻活动给没有达标用户发做任务失败通知begin: 跑了5个定时任务，按uid取模
    scheduler.add_job(activity_diamond_season_notify.send_task_failure, 'cron', minute=cron_setting_for_failure_msg, args=[0])
    scheduler.add_job(activity_diamond_season_notify.send_task_failure, 'cron', minute=cron_setting_for_failure_msg, args=[1])
    scheduler.add_job(activity_diamond_season_notify.send_task_failure, 'cron', minute=cron_setting_for_failure_msg, args=[2])
    scheduler.add_job(activity_diamond_season_notify.send_task_failure, 'cron', minute=cron_setting_for_failure_msg, args=[3])
    scheduler.add_job(activity_diamond_season_notify.send_task_failure, 'cron', minute=cron_setting_for_failure_msg, args=[4])
    # 聊天返钻活动给达标用户发领奖通知begin: 跑了5个定时任务，按uid取模
    scheduler.add_job(activity_diamond_season_notify.send_task_reward, 'cron', minute=cron_setting_for_reward_msg, args=[0])
    scheduler.add_job(activity_diamond_season_notify.send_task_reward, 'cron', minute=cron_setting_for_reward_msg, args=[1])
    scheduler.add_job(activity_diamond_season_notify.send_task_reward, 'cron', minute=cron_setting_for_reward_msg, args=[2])
    scheduler.add_job(activity_diamond_season_notify.send_task_reward, 'cron', minute=cron_setting_for_reward_msg, args=[3])
    scheduler.add_job(activity_diamond_season_notify.send_task_reward, 'cron', minute=cron_setting_for_reward_msg, args=[4]) 
    # 聊天返钻活动给达标用户发领奖通知end

    
    #每隔5分钟 准点执行一次
    scheduler.add_job(init_config_task.refresh_model_stat, "cron", minute="0,5,10,15,20,25,30,35,40,45,50,55")
    # scheduler.add_job(init_config_task.init_cdn_url, "interval", seconds=30)
    #每分钟执行一次
    # scheduler.add_job(init_config_task.refresh_model_stat,"cron", minute="0-59/5")

    scheduler.add_job(recharge_channel_detection.check_channels, "interval", seconds=60 * 5)

    scheduler.add_job(recharge_product_decay.start_decay, 'cron', minute='30')

    scheduler.start()


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8901)
