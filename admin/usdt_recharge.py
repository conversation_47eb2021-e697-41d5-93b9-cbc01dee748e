import asyncio
from datetime import UTC, datetime
import hashlib
import logging
import os
from typing import Optional
import uuid
from fastapi import Depends, Request
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from pydantic import BaseModel, Field
import requests

from admin.auth import is_op_user
from persistence.models.models import RechargeStatusEnum
from services import re_purchase_service, recharge_service, usdt_service
from services.user_service import user_service
from utils import env_const, response_util

class NetworkContractPair(BaseModel):
    network: str
    chain_short_name: str
    contract_address: str
    from_block: str
    decimals: int = 6

class TransferHistory(BaseModel):
    from_address: str = Field(alias='from_address')
    hash: str = Field(alias='transaction_hash')
    to_address: str = Field(alias='to_address')
    amount: str = Field(alias='value_decimal')
    symbol: str = Field(alias='token_symbol')
    transactionTime: str = Field(alias='block_timestamp')
    chain: str

class USDTReconciliation(BaseModel):
    order_id: int
    tx_id: str
    chain: str

class USDTDirectReconciliation(BaseModel):
    order_id: int
    tx_id: str
    chain: str
    from_address: str

networks = [
    NetworkContractPair(network='polygon-mainnet', chain_short_name='polygon', contract_address='******************************************', from_block='0x3ce8145', decimals=6),
    NetworkContractPair(network='eth-mainnet', chain_short_name='eth', contract_address='******************************************', from_block='0x140d7ea', decimals=6),
    NetworkContractPair(network='bnb-mainnet', chain_short_name='bsc', contract_address='******************************************', from_block='0x28d9ea2', decimals=18),
    # NetworkContractPair(network='opBNB-mainnet', chain_short_name='opBNB', contract_address='******************************************', from_block='0x2b03328'),
    NetworkContractPair(network='AVAXC-mainnet', chain_short_name='avalanche', contract_address='******************************************', from_block='0x37846D4', decimals=6),
    # NetworkContractPair(network='XLayer', chain_short_name='xlayer', contract_address='******************************************', from_block='0x98547e'),
    NetworkContractPair(network='Arbitrum', chain_short_name='arbitrum', contract_address='******************************************', from_block='0x13958240', decimals=6),
]

networks_name_map = {n.chain_short_name: n for n in networks}

to_address = os.environ['ALCHEMY_RECIPIENT_ADDRESS']

def get_recent_transfers_by_oklink(pair: NetworkContractPair,
                                   page: int = 1, limit: int = 20):
    url = f'https://deep-index.moralis.io/api/v2.2/{env_const.USDT_EVM_RECIPIENT_ADDRESS}/erc20/transfers'
    headers = {
        'X-API-KEY': env_const.MORALIS_API_KEY
    }

    payload = {
        'chain': pair.chain_short_name,
        'contract_address[0]': pair.contract_address,
        'from_block': int(pair.from_block, 16),
        'order': 'DESC',
        'limit': limit,
    }

    response = requests.get(url, params=payload, headers=headers)
    result = response.json()
    transfers = result['result']

    return [TransferHistory(chain=pair.chain_short_name, **t) for t in transfers]

def get_tx_by_hash(chain: str, hash: str):
    url = f'https://deep-index.moralis.io/api/v2.2/transaction/{hash}/verbose'
    headers = {
        'X-API-KEY': env_const.MORALIS_API_KEY
    }

    payload = {
        'chain': chain
    }

    response = requests.get(url, params=payload, headers=headers)
    response.raise_for_status()
    result = response.json()
    return result

usdt_recharge_router = APIRouter(dependencies=[Depends(is_op_user)])

@usdt_recharge_router.get('/usdt/chains')
async def usdt_chains():
    data = [{'name': pair.network, 'value': pair.chain_short_name} for pair in networks]
    return response_util.ok(data=data)

@usdt_recharge_router.get('/usdt/charge_history')
async def usdt_charge_history():
    result = {}
    for pair in networks:
        transfers = get_recent_transfers_by_oklink(pair)
        result[pair.chain_short_name] = transfers
        await asyncio.sleep(0.5)
    return response_util.ok(data=result)

@usdt_recharge_router.get('/usdt/charge_tx')
async def usdt_charge_tx(chain: str, tx_id: str):
    data = get_tx_by_hash(chain, tx_id)
    return response_util.ok(data=data)

@usdt_recharge_router.get('/usdt/user_orders')
async def user_usdt_charge_orders(uid: int):
    data = await usdt_service.get_usdt_orders_by_user_id(uid)
    return response_util.ok(data=data)

@usdt_recharge_router.post('/usdt/reconciliation')
async def usdt_reconciliation(req: USDTReconciliation):
    exist_tx = await usdt_service.get_usdt_order_by_tx_id(req.tx_id)
    if exist_tx and exist_tx.status != RechargeStatusEnum.INIT:
        return response_util.error(400, f"交易已经被处理, tx_id: {req.tx_id}")

    network = networks_name_map[req.chain]

    on_chain_tx = get_tx_by_hash(req.chain, req.tx_id)
    if on_chain_tx['to_address'].lower() != network.contract_address.lower():
        return response_util.error(400, f"交易币种不是目标币种, tx: {on_chain_tx}")
    decoded_call = on_chain_tx['decoded_call']
    if decoded_call['label'] != 'transfer':
        return response_util.error(400, f"交易不是转账交易, tx: {on_chain_tx}")
    if decoded_call['params'][0]['value'].lower() != env_const.USDT_EVM_RECIPIENT_ADDRESS.lower():
        return response_util.error(400, f"交易接受地址不是目标地址, tx: {on_chain_tx}")
    on_chain_amount = float(decoded_call['params'][1]['value']) / (10**network.decimals) * 100000

    usdt_order = await usdt_service.get_usdt_order(req.order_id)
    if not usdt_order:
        return response_util.error(400, f"系统内 USDT 订单不存在, order_id : {req.order_id}")
    if usdt_order.status != RechargeStatusEnum.INIT:
            return response_util.error(400, f"交易已经被处理, oder_id: {req.order_id}, tx_id: {req.tx_id}")
    if abs(on_chain_amount - usdt_order.final_fee) > 100000:
        return response_util.error(400, f"交易金额差距大于 $1, 订单金额: {usdt_order.final_fee}, 交易金额: {on_chain_amount}")

    user = await user_service.get_user_by_id(usdt_order.user_id)

    delta = datetime.now(UTC) - user.created_at
    fc_reward = False
    if delta.seconds / 60 <= 30:
        recharged = await recharge_service.check_recharge_user(user_id=user.id)
        fc_reward = not recharged
    network_name = network.network.replace('-', '_').upper()
    from_address = on_chain_tx['from_address']
    recharge_order = await usdt_service.reconcile_usdt_order(usdt_order.id, network_name,
                                            req.tx_id, from_address, fc_reward)
    if recharge_order:
        await re_purchase_service.after_recharge(user, recharge_order)
    return response_util.ok(message='补单成功')

@usdt_recharge_router.post('/usdt/reconciliation_direct')
async def usdt_direct_reconciliation(req: USDTDirectReconciliation):
    exist_tx = await usdt_service.get_usdt_order_by_tx_id(req.tx_id)
    if exist_tx and exist_tx.status != RechargeStatusEnum.INIT:
        return response_util.error(400, f"交易已经被处理, tx_id: {req.tx_id}")

    network = networks_name_map[req.chain]

    usdt_order = await usdt_service.get_usdt_order(req.order_id)
    if not usdt_order:
        return response_util.error(400, f"系统内 USDT 订单不存在, order_id : {req.order_id}")
    if usdt_order.status != RechargeStatusEnum.INIT:
            return response_util.error(400, f"交易已经被处理, oder_id: {req.order_id}, tx_id: {req.tx_id}")

    user = await user_service.get_user_by_id(usdt_order.user_id)

    delta = datetime.now(UTC) - user.created_at
    fc_reward = False
    if delta.seconds / 60 <= 30:
        recharged = await recharge_service.check_recharge_user(user_id=user.id)
        fc_reward = not recharged
    network_name = network.network.replace('-', '_').upper()
    recharge_order = await usdt_service.reconcile_usdt_order(usdt_order.id, network_name,
                                            req.tx_id, to_address, fc_reward)
    if recharge_order:
        await re_purchase_service.after_recharge(user, recharge_order)
    return response_util.ok(message='补单成功')