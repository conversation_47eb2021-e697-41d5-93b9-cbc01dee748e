import asyncio
from datetime import timed<PERSON><PERSON>
from io import BytesIO
import json
import logging
import os
import re
import requests
from PIL import Image
from typing import Annotated, Optional
from fastapi import Depends, File, Form, UploadFile
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter
from pydantic import BaseModel
from aiogram.enums import ParseMode
from aiogram.types import BufferedInputFile

from common.bot_common import AI_CHANNEL_CONTENT, CHANNEL_CONTENT
from common.common_constant import (
    <PERSON>t<PERSON>ate<PERSON><PERSON>,
    <PERSON>tReplace,
    BotType,
    ChannelCategory,
    ChatModeType,
    CosPrefix,
    GroupCategory,
    Language,
    RoleReplace,
    RoleTag,
)
from common.role_card import CharacterBookEdit
from common.role_model import (
    AdminChatGroupEdit,
    ChatGroupEdit,
    RoleConfigResponse,
    RoleDataConfig,
    RoleEditDetail,
)
from controllers.request_depends import limit_request_by_key
from persistence import role_broadcast_history_dao
from persistence.models.mongo_models import RoleBroadcastHistory
from services import (
    bot_config_service,
    bot_message_service,
    role_config_service,
    tg_config_service,
)
from services.role import (
    character_book_service,
    role_group_service,
    role_loader_service,
    role_verify_service,
)
from tasks import init_config_task
from .auth import is_op_user
from common.models.common_res_model import SpeakerRes
from services import product_service, tag_service
from services.role_config_service import RoleConfigService
from services.voice_speaker_service import VoiceSpeakerService
from services.bot_services import helper_bots, adv_role_channel_bot
from services.user_service import user_service
from persistence.models.models import RoleConfig
from persistence.mongo_client import TavernCollection
from persistence.redis_client import redis_client
from utils import (
    cos_util,
    env_const,
    image_util,
    json_util,
    request_util,
    response_util,
    role_util,
    str_util,
    token_util,
)

roles_router = APIRouter(dependencies=[Depends(is_op_user)])

log = logging.getLogger(__name__)


channel_chat_id = int(os.environ["ROLE_CHANNEL_ID"])


@roles_router.get("/roles/category/list")
async def query_roles(tags: str = RoleTag.CHOSEN.value):
    tags_list = await tag_service.list_tags_with_orders()
    if tags == RoleTag.CHOSEN.value:
        ret_list = await role_config_service.chosen_card_list()
    elif tags == RoleTag.NEW.value:
        ret_list = await role_config_service.new_card_list()
    elif tags == RoleTag.HOT.value:
        ret_list = await role_config_service.host_card_list()
    elif tags == RoleTag.USER_ROLE.value:
        ret_list = await role_config_service.user_role_list()
    elif tags == RoleTag.USER_GROUP.value:
        ret_list = await role_config_service.user_group_list()
    elif tags in ["RisuAi", "RoChat"]:
        ret_list = await role_config_service.other_tag_card_list(tags)
    elif tags == RoleTag.USER_PUBLIC_ROLE.value:
        ret_list = await role_config_service.user_public_role_list()
    elif tags == RoleTag.USER_GROUP_PUBLIC_ROLE.value:
        ret_list = await role_config_service.user_public_group_list()
    elif tags == RoleTag.DELETE_PUBLIC_ROLE.value:
        ret_list = await role_config_service.delete_public_role_list()
    elif tags == RoleTag.CHOSEN_NEW_ORDER.value:
        ret_list = await role_config_service.chosen_new_order_card_list()
    else:
        ret_list = await role_config_service.card_list_by_sub_tags_category(tags)

    return response_util.success({"list": ret_list, "tags": tags_list})


@roles_router.get("/roles/create_config")
async def createConfig(role_id: int = 0) -> RoleConfigResponse:

    role_config = await RoleConfigService.get_role_config(role_id)
    ret = await RoleConfigService.get_created_config(role_id=role_id)
    speaker_list = await VoiceSpeakerService.get_active_speakers()
    speaker_list = (
        [SpeakerRes.from_model(speaker) for speaker in speaker_list]
        if speaker_list
        else []
    )
    all_tags = await tag_service.list_sub_tags_with_enabled()
    tag_orders = await tag_service.list_tags_with_orders()
    ret.all_tags = [tag.tag_name for tag in all_tags]
    ret.sub_tag_category_list = await tag_service.list_category_group_sub_tags()
    ret.tag_orders = json_util.convert_to_list(tag_orders)
    ret.speakers = speaker_list
    ret.chat_products = await product_service.list_chat_product_new()
    return ret


@roles_router.post("/roles/create")
async def create_role(
    role_json: str = Form(...), avatar_img: UploadFile = File(None)
) -> RoleEditDetail:
    log.info(f"create_role: {role_json}")
    # 解析JSON字符串为Pydantic模型
    role_dict = json.loads(role_json)
    role_dict = json_util.remove_nulls(role_dict)
    role = RoleEditDetail(**json_util.convert_to_dict(role_dict))
    return await role_config_service.create_role(role, avatar_img)


@roles_router.post("/roles/update")
async def update_role(
    role_json: Annotated[str, Form()],
    mode_type: Annotated[str, Form()],
    mode_target_id: Annotated[int, Form()],
    avatar_img: Optional[UploadFile] = None,
):
    log.info(
        f"RolesUpdate,mode_type:{mode_type},mode_target_id:{mode_target_id}: {role_json}"
    )
    upload_img = ""
    if avatar_img:
        upload_img = cos_util.upload_image(avatar_img, CosPrefix.ROLE)
    if mode_type == ChatModeType.GROUP.value:
        chat_group = AdminChatGroupEdit(
            **json_util.convert_to_dict(json.loads(role_json))
        )
        error = await role_verify_service.group_update_error(chat_group)
        if error:
            return response_util.json_param_error(error)
        return await role_group_service.admin_update_group(mode_target_id, chat_group)

    original_config = await role_loader_service.get_by_id(mode_target_id)

    role_dict = json.loads(role_json)
    role_dict = json_util.remove_nulls(role_dict)
    role = RoleEditDetail(**json_util.convert_to_dict(role_dict))
    role.role_avatar = upload_img if upload_img else role.role_avatar
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    role_config = role.to_role_config(product_ids)
    role_config.privacy = True if original_config.uid == 0 else original_config.privacy
    role_config.support_photo = original_config.support_photo
    role_config.def_language = original_config.def_language
    if role.role_book:
        book = await character_book_service.update_character_book(role.role_book)
        role_config.book_id = book.book_id
    res = await RoleConfigService.update_user_role_config(uid=0, config=role_config)
    if res is None:
        return response_util.json_param_error("Update role failed")
    else:
        return RoleEditDetail.from_role_config(res, product_ids)


@roles_router.post("/roles/disable")
async def role_disable(role_id: Annotated[int, Form()]) -> RoleEditDetail:
    role = await RoleConfig.get(id=role_id)
    role.status = False
    await role.save()
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    return RoleEditDetail.from_role_config(role, product_ids)


@roles_router.post("/roles/character_book/update")
async def update_character_book(character_book_json: Annotated[str, Form()]):
    character_book_dict = json.loads(character_book_json)
    character_book_dict = json_util.remove_null_dict(character_book_dict)
    role = CharacterBookEdit(**character_book_dict)
    verify, ret = await character_book_service.verify_character_book(role)
    if not verify:
        return response_util.json_param_error(ret)
    res = await role_config_service.update_character_book(role)
    if not res:
        return response_util.json_param_error("Update character book failed")
    return res


class TokenCountRequest(BaseModel):
    content: str


@roles_router.post("/tokenizers/count")
async def get_token_count(input: dict = {}):
    ret = token_util.num_token_from_dict(input)
    if "content" in input:
        token_count = token_util.num_tokens_from_string(input["content"])
        ret["token_count"] = token_count
    return JSONResponse(content=ret)


# @roles_router.post("/tools/upload/img")
# async def upload_img(img: UploadFile = File(None)):

#     img_content = await img.read()
#     img_url = image_util.upload_image(img_content)

#     return JSONResponse(content={"img_url": img_url})


@roles_router.post("/roles/group/disable")
async def disable_group(
    group_id: Annotated[int, Form()],
):
    log.info(f"disable_group,group_id:{group_id}")
    return await role_group_service.disable(0, group_id)


@roles_router.get("/roles/group/create_config")
async def create_group_config(
    group_id: int = 0,
):
    return await role_group_service.create_config(group_id)


@roles_router.post("/roles/group/create")
async def create_group(chat_group: ChatGroupEdit):
    log.info(f"create_group,chat_group_json: {chat_group.model_dump()}")
    error = await role_verify_service.group_create_error(chat_group, 0)
    if error:
        return response_util.json_param_error(error)
    roles = await role_loader_service.list_by_ids(chat_group.role_ids)
    nsfw = any([role.nsfw for role in roles])
    return await role_group_service.create_group(0, chat_group, nsfw)


class RoleBroadcastRequest(BaseModel):
    role_id: int
    channel_id: int = 0


class RoleBroadcastToChannelRequest(BaseModel):
    role_id: int
    channel_id: int


def escape_tg_text(content: str) -> str:
    return (
        content.replace("-", "\\-")
        .replace("(", "\\(")
        .replace(")", "\\)")
        .replace("!", "\\!")
        .replace("_", "\\_")
        .replace(".", "\\.")
        .replace("=", "\\=")
        .replace("+", "\\+")
        .replace("~", "\\~")
        .replace("*", "\\*")
        .replace("#", "\\#")
    )


async def get_role_author(role: RoleConfig) -> str:
    user_id = role.uid
    if user_id == 0 or (not role.operation_config.get("author", False)):
        return "匿名"
    user = await user_service.get_user_by_id(user_id)
    return escape_tg_text(user.nickname)


async def broadcast_role_to_channels(role_id: int, channels: list[list[str]]):
    role = await role_loader_service.load_by_id(role_id)
    if role is None:
        return JSONResponse(content={"message": "Role not found"}, status_code=404)

    intro = str_util.format_char(role.introduction, role.role_name)
    intro = str_util.format_user(intro, "你")
    card_name = str_util.format_char(role.card_name, role.role_name)
    intro = escape_tg_text(intro)
    role_name = escape_tg_text(role.role_name)
    card_name = escape_tg_text(card_name)
    author = await get_role_author(role)
    sub_tags = [f"\\#{tag}" for tag in role.sub_tags]
    tags = " ".join(sub_tags)
    main_bot = await tg_config_service.get_main_bot_by_category(BotCategory.HELPER)
    bot = await tg_config_service.get_sender_bot_by_id(main_bot.bot_id)
    # bot = helper_bots[0]
    for ch in channels:
        if ch[1]:
            tma_link = (
                f"{env_const.TMA_URL_FOR_ROLE_CHANNEL}?startapp=u_{ch[1]}-r_{role.id}"
            )
            bot_link = (
                f"{env_const.CHAT_URL_FOR_ROLE_CHANNEL}?start=u_{ch[1]}-r_{role.id}"
            )
        else:
            tma_link = f"{env_const.TMA_URL_FOR_ROLE_CHANNEL}?startapp=r_{role.id}"
            bot_link = f"{env_const.CHAT_URL_FOR_ROLE_CHANNEL}?start=r_{role.id}"

        content = f"""作者： \\#{author}
*\\#{role_name} \\(\\#{card_name}\\)*

{intro}

{tags} \\#幻梦 \\#AI \\#文爱 \\#小说 \\#AI女友 \\#赛博恋人

*👉[开启小程序]({tma_link})👈*
*👉[开启 TG 直聊]({bot_link})👈*

幻梦AI宇宙 \\| AI女友第一名 \\| 可私人定制任意角色 \\| 7\\*24小时的性福 \\| 满足你一切幻想
"""
        if ch[0] in env_const.NSFW_ROLE_CHANNELS:
            image_url = role.role_avatar
        else:
            image_url = role_util.get_safe_image_url(role)
        message = await bot.send_photo(
            ch[0],
            photo=image_url,
            caption=content,
            parse_mode=ParseMode.MARKDOWN_V2,
        )
        await TavernCollection.ROLE_BROADCAST_HISTORY.insert_one(
            {"role_id": role_id, "message_id": message.message_id, "chat_id": ch[0]}
        )
        await asyncio.sleep(0.1)

    await send_to_all_ai_rewrite(intro, card_name, role_name, author, role)

    return JSONResponse(content={"message": "Broadcast success"})


async def send_to_all_ai_rewrite(intro, card_name, role_name, author, role):
    for ch in env_const.ADV_ROLE_CHANNEL_CHAT_MAP.keys():
        try:
            await send_by_ai_rewrite_processed(
                ch, intro, card_name, role_name, author, role
            )
        except Exception as e:
            log.warning(f"send_to_all_ai_rewrite error: {e}")


# 老版本
# @roles_router.post("/roles/broadcast")
# async def broadcast_role(req: RoleBroadcastRequest):
#     channel_bots = await bot_config_service.list_bot(BotType.CHANNEL)
#     channels = [[str(bot.bot_id), str(bot.channel_id)] for bot in channel_bots]
#     return await broadcast_role_to_channels(req.role_id, channels)

# 老版本v1
# @roles_router.post("/roles/broadcast")
# async def broadcast_role(req: RoleBroadcastRequest):
#     channel_bots = await tg_config_service.list_channel(category=ChannelCategory.ROLE, active=True)
#     channel_bots = [bot for bot in channel_bots if not bot.ai_content]
#     channels = [[str(bot.chat_id), str(bot.channel_id)] for bot in channel_bots]
#     return await broadcast_role_to_channels(req.role_id, channels)


@roles_router.post("/roles/broadcast/channel")
async def broadcast_role_to_channel(req: RoleBroadcastToChannelRequest):
    target = None
    for channel in env_const.ROLE_SUB_CHANNELS:
        if channel[0] == str(req.channel_id):
            target = channel
            break
    if target is None:
        return JSONResponse(content={"message": "Channel not found"}, status_code=404)
    return await broadcast_role_to_channels(req.role_id, [target])


def extract_response_parts(response_content: str) -> tuple[str, str]:
    """
    Extract the three parts from response content: rep content, main content, and tags

    Args:
        response_content: The markdown formatted response content

    Returns:
        tuple containing (rep_content, main_content, tags)
    """
    # Extract content between <rep> tags
    rep_pattern = r"<rep>(.*?)</rep>"
    rep_match = re.search(rep_pattern, response_content, re.DOTALL)
    if not rep_match:
        return "", ""

    rep_content = rep_match.group(1).strip()

    # Extract main content between SoExpert and 【标签】
    main_start = rep_content.find("SoExpert:")
    main_end = rep_content.find("【标签】")

    if main_start == -1 or main_end == -1:
        return rep_content, ""

    main_content = rep_content[main_start + len("SoExpert:") : main_end].strip()

    # Extract tags after 【标签】
    tags = rep_content[main_end + len("【标签】") :].strip()

    return main_content, tags


async def get_rewrite_intro(intro: str):
    data = {
        "inputs": {},
        "query": intro,
        "response_mode": "blocking",
        "conversation_id": "",
        "user": "tavern-admin",
    }
    header = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {env_const.DIFY_ROLE_REWRITE_TOKEN}",
    }
    # response = requests.post(env_const.DIFY_URL, json=data, headers=header)
    # response_content = response.json().get("answer")
    response = await request_util.post_json(
        env_const.DIFY_URL, data, headers=header, timeout=60
    )
    if not response:
        raise Exception("Failed to get response from Dify")
    response_content = response.get("answer", "")
    new_into, tags = extract_response_parts(response_content)
    return escape_tg_text(new_into), escape_tg_text(tags)


async def send_by_ai_rewrite_processed(
    channel_id: int,
    intro: str,
    card_name: str,
    role_name: str,
    author: str,
    role: RoleConfig,
):
    bot = adv_role_channel_bot
    intro, tags = await get_rewrite_intro(intro)
    content = f"""*\\#{role_name} \\(\\#{card_name}\\)*
作者： \\#{author}

{intro}

和{role_name}激情文爱：链接在评论区

{tags}
    """

    safe_url = role_util.get_safe_image_url(role)
    image_bytes = await request_util.get_bytes(safe_url)
    if not image_bytes:
        return
    image_byte_arr = image_util.random_noise_to_image(image_bytes)
    message = await bot.send_photo(
        channel_id,
        photo=BufferedInputFile(image_byte_arr.getvalue(), filename=f"result.png"),
        caption=content,
        parse_mode=ParseMode.MARKDOWN_V2,
    )
    history = RoleBroadcastHistory(
        role_id=role.id,
        message_id=message.message_id,
        chat_id=channel_id,
        photo_file_id=message.photo[0].file_id if message.photo else "",
        caption=content,
        ai_rewrite=True,
    )
    await role_broadcast_history_dao.insert(history)
    # await TavernCollection.ROLE_BROADCAST_HISTORY.insert_one(
    # {"role_id": role.id, "message_id": message.message_id, "chat_id": channel_id}
    # )
    data = {
        "role_id": role.id,
        "message_id": message.message_id,
        "role_name": role_name,
        "content": content,
        "photo": role.role_avatar,
    }
    chat_id = env_const.ADV_ROLE_CHANNEL_CHAT_MAP.get(channel_id)
    redis_client.set(
        f"role_br_message_{chat_id}:{message.message_id}",
        json.dumps(data),
        ex=timedelta(hours=1),
    )


@roles_router.post("/roles/broadcast_with_ai")
async def send_by_ai_rewrite(req: RoleBroadcastRequest):
    role = await role_loader_service.load_by_id(req.role_id)
    if role is None:
        return JSONResponse(content={"message": "Role not found"}, status_code=404)

    intro = str_util.format_char(role.introduction, role.role_name)
    intro = str_util.format_user(intro, "你")
    card_name = str_util.format_char(role.card_name, role.role_name)
    intro = escape_tg_text(intro)
    role_name = escape_tg_text(role.role_name)
    card_name = escape_tg_text(card_name)
    author = await get_role_author(role)
    await send_by_ai_rewrite_processed(
        req.channel_id, intro, card_name, role_name, author, role
    )
    return JSONResponse(content={"message": "Broadcast success"})


# @roles_router.post("/roles/broadcast/chat")
# async def broadcast_role_to_chat(chat_id: int, role_id: int):
#     log.info(f"broadcast_role_to_chat,chat_id:{chat_id},role_id:{role_id}")
#     role = await role_loader_service.load_by_id(role_id)
#     if not role or not role.privacy:
#         log.error(f"broadcast_role_to_chat,chat_id:{chat_id},role_id:{role_id}")
#         return response_util.def_error("Role not found or not public")
#     channel = await tg_config_service.get_channel_by_chat_id(chat_id)
#     if channel is None:
#         log.error(f"broadcast_role_to_chat,chat_id:{chat_id},role_id:{role_id}")
#         return response_util.def_error("Channel not found")
#     if channel.category == ChannelCategory.ROLE.value:
#         await bot_message_service.push_role_channel_message(
#             channel.chat_id, role_id, CHANNEL_CONTENT
#         )
#         return response_util.ok({"role_id": role_id, "chat_id": chat_id})
#     if channel.category == ChannelCategory.AI_ROLE.value:
#         intro = str_util.escape_tg_text(role.introduction)
#         intro, tags = await get_rewrite_intro(intro)
#         ai_content = AI_CHANNEL_CONTENT.replace(RoleReplace.ROLE_INTRO.value, intro)
#         content = ai_content.replace(RoleReplace.ROLE_SUB_TAGS.value, tags)
#         await bot_message_service.push_role_channel_message(
#             channel.chat_id, role_id, content, True
#         )
#         return response_util.ok({"role_id": role_id, "chat_id": chat_id})
#     return response_util.def_error("Channel not supported")


@roles_router.post("/roles/broadcast")
@limit_request_by_key("roles_broadcast_role", 30)
async def broadcast_role(req: RoleBroadcastRequest):
    log.info(f"broadcast_role,req:{req}")
    debounce_key = f"role_broadcast_{req.role_id}"
    if redis_client.get(debounce_key):
        return response_util.def_error("当前角色正在推送中，稍后再试")
    redis_client.set(debounce_key, 1, ex=timedelta(minutes=10))
    try:
        role_config = await role_loader_service.load_translated_role(
            req.role_id, Language.ZH.value, ""
        )
        if not role_config or not role_config.privacy:
            return response_util.def_error("角色不存在、已下架或者未公开")
        channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
        for channel in channels:
            await bot_message_service.push_role_channel_message(
                channel.chat_id, req.role_id, CHANNEL_CONTENT
            )
        role_groups = await tg_config_service.list_group(category=GroupCategory.ROLE)
        for group in role_groups:
            await bot_message_service.push_role_group_message(
                group.chat_id, req.role_id, CHANNEL_CONTENT
            )
        channels = await tg_config_service.list_channel(
            category=ChannelCategory.AI_ROLE
        )
        common_channels = [channel.username for channel in channels]
        if not channels:
            return response_util.ok(
                {"role_id": req.role_id, "common_channels": common_channels}
            )
        intro = str_util.escape_tg_text(role_config.introduction)
        intro, tags = await get_rewrite_intro(intro)
        ai_content = AI_CHANNEL_CONTENT.replace(RoleReplace.ROLE_INTRO.value, intro)
        content = ai_content.replace(RoleReplace.ROLE_SUB_TAGS.value, tags)
        for channel in channels:
            await bot_message_service.push_role_channel_message(
                channel.chat_id, req.role_id, content, True
            )
        ai_channels = [channel.username for channel in channels]
        return response_util.ok(
            {
                "role_id": req.role_id,
                "common_channels": common_channels,
                "ai_channels": ai_channels,
            }
        )
    finally:
        redis_client.delete(debounce_key)
        log.info(f"broadcast_role,req:{req} done")