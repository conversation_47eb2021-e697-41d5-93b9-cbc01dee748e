import asyncio
from datetime import UTC, datetime, timedelta, timezone
import json
import logging
import os
import uuid
from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
import requests
from ai import inner_bot, lite_llm_bot
from common.models.common_res_model import SpeakerR<PERSON>
from common.role_model import RoleConfigResponse, RoleDataConfig
from common.translate_model import TranslateTaskType
from persistence import chat_history_dao
from properties import prop_util
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from services.voice_speaker_service import VoiceSpeakerService
from tasks.translate import trans_role_desc_task
from utils import env_const, json_util, message_utils, translate_util, utils

from admin.auth import is_op_user
from common.bot_common import (
    CHANNEL_CONTENT,
    RECHARGE_PRODUCT_CALLBACK_TIPS,
    Button,
    MessageTemplate,
)
import mysql.connector
from common.common_constant import (
    ERROR_CODE,
    <PERSON>t<PERSON><PERSON>lace,
    Env,
    Language,
    ProductType,
    RoleTag,
)
from persistence.models.models import (
    CursorAccount,
    ExpirableAward,
    LlmModelConfig,
    PackageVoucher,
    PopupRecord,
    Product,
    RechargeOrder,
    RegexOption,
    RoleAudit,
    RoleConfig,
    SubTag,
    TranslateResource,
    UcbRefreshRecord,
    User,
    UserChatBenefit,
    UserRoleShare,
    UserStatus,
    UserTaskRecord,
    VoucherStatusEnum,
)
from services import (
    bot_message_service,
    config_service,
    model_service,
    product_service,
    regex_service,
    tag_service,
    tg_config_service,
    translate_service,
    user_service,
)
from services.user import user_benefit_service
from tasks import init_config_task, recall_message, translate_task
from utils import env_util, response_util, str_util
from utils.exception_util import ParamException


no_auth_router = APIRouter()

log = logging.getLogger(__name__)


@no_auth_router.post("/no_auth/refresh_role_avatar")
async def refresh_role_avatar():
    all_list = await RoleConfig.filter().only("id", "role_avatar").all()
    for role in all_list:
        role_avatar = str_util.format_avatar(role.role_avatar)
        if role_avatar != role.role_avatar:
            await RoleConfig.filter(id=role.id).update(role_avatar=role_avatar)
            log.info(
                f"update role_avatar: {role.id}, {role.role_avatar} -> {role_avatar}"
            )
    return response_util.ok("ok")


@no_auth_router.post("/no_auth/product/add_model_product")
async def upsert_model_product(
    llm_model: str,
    display_name: str = "",
    desc: str = "",
    price: int = 10,
    online: bool = False,
):
    model_config = await config_service.get_llm_model_config_by_model(llm_model)
    if not model_config:
        return response_util.def_error("模型不存在")
    products = await Product.filter(model=model_config.llm_model).all()
    if products:
        return response_util.def_error("已存在该模型的商品")

    await Product.create(
        product_id=uuid.uuid4(),
        model=llm_model,
        type=ProductType.CHAT.value,
        display_name=display_name,
        name="聊天",
        desc=desc,
        price=price,
        online=online,
        # mid=model_config.mid,
        model_int=model_config.model_int,
    )
    await Product.create(
        product_id=uuid.uuid4(),
        model=llm_model,
        type=ProductType.IMPERSONATE.value,
        display_name=display_name + "-AI代答",
        name="AI代答",
        desc="",
        price=price,
        online=online,
        # mid=model_config.mid,
        model_int=model_config.model_int,
    )
    await Product.create(
        product_id=uuid.uuid4(),
        model=llm_model,
        type=ProductType.CHAT_CONTINUE.value,
        display_name=display_name + "-AI续答",
        name="AI续答",
        desc="",
        price=price / 2,
        online=online,
        # mid=model_config.mid,
        model_int=model_config.model_int,
    )
    return response_util.ok()


@no_auth_router.get("/no_auth/voucher/init")
async def upsert_package_voucher(
    price: int = 100, product_id: str = "a524b473-56c3-4988-b939-ccae98666176"
):
    if env_util.get_current_env() != Env.STAG:
        return response_util.def_error("非测试环境禁止操作")
    voucher = await PackageVoucher.create(
        voucher_id=uuid.uuid4(),
        recharge_product_id=product_id,
        price=price,
        distributor="SDFKW",
        expire_at=datetime.now(UTC) + timedelta(days=365 * 10),
        status=VoucherStatusEnum.NORMAL,
    )
    return response_util.ok({"voucher_id": voucher.voucher_id})


@no_auth_router.get("/no_auth/chat/benefit/add")
async def benefit_add(
    user_id: int,
    recharge_product_id: str,
):
    await user_benefit_service.reward_chat_benefit(user_id, recharge_product_id)
    return response_util.ok()


@no_auth_router.get("/no_auth/chat/send/recall_message")
async def send_recall_message(
    user_id: int = 0,
):
    if user_id:
        time = datetime.now(timezone.utc) - timedelta(days=2)
        user = await user_service.get_by_id(user_id)
        await recall_message.send_messages({user.id: time})
    return response_util.ok()


@no_auth_router.get("/no_auth/chat/benefit/send/callback_msg")
async def send_callback_msg(
    user_id: int = 0,
    send_all: bool = False,
):

    url = f"https://t.me/{BotReplace.MAIN_TMA_BOT.value}/tavern?startapp=e_eyJwIjoicGF5In0"
    template = MessageTemplate(
        tips=RECHARGE_PRODUCT_CALLBACK_TIPS,
        buttons=[Button(text="点此充值领豪礼", url=url)],
    )
    template = await bot_message_service.format_template_replace(template)
    if user_id:
        user = await user_service.get_by_id(user_id)
        await bot_message_service.send_user_template_message(user, template)
    if send_all:
        asyncio.create_task(_send_all_message(template))
    return response_util.ok()


async def fetch_user_ids():
    connection_string = os.environ["MYSQL_SLAVE_URL"]
    conn_params = utils.parse_mysql_connection_string(connection_string)
    with mysql.connector.connect(**conn_params) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "select distinct(user_id) from chat_history_statistic where created_at>(NOW() - INTERVAL 1 MONTH) and id >=10000000"
        )
        items = cursor.fetchall()
        res = [int(x) for x in items]  # type: ignore
        cursor.close()
        return res  # type: ignore
    return []


async def _send_all_message(template: MessageTemplate):

    async def check_and_send(user: User):
        is_pay_user = await user_service.is_payed_user(user.id)
        if is_pay_user:
            log.info(f"send user message failed pay user: {user.id}")
            return
        await bot_message_service.send_user_template_message(user, template)
        log.info(f"send user message success {user.id}")

    user_ids = await fetch_user_ids()
    for uid in user_ids:
        if uid > 2996000:
            continue
        user = await user_service.get_by_id(uid)
        await check_and_send(user)
    return True


# role相关


@no_auth_router.get("/no_auth/role/create_config")
async def createConfig(role_id: int = 0) -> RoleConfigResponse:

    ret = await RoleConfigService.get_created_config(role_id=role_id)
    speaker_list = await VoiceSpeakerService.get_active_speakers()
    speaker_list = (
        [SpeakerRes.from_model(speaker) for speaker in speaker_list]
        if speaker_list
        else []
    )
    all_tags = await tag_service.list_sub_tags_with_enabled()
    tag_orders = await tag_service.list_tags_with_orders()
    ret.all_tags = [tag.tag_name for tag in all_tags]
    ret.sub_tag_category_list = await tag_service.list_category_group_sub_tags()
    ret.tag_orders = json_util.convert_to_list(tag_orders)
    ret.speakers = speaker_list
    ret.chat_products = await product_service.list_chat_product_new()
    return ret


@no_auth_router.post("/no_auth/role/translate_desc/pending_task_ids")
async def get_translate_task_ids():
    role_list = await role_loader_service.list_public()
    task_list = await translate_service.list_tasks_by_type(
        TranslateTaskType.ROLE_DESCRIPTION.value
    )
    task_map = {task.task_key: task for task in task_list}
    undo_list = []
    finish_list = []
    for role in role_list:
        if role.switch_en_desc:
            continue
        role_data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        language = translate_util.detect_language(role_data_config.description)
        if language == Language.EN:
            continue
        if (
            str(role.id) in task_map
            and task_map[str(role.id)].updated_at > role.updated_at
        ):
            finish_list.append(
                {
                    "role_id": role.id,
                    "role_name": role.card_name,
                }
            )
            continue

        undo_list.append(
            {
                "role_id": role.id,
                "role_name": role.card_name,
            }
        )
    return response_util.ok({"undo_list": undo_list, "finish_list": finish_list})


@no_auth_router.get("/no_auth/role/translate/desc/start")
async def start_translate_desc(role_id: int, init_task: bool = False):
    target_data = await trans_role_desc_task.init_and_run_role_task_single(
        role_id, init_task
    )
    return response_util.ok({"target_data": target_data})


@no_auth_router.get("/no_auth/role/translate/desc/detail")
async def translate_detail(role_id: int):
    role = await role_loader_service.get_by_id(role_id)
    if not role:
        return response_util.error(ERROR_CODE.PARAM_ERROR.value, "Role not found")
    role_data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
    language = translate_util.detect_language(role_data_config.description)
    if language == Language.EN:
        return response_util.ok({"description": role_data_config.description})
    task = await translate_service.get_task(
        TranslateTaskType.ROLE_DESCRIPTION.value, str(role_id)
    )
    if not task:
        return response_util.def_error("Task not found")
    return response_util.ok(
        {
            "description": task.source,
            "task_target": task.target,
            "role_updated_at": role.updated_at,
            "task_updated_at": task.updated_at,
        }
    )


@no_auth_router.post("/no_auth/llm_model/add")
async def add_llm_model(
    llm_model: str,
    request_llm_model: str,
    min_p: bool = True,
    repetition_penalty: bool = True,
):
    model_config = await config_service.get_llm_model_config_by_model(llm_model)
    if model_config:
        return response_util.def_error("模型已存在")
    all_llm_configs = await LlmModelConfig.all()
    model_int = max([x.model_int for x in all_llm_configs]) + 1
    support_params = []
    if min_p:
        support_params.append("min_p")
    if repetition_penalty:
        support_params.append("repetition_penalty")
    await LlmModelConfig.create(
        llm_model=llm_model,
        model_int=model_int,
        enabled=True,
        request_llm_model=request_llm_model,
        support_params=support_params,
        check_llm_request=True,
        request_cluster="FREE" if "free" in llm_model else "PRO",
    )
    if env_util.get_current_env() == Env.PROD:
        ret = requests.post(
            f"https://tavern-admin-api.655356.xyz/no_auth/llm_model/add?llm_model={llm_model}&request_llm_model={request_llm_model}&min_p={min_p}&repetition_penalty={repetition_penalty}"
        ).json()
        log.info(f"add llm model {llm_model} to prod, ret: {ret}")
    return response_util.ok()


@no_auth_router.get("/no_auth/llm_model/stat")
async def llm_request_stat():
    summary = await model_service.map_model_request_by_summary(5)
    return response_util.ok({"summary": summary})


@no_auth_router.get("/no_auth/llm_model/list")
async def model_list():
    model_list = await config_service.list_llm_model_config()
    model_list = [x.llm_model for x in model_list if x.enabled]
    model_list.sort(key=lambda x: x.lower())
    return response_util.ok({"model_list": model_list})


@no_auth_router.post("/no_auth/llm_model/check_success")
async def check_model_success(llm_model: str):
    if not llm_model:
        return response_util.def_error("模型不能为空")
    model_config = await config_service.get_llm_model_config_by_model(llm_model)
    if not model_config:
        return response_util.def_error("模型不存在")
    base_url = config_service.get_lite_llm_base_url(model_config.request_cluster)
    success = await lite_llm_bot.check_models_success(llm_model, base_url=base_url)
    return response_util.ok({"model": llm_model, "success": success})


@no_auth_router.get("/no_auth/cursor_account/register_list")
async def cursor_account_register_list():

    cursor_account_list = await CursorAccount.filter(token_init=False).all()
    cursor_account_list = [
        {
            "email": user.email,
            "original_email": user.original_email,
            "imap_server": user.imap_server,
            "imap_port": user.imap_port,
            "original_password": user.original_password,
        }
        for user in cursor_account_list
    ]
    return response_util.ok({"account_list": cursor_account_list})


@no_auth_router.post("/no_auth/cursor_account/upsert_token")
async def cursor_account_upsert_token(
    email: str,
    token: str,
):
    cursor_account = await CursorAccount.get_or_none(email=email)
    if not cursor_account:
        return response_util.def_error("邮箱不存在")
    if not token:
        return response_util.def_error("token不能为空")
    cursor_account.token = token
    cursor_account.token_init = True
    await cursor_account.save()
    return response_util.ok()


@no_auth_router.post("/no_auth/cursor_account/add")
async def cursor_account_add(
    email: str,
    original_email: str,
    original_password: str,
    imap_server: str,
    imap_port: int,
):
    cursor_account = await CursorAccount.get_or_none(email=email)
    if cursor_account:
        return response_util.def_error("邮箱已存在")

    cursor_account = await CursorAccount.create(
        email=email,
        original_email=original_email,
        original_password=original_password,
        imap_server=imap_server,
        imap_port=imap_port,
    )
    return response_util.ok()


@no_auth_router.post("/no_auth/cursor_account/new_emails")
async def new_emails():

    original_email = "<EMAIL>"
    original_password = "qewx ybsl vglw degd"
    imap_server = "imap.gmail.com"
    imap_port = 993
    duck_url = "https://quack.duckduckgo.com/api/email/addresses"
    headers = {
        "authorization": "Bearer x5nshbmarz2nuuczrpnbrzgmlk5clnw7edj4ggoyjurjqcle8kgaubbhahtyy3"
    }
    num = 30
    for i in range(num):
        res = requests.post(duck_url, headers=headers)
        res = res.json()
        if "address" not in res:
            continue
        email = res["address"]
        email = email + "@duck.com"

        await cursor_account_add(
            email=email,
            original_email=original_email,
            original_password=original_password,
            imap_server=imap_server,
            imap_port=imap_port,
        )


# 转移用户资产
# ===============user asset transfer api==================
@no_auth_router.post("/no_auth/user/transform_account")
async def transform_account(original_user_id: int, new_user_id: int, run: bool = False):
    old_user = await User.get_or_none(id=original_user_id)
    new_user = await User.get_or_none(id=new_user_id)
    if not old_user or not new_user:
        return response_util.def_error("用户不存在")
    ret = {}
    recharge_orders = await RechargeOrder.filter(user_id=original_user_id).all()
    ret["update_recharge_orders"] = len(recharge_orders)
    if recharge_orders and run:
        await RechargeOrder.filter(user_id=original_user_id).update(user_id=new_user_id)

    expirable_awards = await ExpirableAward.filter(user_id=original_user_id).all()
    ret["update_expirable_awards"] = len(expirable_awards)
    if expirable_awards and run:
        await ExpirableAward.filter(
            user_id=original_user_id, out_order_id__not="G1"
        ).update(user_id=new_user_id)
    user_chat_benefits = await UserChatBenefit.filter(user_id=original_user_id).all()
    ret["update_user_chat_benefits"] = len(user_chat_benefits)
    if user_chat_benefits and run:
        await UserChatBenefit.filter(user_id=original_user_id).update(
            user_id=new_user_id
        )
    user_chat_benefit_refreshs = await UcbRefreshRecord.filter(
        user_id=original_user_id
    ).all()
    ret["update_user_chat_benefit_refreshs"] = len(user_chat_benefit_refreshs)
    if user_chat_benefit_refreshs and run:
        await UcbRefreshRecord.filter(user_id=original_user_id).update(
            user_id=new_user_id
        )

    role_configs = await RoleConfig.filter(uid=original_user_id).all()
    ret["update_role_configs"] = len(role_configs)
    if role_configs and run:
        await RoleConfig.filter(uid=original_user_id).update(uid=new_user_id)

    role_audits = await RoleAudit.filter(user_id=original_user_id).all()
    ret["update_role_audits"] = len(role_audits)
    if role_audits and run:
        await RoleAudit.filter(user_id=original_user_id).update(user_id=new_user_id)

    msg_count = await chat_history_dao.count_by_admin(user_id=original_user_id)
    ret["update_msg_count"] = msg_count
    if msg_count and run:
        await chat_history_dao.update_user_id_by_admin(original_user_id, new_user_id)

    if run:
        old_user.status = UserStatus.CHAT_BLACK.value
        await old_user.save()
        ret["update_old_user_status"] = 1

    return response_util.ok(ret)


@no_auth_router.post("/no_auth/user/delete_account")
async def delete_account(user_id: int):
    user = await User.get_or_none(id=user_id)
    if not user:
        return response_util.def_error("用户不存在")
    await user.delete()
    tg_info = await user_service.get_tg_user_by_id(user_id)
    if tg_info:
        await tg_info.delete()
    return response_util.ok(
        {"user_id": user_id, "tg_id": tg_info.tg_id if tg_info else None}
    )


@no_auth_router.get("/no_auth/translate/resource/run")
async def run_translate_resource():
    resources = await TranslateResource.filter(status="pending").all()
    for resource in resources:
        if not resource.text:
            continue
        if not resource.zh_cn:
            resource.zh_cn = resource.text
        resource.zh_tw = await inner_bot.translate(resource.text, Language.ZH_TW.value)
        resource.en_us = await inner_bot.translate(resource.text, Language.EN.value)
        resource.status = "finished"
        resource.review = False
        log.info(f"translate resource {resource.id} finished")
        await resource.save()
    return response_util.ok()


@no_auth_router.get("/no_auth/translate/task/retry")
async def retry_translate_task(task_id: int):
    ret = await translate_task.run_retry_translate_task_by_id(task_id)
    return response_util.ok({"task_id": task_id, "success": ret})


@no_auth_router.get("/no_auth/translate/init/sub_tags")
async def run_init_sub_tags():
    sub_tag_lines = prop_util.read_line_sub_tags()
    for sub_tag_line in sub_tag_lines:
        if not sub_tag_line:
            continue
        if len(sub_tag_line) != 3:
            continue
        sub_tag = sub_tag_line[0]
        sub_tag_en = sub_tag_line[1]
        sub_tag_tw = sub_tag_line[2]
        resource = await TranslateResource.filter(
            key=sub_tag, category=SubTag.__name__
        ).first()
        if not resource:
            continue
        resource.zh_tw = sub_tag_tw
        resource.en_us = sub_tag_en
        resource.status = "finished"
        log.info(
            f"translate resource {resource.id} finished, sub_tag: {sub_tag},en: {sub_tag_en},zh_tw: {sub_tag_tw}"
        )
        await resource.save()
    return response_util.ok()


@no_auth_router.post("/no_auth/roles/broadcast/channel_content")
async def channel_content(role_id: int):
    role_config = await role_loader_service.load_translated_role(
        role_id, Language.ZH.value, ""
    )
    if not role_config or not role_config.privacy:
        return response_util.def_error("角色不存在、已下架或者未公开")
    format_content = await bot_message_service.format_content_replace(
        CHANNEL_CONTENT, 0, role_id
    )
    format_content = format_content.replace(BotReplace.CHANNEL_ID.value, "")
    return response_util.ok(
        {
            "role_id": role_id,
            "content": format_content,
        }
    )


# 角色组推送所有内容
@no_auth_router.post("/no_auth/roles/group_role/broadcast/push_all")
async def broadcast_push_all(chat_id: int):
    ids = await role_loader_service.list_public_ids_by_params(None, None)
    role_group = await tg_config_service.get_group_config_by_chat_id(chat_id)
    if not role_group or not ids:
        return response_util.def_error("角色不存在、已下架或者未公开")
    ids.sort()

    async def sync_send(role_ids: list[int]):
        for id in role_ids:
            await bot_message_service.push_role_group_message(
                chat_id, id, CHANNEL_CONTENT, resend_bottom=False
            )
        await asyncio.sleep(10)

    asyncio.create_task(sync_send(ids))

    return response_util.ok(
        {
            "role_id": ids,
        }
    )


@no_auth_router.post("/no_auth/roles/share/init_message_count")
async def init_message_count():
    async def run_task():
        user_share_roles = (
            await UserRoleShare.filter(message_count=0)
            .only("user_id", "conversation_id")
            .all()
        )
        for user_role in user_share_roles:
            message_count = await chat_history_dao.count_by_user_id_conv_id(
                user_role.user_id, user_role.conversation_id
            )
            if not message_count:
                continue
            log.info(
                f"user_id: {user_role.user_id}, conv_id: {user_role.conversation_id},message_count: {message_count}"
            )
            await UserRoleShare.filter(
                user_id=user_role.user_id, conversation_id=user_role.conversation_id
            ).update(message_count=message_count)

    asyncio.create_task(run_task())
    return response_util.ok()


# @no_auth_router.post("/no_auth/roles/model/check_models")
# async def check_models(models: str = ""):
#     if not models:
#         return response_util.def_error("模型不能为空")
#     model_list = models.split(",")
#     success_list = await lite_llm_bot.check_models_success(model_list)

#     return response_util.ok({"model_list": model_list, "success_list": success_list})


@no_auth_router.get("/no_auth/roles/check_auto_regex")
async def check_auto_regex():
    regex_list = await regex_service.get_all_regex_rules()
    regex_list = [x for x in regex_list if RegexOption.AUTO_RETRY.value in x.options]
    if not regex_list:
        return response_util.ok("没有自动重试的正则")
    roles = await role_loader_service.list_public()
    ret = []
    for role in roles:
        role_data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        hit_rules = []
        for rule in regex_list:
            content = message_utils.run_js_regex(
                rule.regex, rule.replacement, role_data_config.status_block_init
            )
            if content != role_data_config.status_block_init:
                hit_rules.append(f"Hit:{rule.title} -> status_block_init")
            content = message_utils.run_js_regex(
                rule.regex, rule.replacement, role_data_config.first_message
            )
            if role_data_config.format_new_scenes():
                first_message = role_data_config.format_new_scenes()[0].first_message
                content = message_utils.run_js_regex(
                    rule.regex, rule.replacement, first_message
                )
                if content != first_message:
                    hit_rules.append(f"Hit:{rule.title} -> first_message")
        if not hit_rules:
            continue
        ret.append(
            {
                "role_id": role.id,
                "role_name": role.card_name,
                "hit_rules": hit_rules,
            }
        )
        return response_util.ok({"role_hit_rules": ret})


# ==============operation api==================


@no_auth_router.get("/no_auth/operation/clear/new_user_benefit_popup")
async def clear_new_user_benefit(user_id: int):
    await UserTaskRecord.filter(
        user_id=user_id,
    ).delete()
    await chat_history_dao.delete_all_by_user_id_for_admin(user_id)
    await PopupRecord.filter(user_id=user_id).delete()
    return response_util.ok({"user_id": user_id})


@no_auth_router.post("/no_auth/bot_config/set_webhook")
async def set_webhook(bot_token: str, webhook: str):
    bot = tg_config_service.builder_sender_bot_by_token(token=bot_token)
    await bot.set_webhook(
        webhook,
        allowed_updates=["message", "chat_member", "callback_query"],
        secret_token="8e2104bd",
    )
    return response_util.ok()
