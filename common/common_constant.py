from enum import Enum
from typing import Optional

COMMON_USER_NAME = "Charlie"
class Env(str, Enum):
    LOCAL = "local"
    STAG = "stag"
    PROD = "production"

    @classmethod
    def from_str(cls, env: str) -> "Env":
        return cls(env.lower())

class ApiSource(str, Enum):
    US_WEB = "US_WEB"
    HM_WEB = "HM_WEB"
    OVERSEAS_WEB = "OVERSEAS_WEB"
    TMA = "TMA"

class ErrorKey(Enum):
    # 通用错误
    PARAM_ERROR = "PARAM_ERROR"

    # chat相关
    ROLE_DELETED = "ROLE_DELETED"
    CHAT_PRODUCT_NOT_FOUND = "CHAT_PRODUCT_NOT_FOUND"

    # 输入有违禁内容
    ILLEGAL_INPUT_BAN = "ILLEGAL_INPUT_BAN"
    ILLEGAL_CONTENT = "ILLEGAL_CONTENT"
    INNER_ERR = "INNER_ERROR"
    CHAT_SYS_ERR = "CHAT_SYSTEM_ERROR"
    MSG_LOST = "CHAT_MSG_LOST"
    MESSAGE_TOO_LONG = "MESSAGE_TOO_LONG"
    MODEL_OFFLINE_ERROR = "MODEL_OFFLINE_ERROR"
    MODEL_ONLY_FOR_PAID_USER = "MODEL_ONLY_FOR_PAID_USER"
    TAVERN_IMAGE_ERROR = "TAVERN_IMAGE_ERROR"
    CHAT_CONTENT_ERROR = "CHAT_CONTENT_ERROR"

    CHAT_ERROR_AUTO_RETRY = "CHAT_ERROR_AUTO_RETRY"     #聊天错误，自动重试错误
    # 未成年人敏感词
    MINOR_SENSITIVE_WORDS = "MINOR_SENSITIVE_WORDS"

    ##账号相关错误
    USER_IN_BLACKLIST = "USER_IN_BLACKLIST"

    # 付费与权限问题
    # 钻石不足
    INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE"
    INSUFFICIENT_FREE_BENEFIT = "INSUFFICIENT_FREE_BENEFIT"  # 免费权益不足
    # role权限不足
    INSUFFICIENT_ROLE_LEVEL = "INSUFFICIENT_ROLE_LEVEL"
    # 所选模型不匹配
    UNSUPPORTED_MODEL = "UNSUPPORTED_MODEL"

    def message(self)->str:
        message_map = {
            ErrorKey.PARAM_ERROR.value: "请求参数错误，请检查后重试或者截图反馈客服",
            ErrorKey.ROLE_DELETED.value: "当前角色卡（群组）已被删除，请重新选择",
            ErrorKey.CHAT_PRODUCT_NOT_FOUND.value: "当前产品模式不存在（或者已下线），请重新选择聊天模式",
            ErrorKey.ILLEGAL_INPUT_BAN.value: "输入内容包括非法敏感词，账号已被封禁，详细情况请联系管理员",
            ErrorKey.ILLEGAL_CONTENT.value: "输入内容包括非法敏感词，请重新输入（当前消息不扣费）",
            ErrorKey.INNER_ERR.value: "服务内部错误，反馈客服解决，并且稍后尝试",
            ErrorKey.CHAT_SYS_ERR.value: "\n系统错误，请稍后再试（本条消息不扣费）",
            ErrorKey.MSG_LOST.value: "历史消息不一致，请返聊天历史页，重新进入聊天（当前请求不扣费）",
            ErrorKey.MESSAGE_TOO_LONG.value: "消息过长，请删减后重新输入（当前消息不扣费）",
            ErrorKey.MODEL_OFFLINE_ERROR.value: "模型已下线，请切换：\n（选择的模式已下线，请切换为其他模式聊天）（本条请求不扣费）",
            ErrorKey.MODEL_ONLY_FOR_PAID_USER.value: "模型资源稀缺，仅限充值用户。目前充值套餐送限时惊喜福利，快来薅羊毛吧。",
            ErrorKey.TAVERN_IMAGE_ERROR.value: "图片不符合规范（请上传从酒馆或者其他平台导出的，带有卡片信息图片）",
            ErrorKey.CHAT_CONTENT_ERROR.value: "\n返回内容有异常，请重试，(本条消息不扣费）",
            ErrorKey.CHAT_ERROR_AUTO_RETRY.value: "返回内容有异常，正在自动重试，请稍等（本条消息不扣费）",
            ErrorKey.MINOR_SENSITIVE_WORDS.value: "未成年人敏感词",
            ErrorKey.USER_IN_BLACKLIST.value: "账户异常,请求被拒绝（请联系客服",
            ErrorKey.INSUFFICIENT_BALANCE.value: "余额不足，请充值",
            ErrorKey.INSUFFICIENT_FREE_BENEFIT.value: "当前余额不足，请切换免费聊天模式或充值",
            ErrorKey.INSUFFICIENT_ROLE_LEVEL.value: "角色无权限，请切换其他角色",
            ErrorKey.UNSUPPORTED_MODEL.value: "该角色不支持您当前所选择的聊天模式，请切换到其他模式（本条请求不扣费）",
            
        }
        return message_map.get(self.value, "未知错误")
    
    @staticmethod
    def all_values():
        return [error.value for error in ErrorKey]


class ErrorCode(Enum):
    #公共错误，不做处理，读取message错误提示
    COMMON_ERROR = -1
    # 成功
    OK = 0
    #参数不支持
    PARAM_ERROR = 1001
    NOT_ALLOWED = 1002
    # 模型不支持
    UN_SUPPORT_MODEL = 1003
    # 余额不足
    INSUFFICIENT_BALANCE = 1004

    #存在敏感词
    ILLEGAL_CONTENT = 1005


# 未来要删除，使用ErrorCode
# 这个是临时的
class ERROR_CODE(Enum):
    OK = 0
    PARAM_ERROR = 1001
    NOT_ALLOWED = 1002
    UN_SUPPORT_MODEL = 1003


class UserModelFilter(Enum):
    # 默认分组（付费用户）
    DEFAULT = "DEFAULT"
    # 免费权益（免费用户）
    FREE_BENEFIT = "FREE_BENEFIT"
    # 活动用户 (下线)
    JOIN_ACTIVITY = "JOIN_ACTIVITY"

    @staticmethod
    def display(user_model_filter: str) -> str:
        if user_model_filter == UserModelFilter.DEFAULT.value:
            return "默认分组（付费用户）"
        if user_model_filter == UserModelFilter.FREE_BENEFIT.value:
            return "免费权益（免费用户）"
        if user_model_filter == UserModelFilter.JOIN_ACTIVITY.value:
            return "活动用户（下线）"
        return "未知分组"


# 以后这个是标准的
class LlmModel(Enum):
    CLAUDE_3_HAIKU = "claude-3-haiku"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_OPUS = "claude-3-opus"
    GEMINI_1_5_FLASH = "gemini-1.5-flash"
    GEMINI_1_5_PRO = "gemini-1.5-pro"
    CLAUDE_3_5_SONNET = "claude-3.5-sonnet"
    CLAUDE_3_5_SONNET_V2 = "claude-3.5-sonnet-v2"
    GEMINI_FLASH_1_5_8B = "gemini-flash-1.5-8b"
    CLAUDE_3_5_HAIKU = "claude-3.5-haiku"
    CLAUDE_3_5_HAIKU_PLUS = "claude-3.5-haiku-plus"
    CLAUDE_3_5_SONNET_V2_PLUS = "claude-3.5-sonnet-v2-plus"
    DEEPSEEK_3_DP8 = "deepseek-3-dp8"
    DEEPSEEK_V3_FP8 = "deepseek-v3-fp8"
    DEEPSEEK_V3_VOLCENGINE = "deepseek-v3-volcengine"
    DEEPSEEK_R1_VOLCENGINE = "deepseek-r1-volcengine"
    GEMINI_2_0_FLASH = "gemini-2.0-flash"
    QWEN_TURBO = "qwen-turbo"
    MICROSOFT_PHI4 = "microsoft-phi4"
    DEEPSEEK_R1 = "deepseek-r1"
    CLAUDE_3_7_SONNET = "claude-3.7-sonnet"
    # deepseek-r1-chutes
    DEEPSEEK_R1_CHUTES = "deepseek-r1-chutes"
    # mistral-small
    MISTRAL_SMALL = "mistral-small"
    # mistral-nemo
    MISTRAL_NEMO = "mistral-nemo"
    # mistral-small-mistral
    MISTRAL_SMALL_MISTRAL = "mistral-small-mistral"

class LlmRequestCluster(Enum):
    PRO = "PRO"
    FREE = "FREE"
class LlmCacheType(Enum):
    EPHEMERAL_FLAG = "EPHEMERAL_FLAG"  # 临时标记
    CONTEXT_API = "CONTEXT_API"  # 上下文API


class TgGroupChatId(Enum):
    PAID_MONITOR = "-1002560551936"
    #@fancy_monitor
    FREE_MONITOR = "-1002260372329"

class AdminUserRole(Enum):
    ADMIN = "ADMIN"
    OP = "OP"
    API = "API"


# role config constant
class RoleChatType(Enum):
    CHAT = "Chat"
    ROLE_PLAY = "RolePlay"
    GENERAL = "General"
class RoleStatusBlockType(Enum):
    NORMAL = "normal"
    COLLAPSE = "collapse"

class ChatApiVersion(Enum):
    V1 = "v1"
    V2 = "v2"
class LiteLlmErrorType(Enum):
    UNKNOWN = "unknown"
    #request error
    REQUEST_ERROR = "request_error"
    TIMEOUT = "timeout"
    CONNECTION_ERROR = "connection_error"
    STOP_ERROR = "stop_error"
    API_ERROR = "api_error"

    #content error
    APOLOGIZE = "apologize"
    TOO_SHORT = "too_short"
    OOC_ERROR = "ooc_error"
    INVALID_CHAR = "invalid_char"
    REP_TOO_SHORT = "rep_too_short"
    AUTO_RETRY_RULE = "auto_retry_rule"
    #乱码
    GIBBERISH = "gibberish"

    @staticmethod
    def display(val:str):
        desc_map = {
            LiteLlmErrorType.UNKNOWN.value: "未知错误",
            LiteLlmErrorType.REQUEST_ERROR.value: "请求错误",
            LiteLlmErrorType.TIMEOUT.value: "请求超时",
            LiteLlmErrorType.CONNECTION_ERROR.value: "连接错误",
            LiteLlmErrorType.STOP_ERROR.value: "停止错误",
            LiteLlmErrorType.API_ERROR.value: "API错误",
            LiteLlmErrorType.APOLOGIZE.value: "AI道歉",
            LiteLlmErrorType.TOO_SHORT.value: "回复过短",
            LiteLlmErrorType.OOC_ERROR.value: "OOC错误",
            LiteLlmErrorType.INVALID_CHAR.value: "超过100个字符无标点符号",
            LiteLlmErrorType.REP_TOO_SHORT.value: "Rep回复过短",
            LiteLlmErrorType.AUTO_RETRY_RULE.value: "自动重试规则错误",
            LiteLlmErrorType.GIBBERISH.value: "回复包含乱码",
        }
        return desc_map.get(val, "未知错误")


class ModelEventType(Enum):
    MAN = "man"  # 备用，后续可能使用
    CHAT_MAN = "chat_man"  # 聊天页面手动切换模型事件
    CHAT_AUTO = "chat_auto"  # 聊天页面自动切换模型事件
    CHAT_ENTRY = "chat_entry"  # 进入聊天页面事件
    CHAT_CHANNEL_AUTO = "chat_channel_auto"  # 切换聊天通道
    CHAT_AUTO_FREE = "chat_auto_free"  # 自动切换到免费通道



class ChatModeType(Enum):
    SINGLE = "single"
    GROUP = "group"

    @staticmethod
    def all():
        return [ChatModeType.SINGLE.value, ChatModeType.GROUP.value]

class ChatChannel(Enum):
    #付费模式
    PAID = "PAID"
    #免费模式
    FREE_BENEFIT = "FREE_BENEFIT"

    def bot_display(self) -> str:
        if self == ChatChannel.FREE_BENEFIT:
            return "共享（使用免费次数）"
        if self == ChatChannel.PAID:
            return "尊享（使用💎与🟡）"
        return ""
    def display(self) -> str:
        name_map = {
            ChatChannel.FREE_BENEFIT: "共享（使用免费次数）",
            ChatChannel.PAID: "尊享（使用💎与🟡）"
        }
        return name_map.get(self, "")
    
    def short_display(self)->str:
        name_map = {
            ChatChannel.FREE_BENEFIT: "共享",
            ChatChannel.PAID: "尊享"
        }
        return name_map.get(self, "")
    @staticmethod
    def safe_parse(val:str):
        if val and val in [x.value for x in ChatChannel]:
            return ChatChannel(val)
        return ChatChannel.PAID

class RoleLevelType(Enum):
    NORMAL = "normal"
    PREMIUM = "premium"

    @staticmethod
    def replayMaxTokenConfig() -> dict:
        return {RoleLevelType.NORMAL.value: 1000, RoleLevelType.PREMIUM.value: 2000}

    @staticmethod
    def get_replay_max_token(role_level: str) -> int:
        return RoleLevelType.replayMaxTokenConfig()[role_level]

    @staticmethod
    def get_max_context(role_level: str, def_max: int) -> int:
        return def_max if role_level == RoleLevelType.NORMAL.value else 12000


# 玩法类型
class RolePlayType(Enum):
    # 对手戏
    RIVALRY = "RIVALRY"
    # 推剧情
    PLOT_INFERENCE = "PLOT_INFERENCE"
    # 系统工具
    SYSTEM = "SYSTEM"

    def to_desc(self) -> str:
        ret = {}
        ret[RolePlayType.RIVALRY.value] = "对手戏(AI不抢话)"
        ret[RolePlayType.PLOT_INFERENCE.value] = "推剧情"
        ret[RolePlayType.SYSTEM.value] = "系统工具"
        return ret[self.value]


class Language(Enum):
    ZH = "zh"
    EN = "en"
    ZH_TW = "zh-TW"

    @staticmethod
    def fetch_all():
        return [Language.ZH.value, Language.EN.value, Language.ZH_TW.value]
    
    @staticmethod
    def fetch_config(api_source: Optional[ApiSource] = None) -> list[dict]:
        ret = []
        if api_source != ApiSource.OVERSEAS_WEB:
            ret.append({"name": "简体中文", "key": Language.ZH.value})
        ret.append({"name": "繁體中文", "key": Language.ZH_TW.value})
        ret.append({"name": "English", "key": Language.EN.value})
        return ret

    @staticmethod
    def load_desc(language: str) -> str:
        ret = {}
        ret[Language.ZH.value] = "简体中文"
        ret[Language.EN.value] = "English"
        ret[Language.ZH_TW.value] = "繁體中文"
        if language not in ret:
            return ret[Language.ZH.value]
        return ret[language]

    @staticmethod
    def load_english(language: str) -> str:
        ret = {}
        ret[Language.ZH.value] = "Simplified Chinese"
        ret[Language.EN.value] = "English"
        ret[Language.ZH_TW.value] = "Traditional Chinese"
        if language not in ret:
            return ret[Language.ZH.value]
        return ret[language]

    @staticmethod
    def val_and_region(language: str) -> str:
        ret = {}
        ret[Language.ZH.value] = "zh_CN"
        ret[Language.EN.value] = "en_US"
        ret[Language.ZH_TW.value] = "zh_TW"
        if language not in ret:
            return ret[Language.ZH.value]
        return ret[language]


class AuditStatus(Enum):
    # 编辑中
    EDITING = "editing"
    # 审核中
    AUDITING = "auditing"
    # 审核通过
    APPROVED = "approved"
    # 审核拒绝
    REJECTED = "rejected"

    @staticmethod
    def get_desc(status: str) -> str:
        if status == AuditStatus.EDITING.value:
            return "编辑中"
        if status == AuditStatus.AUDITING.value:
            return "审核中"
        if status == AuditStatus.APPROVED.value:
            return "审核通过"
        if status == AuditStatus.REJECTED.value:
            return "审核拒绝"
        return ""


class RoleSubTag(Enum):
    STATUS = "状态栏"
    ROLE_PLAY = "角色扮演"
    LIGHT_CHAT = "轻聊天"


class RoleTag(Enum):
    # 精选
    CHOSEN = "精选"
    # 热门
    HOT = "热门"
    # 上新
    NEW = "上新"
    # 群组
    GROUP = "群聊"

    USER_ROLE = "用户角色卡"

    USER_GROUP = "用户群组卡"

    USER_PUBLIC_ROLE = "用户角色公开卡"
    USER_GROUP_PUBLIC_ROLE = "用户群组公开卡"
    DELETE_PUBLIC_ROLE = "已删除公开卡"

    # 审核中
    AUDITING = "审核中"

    # 精选
    CHOSEN_NEW_ORDER = "精选-新排序"

    def translate(self, language: str) -> str:
        return RoleTag.translate_by_source(self.value, language)

    @staticmethod
    def from_by_language(tag: str, language: str) -> str:
        mid_list = [
            RoleTag.CHOSEN.value,
            RoleTag.HOT.value,
            RoleTag.NEW.value,
            RoleTag.GROUP.value,
        ]
        for mid in mid_list:
            if RoleTag.translate_by_source(mid, language) == tag:
                return mid
        return tag

    @staticmethod
    def translate_by_source(source: str, language: str) -> str:
        ret = {}
        ret[RoleTag.CHOSEN.value] = {"zh": "精选", "en": "Chosen", "zh-TW": "精選"}
        ret[RoleTag.HOT.value] = {"zh": "热门", "en": "Hot", "zh-TW": "熱門"}
        ret[RoleTag.NEW.value] = {"zh": "上新", "en": "New", "zh-TW": "上新"}
        ret[RoleTag.GROUP.value] = {"zh": "群聊", "en": "Group", "zh-TW": "群聊"}
        if source in ret and language is not None and language in ret[source]:
            return ret[source][language]
        return source

    @staticmethod
    def online_tags(language: str = Language.ZH.value) -> list[str]:
        ret_list = [
            RoleTag.CHOSEN.value,
            RoleTag.HOT.value,
            RoleTag.NEW.value,
            RoleTag.GROUP.value,
        ]
        return [RoleTag.translate_by_source(tag, language) for tag in ret_list]

    @staticmethod
    def old_online_tags(language: str = Language.ZH.value) -> list[str]:
        ret_list = [
            RoleTag.CHOSEN.value,
            RoleTag.HOT.value,
            RoleTag.NEW.value,
        ]
        return [RoleTag.translate_by_source(tag, language) for tag in ret_list]


class RoleFilterTag(Enum):
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"

    CHOSEN = "CHOSEN"
    HOT = "HOT"
    NEW = "NEW"
    RANKING = "RANKING"
    GROUP = "GROUP"

    @staticmethod
    def display_tags():
        return [
            RoleFilterTag.CHOSEN,
            RoleFilterTag.HOT,
            RoleFilterTag.NEW,
            RoleFilterTag.GROUP,
        ]
        # return [x for x in RoleFilterTag]

    @staticmethod
    def summary_ranking_tags():
        return [RoleFilterTag.DAILY, RoleFilterTag.WEEKLY, RoleFilterTag.MONTHLY]


class RoleFilterChatType(Enum):
    REAL_ROLE_CHAT = "REAL_ROLE_CHAT"
    # 独立标签剧情
    REAL_ROLE_PLOT = "REAL_ROLE_PLOT"
    # 多角色剧情
    MULTI_ROLE_PLOT = "MULTI_ROLE_PLOT"

    def real_role(self) -> bool:
        return self in [
            RoleFilterChatType.REAL_ROLE_CHAT,
            RoleFilterChatType.REAL_ROLE_PLOT,
        ]

    def chat_types(self) -> list[str]:
        if self == RoleFilterChatType.REAL_ROLE_CHAT:
            return [RoleChatType.CHAT.value]
        return [RoleChatType.ROLE_PLAY.value, RoleChatType.GENERAL.value]


class RoleSortType(Enum):
    # 点赞榜
    LIKE = "LIKE"
    # 收藏榜
    FAVORITE = "FAVORITE"
    # 更新榜
    UPDATE = "UPDATE"

    # 日榜
    DAILY = "DAILY"
    # 周榜
    WEEKLY = "WEEKLY"
    # 月榜
    MONTHLY = "MONTHLY"

    @staticmethod
    def summary_rank_types():
        return [RoleSortType.DAILY, RoleSortType.WEEKLY, RoleSortType.MONTHLY]

    @staticmethod
    def common_rank_types():
        return [RoleSortType.LIKE, RoleSortType.FAVORITE, RoleSortType.UPDATE]

    def to_filter_tag(self) -> RoleFilterTag | None:
        if self == RoleSortType.DAILY:
            return RoleFilterTag.DAILY
        if self == RoleSortType.WEEKLY:
            return RoleFilterTag.WEEKLY
        if self == RoleSortType.MONTHLY:
            return RoleFilterTag.MONTHLY
        return None


class PresetReplace(Enum):
    CHAR = "{{char}}"
    USER = "{{user}}"
    SCENARIO = "{{scenario}}"
    PERSONALITY = "{{personality}}"
    CHAT_HISTORY = "{{chat_history}}"
    REPLAY_MAX_COUNT = "{{replay_max_count}}"
    CHAR_DESCRIPTION = "{{char_description}}"
    DIALOGUE_EXAMPLES = "{{dialogue_examples}}"
    STATUS_BAR = "{{StatusBar}}"
    STATUS_RULES = "{{StatusRules}}"
    LAST_MESSAGE_ID = "{{last_message_id}}"
    LAST_MESSAGE = "{{last_message}}"
    LANGUAGE = "{{language}}"
    FIRST_MESSAGE = "{{first_message}}"
    # 世界书关键词触发
    KEY_MATCHED = "{{key_matched}}"


class PresetIdentifier(Enum):
    CHAT_HISTORY = "chatHistory"
    SCENARIO = "scenario"
    CHAR_DESCRIPTION = "charDescription"
    PERSONALITY = "charPersonality"
    DIALOGUE_EXAMPLES = "dialogueExamples"
    WORLD_INFO_BEFORE = "worldInfoBefore"
    WORLD_INFO_AFTER = "worldInfoAfter"


class PresetSwitchPrefix(Enum):
    CHAT_EXAMPLE = "CE:"
    PERSONALITY = "PD:"
    SCENARIO = "SC:"
    FIRST_MSG = "FM:"
    WORLD_INFO = "WI:"


class PresetChatRegex(Enum):
    CHAT = "(?:^|:)Chat:"
    ROLE_PLAY = "(?:^|:)RolePlay:"
    GENERAL = "(?:^|:)General:"


class PresetStatusRegex(Enum):
    STATUS_ON = "(?:^|:)StatusOn:"
    STATUS_OFF = "(?:^|:)StatusOff:"


class PresetExtraParam(Enum):
    MIN_P = "min_p"


class ChatPlatform(Enum):
    TMA = "TMA"
    CHAT_BOT = "CHAT_BOT"
    WEB = "WEB"

    @staticmethod
    def list_display() -> list:
        return [
            {"key": ChatPlatform.TMA.value, "display": "小程序"},
            {"key": ChatPlatform.CHAT_BOT.value, "display": "聊天Bot"},
            {"key": ChatPlatform.WEB.value, "display": "网站"},
        ]


class ChatFinishReason(Enum):
    LENGTH = "length"
    STOP = "stop"


class PopupType(Enum):
    ROLE_CREATE = "role_create"
    PAY_SWITCH_MODEL = "pay_switch_model"
    BOT_PAY_SWITCH_MODEL = "bot_pay_switch_model"
    COMMON = "common"
    DIAMOND_ACTIVITY_ENROLL_SUCCESS = "d_act_enroll_succ"
    DIAMOND_ACTIVITY_NOTICE = "d_act_notice"
    DIAMOND_ACTIVITY_NOTICE_BOT = "d_act_notice_bot"
    DIAMOND_ACTIVITY_RECHARGE = "d_act_recharge"
    DIAMOND_ACTIVITY_RECHARGE_BOT = "d_act_recharge_bot"

    # 新用户福利（渠道用户）
    NEW_USER_BENEFIT = "new_user_benefit"

    # 新用户福利（非渠道用户）
    NEW_USER_BENEFIT_NON_CHANNEL = "new_user_benefit_non_channel"



class PopupPosition(Enum):
    # 首页
    HOME = "HOME"
    # start命令
    CHAT_BOT_START = "CHAT_BOT_START"
    # 聊天输入
    USER_INPUT = "USER_INPUT"

    @staticmethod
    def list_display() -> list:
        return [
            {"key": PopupPosition.HOME.value, "display": "卡片页面"},
            {"key": PopupPosition.CHAT_BOT_START.value, "display": "Start命令"},
            {"key": PopupPosition.USER_INPUT.value, "display": "聊天输入"},
        ]

    @staticmethod
    def allow_platform() -> dict:
        return {
            PopupPosition.HOME.value: [ChatPlatform.TMA.value, ChatPlatform.WEB.value],
            PopupPosition.CHAT_BOT_START.value: [ChatPlatform.CHAT_BOT.value],
            PopupPosition.USER_INPUT.value: [ChatPlatform.CHAT_BOT.value],
        }


class PopupShowPeriod(Enum):
    # 每次
    EVERY_TIME = "EVERY_TIME"
    # 一次
    ONCE = "ONCE"
    # 每天
    EVERY_DAY = "EVERY_DAY"
    # 每周
    EVERY_WEEK = "EVERY_WEEK"

    # 每月
    EVERY_8H = "EVERY_8H"

    @staticmethod
    def list_display() -> list:
        return [
            {"key": PopupShowPeriod.EVERY_TIME.value, "display": "每次"},
            {"key": PopupShowPeriod.ONCE.value, "display": "只弹一次"},
            {"key": PopupShowPeriod.EVERY_DAY.value, "display": "每天一次"},
            {"key": PopupShowPeriod.EVERY_WEEK.value, "display": "每周一次"},
            {"key": PopupShowPeriod.EVERY_8H.value, "display": "8H一次"},
        ]

#用户范围
class PopupUserScope(Enum):
    ALL = "ALL"  # 全部用户
    POPUP_AFTER_REG = "POPUP_AFTER"  # 弹窗上线后注册用户
    #注册24小时之内用户
    REG_24H = "REG_24H"  # 注册24小时之内用户

    @staticmethod
    def list_display() -> list:
        return [
            {"key": PopupUserScope.ALL.value, "display": "全部用户"},
            {"key": PopupUserScope.POPUP_AFTER_REG.value, "display": "弹框开始后新用户"},
            {"key": PopupUserScope.REG_24H.value, "display": "注册新用户24H内"},
        ]
class AuthorDefaultName(Enum):
    ADMIN = "幻梦平台"
    ANONYMOUS = "匿名"


class CosPrefix(Enum):
    AVATAR = "image/avatar"
    ROLE = "image/card"
    VIDEO = "video"
    CHAT_BACKGROUND = "image/background"


class UploadImageCategory(Enum):
    DEFAULT = "DEFAULT"
    CHAT_BACKGROUND = "CHAT_BACKGROUND"


class ImageCheckSuggestions(Enum):
    PASS = "Pass"
    REVIEW = "Review"
    BLOCK = "Block"

    @staticmethod
    def from_str(suggestions: str) -> "ImageCheckSuggestions":
        for item in ImageCheckSuggestions:
            if item.value == suggestions:
                return item
        return ImageCheckSuggestions.PASS


class ImageCheckLabel(Enum):
    NORMAL = "Normal"
    PORN = "Porn"
    ABUSE = "Abuse"
    AD = "Ad"
    CUSTOM = "Custom"
    UNKNOWN = "Unknown"

    @staticmethod
    def from_str(label: str) -> "ImageCheckLabel":
        for item in ImageCheckLabel:
            if item.value == label:
                return item
        return ImageCheckLabel.CUSTOM


class S3Bucket(Enum):
    DEF_BUCKET = "ai-data.424224.xyz"
    GROUP_IMAGE_BUCKET = "group-image.424224.xyz"


class S3BucketUrlPrefix(Enum):
    DEF_BUCKET = "https://s3.ap-southeast-1.amazonaws.com/ai-data.424224.xyz/"
    GROUP_IMAGE_BUCKET = (
        "https://s3.ap-southeast-1.amazonaws.com/group-image.424224.xyz/"
    )

    @staticmethod
    def get_url_prefix(bucket: S3Bucket) -> str:
        if bucket == S3Bucket.DEF_BUCKET:
            return S3BucketUrlPrefix.DEF_BUCKET.value
        return S3BucketUrlPrefix.GROUP_IMAGE_BUCKET.value


class TaskStatus(Enum):
    TODO = "TODO"  # 未完成
    DONE = "DONE"  # 已完成
    UN_RECEIVE = "UN_RECEIVE"  # 领取奖励

    def get_btn(self, to_btn: str) -> str:
        if self == TaskStatus.TODO:
            return to_btn
        ret = {TaskStatus.DONE: "已完成", TaskStatus.UN_RECEIVE: "领取奖励"}
        return ret[self]


class TaskResetPeriod(Enum):
    ONCE = "ONCE"  # 只弹一次
    DAILY = "DAILY"  # 每天
    AFTER_24H = "AFTER_24H"  # 24小时间隔
    EVERY_TIME = "EVERY_TIME"  # 每次


class WelfareTaskType(Enum):
    INVITATION = "INVITATION"
    CHAT_JOIN = "CHAT_JOIN"
    CHECK_IN = "CHECK_IN"
    RECHARGE_PRODUCT = "RECHARGE_PRODUCT"
    FIRST_PUBLISH_CARD = "FIRST_PUBLISH_CARD"
    PUBLISH_CARD = "PUBLISH_CARD"
    WEB_CHECK_IN = "WEB_CHECK_IN"
    # 直接领取
    DIRECT_RECEIVE = "DIRECT_RECEIVE"

class ActionType(Enum):

    LINK = "link"  # 链接
    API = "api"  # 接口
    INNER_LINK = "inner_link"  # 内部链接
    #回调
    CALLBACK_CMD = "callback_cmd"  # 回调命令

    @staticmethod
    def list_display() -> list:
        return [
            {"key": "", "display": "无跳转"},
            {"key": ActionType.LINK.value, "display": "Web链接"},
            {"key": ActionType.API.value, "display": "Api接口"},
            {"key": ActionType.INNER_LINK.value, "display": "内部链接"},
            {"key": ActionType.CALLBACK_CMD.value, "display": "回调命令"},
        ]


    
class RewardType(Enum):
    # 金币
    COIN = "COIN"
    CHAT_BENEFIT = "CHAT_BENEFIT"
class BotSettingType(Enum):
    MODEL = "model"
    STATUS_BLOCK = "status_block"
    CHAT_CHANNEL = "chat_channel"
    MODEL_V1 = "model_v1"  # 兼容旧版本的模型设置
    VOICE = "voice"  # 语音设置
    VOICE_SELECTED = "voice_selected"  # 已选语音

class BotType(Enum):
    TMA = "TMA"  # 伴侣小程序
    CHAT_BOT = "CHAT_BOT"  # 聊天bot
    GROUP = "GROUP"  # 群组
    CHANNEL = "CHANNEL"  # 频道
    HELPER = "HELPER"  # 功能bot
    WEB = "WEB"  # 网页
    CUSTOMER = "CUSTOMER"  # 客服
    BUSINESS = "BUSINESS"  # 商务
    CHANNEL_WELFARE = "CHANNEL_WELFARE"  # 福利频道


class BotCategory(Enum):
    TMA = "TMA"  # 伴侣小程序
    CHAT_BOT = "CHAT_BOT"  # 聊天bot
    HELPER = "HELPER"  # 功能bot
    CUSTOMER = "CUSTOMER"  # 客服
    BUSINESS = "BUSINESS"  # 商务
    # CHANNEL_WELFARE = "CHANNEL_WELFARE" #福利频道
    LOGIN_HELPER = "LOGIN_HELPER"  # 登录助手
    # ADV_HELPER = "ADV_HELPER"   #广告功能bot
    FORWARDED_HELPER = "FORWARDED_HELPER"  # 转发功能bot


class ChannelCategory(Enum):
    ROLE = "ROLE"
    WELFARE = "WELFARE"
    AI_ROLE = "AI_ROLE"
    AI_ROLE_CHAT = "AI_ROLE_CHAT"


class GroupCategory(Enum):
    CHAT = "CHAT"
    ROLE = "ROLE"


class DfyApiKey(Enum):
    ROLE_DESC_TRANSLATE = "app-VlWkbpMKKD3NWAqZZYJ0AibM"
    SHARE_CONTENT_CHECK = "app-aferoZmL2mALQ80cjhakN5S3"


class ProductType(Enum):
    CHAT = "chat"
    TTS = "tts"
    PHOTO = "photo"
    SHARE = "share"
    IMPERSONATE = "impersonate"
    CHAT_CONTINUE = "chat_continue"
    MANUAL = "manual"
    ACTIVITY_DIAMOND_SEASON = "activity_diamond_season"
    PHOTO_RETRY = "photo_retry"
    GENERATE_STATUS_BLOCK = "generate_status_block"  # 生成
    SAVE_STATUS_BLOCK = "save_status_block"  # 保存

class ProductPermission(Enum):
    ALL_USER = "ALL_USER"
    PAID_USER = "PAID_USER"


class BotResourcePosition(Enum):
    # 用户底部
    USER_BOT_BOTTOM = "user_bot_bottom"
    # 频道底部
    CHANNEL_BOTTOM = "channel_bottom"

    # 置顶消息
    PINED_MESSAGE = "pined_message"

    # 简介
    DESCRIPTION = "description"
    SHORT_DESCRIPTION = "short_description"

    # 主动推送消息
    PUSH_MESSAGE = "push_message"


class ResourcePlatform(Enum):
    BOT = "BOT"
    GROUP = "GROUP"
    CHANNEL = "CHANNEL"


class BotReplace(Enum):
    MAIN_TMA_BOT = "{{main_tma_bot}}"
    MAIN_CHAT_BOT = "{{main_chat_bot}}"
    MAIN_GROUP = "{{main_group_bot}}"
    MAIN_CHANNEL = "{{main_channel}}"
    MAIN_WELFARE_CHANNEL = "{{main_welfare_channel}}"
    MAIN_CUSTOMER_BOT = "{{main_customer_bot}}"
    MAIN_BUSINESS_BOT = "{{main_business_bot}}"
    CHANNEL_ID = "{{channel_id}}"
    MAIN_GROUP_ROLE = "{{main_group_role}}"


class UserReplace(Enum):
    TG_FIRST_NAME = "{{tg_first_name}}"
    TG_LAST_NAME = "{{tg_last_name}}"


class RoleReplace(Enum):
    ROLE_NAME = "{{role_name}}"
    CARD_NAME = "{{card_name}}"
    ROLE_INTRO = "{{role_intro}}"
    ROLE_SUB_TAGS = "{{role_sub_tags}}"
    ROLE_ID = "{{role_id}}"
    AUTHOR = "{{author}}"
    SUPPORT_MODELS = "{{support_models}}"
    TOKEN_COUNT = "{{token_count}}"
    ROLE_FILTER_CHAT_TYPE = "{{role_filter_chat_type}}"
    ROLE_PLAY_TYPE = "{{role_play_type}}"


class ChatBenefitType(Enum):
    # 免费次数
    FREE_CHAT_TIMES = "FREE_CHAT_TIMES"
    # 每日领取次数
    DAILY_RECEIVE_TIMES = "DAILY_RECEIVE_TIMES"

    # 每小时领取次数
    HOURLY_RECEIVE_TIMES = "HOURLY_RECEIVE_TIMES"


class ChatBenefitEnum(str, Enum):
    RECHARGE = "RECHARGE"  # 支付
    INVITATION = "INVITATION"  # 邀请
    WELFARE = "WELFARE"  # 福利
    DAILY_FREE = "DAILY_FREE"  # 每日免费
    PUBLISH_CARD = "PUBLISH_CARD"  # 发布角色


class UserBenefitCategory(Enum):
    # 充值
    RECHARGE = "RECHARGE"
    # 赠送
    GIFT = "GIFT"
    # FREE
    FREE = "FREE"

    @staticmethod
    def to_desc_map() -> dict:
        ret = {}
        ret[UserBenefitCategory.RECHARGE.value] = "充值权益"
        ret[UserBenefitCategory.GIFT.value] = "赠送权益"
        ret[UserBenefitCategory.FREE.value] = "免费权益"
        return ret

    def benefit_types(self) -> list[ChatBenefitEnum]:
        if self == UserBenefitCategory.RECHARGE:
            return [ChatBenefitEnum.RECHARGE]
        if self == UserBenefitCategory.GIFT:
            return [
                ChatBenefitEnum.INVITATION,
                ChatBenefitEnum.WELFARE,
                ChatBenefitEnum.PUBLISH_CARD,
            ]
        if self == UserBenefitCategory.FREE:
            return [ChatBenefitEnum.DAILY_FREE]
        return []


# 官方背景图

OFFICIAL_BACKGROUND = [
    "https://ai-data.424224.xyz/image/background/e807948cb02f42379ed608db8d65bbe9.jpg",
    "https://ai-data.424224.xyz/image/background/5e349100983c442f88c97c0024ab9344.jpg",
    "https://ai-data.424224.xyz/image/background/18fa5d4238e5459796569a3570c16481.jpg",
    "https://ai-data.424224.xyz/image/background/e0ade2bfe4624347a3fe7414e38a111e.jpg",
    "https://ai-data.424224.xyz/image/background/2487b72d4b62411a9efb873be4cab9af.jpg",
    "https://ai-data.424224.xyz/image/background/f0991603911d42bea471142693ad0a97.jpg",
    "https://ai-data.424224.xyz/image/background/b8b7b4e7b07946f1a9da769ec07fc91c.jpg",
]


class RpDisplayFilter(Enum):
    # 普通套餐付费用户
    NORMAL_RECHARGE_PAYED_USER = "NORMAL_RECHARGE_PAYED_USER"
    # 购买后隐藏
    HIDE_AFTER_PURCHASE = "HIDE_AFTER_PURCHASE"
    # 新用户专享套餐
    NEW_USER_ONLY = "NEW_USER_ONLY"
    # 高充值用户专享套餐
    HIGH_RECHARGE_USER_ONLY = "HIGH_RECHARGE_USER_ONLY"
    # 非首充用户专享
    NON_FIRST_RECHARGE_USER = "NON_FIRST_RECHARGE_USER"
    # 永久保留
    PERMANENT_RETAIN = "PERMANENT_RETAIN"
    # 图片Bot专享
    IMAGE_BOT_ONLY = "IMAGE_BOT_ONLY"

class VoiceContentType(str, Enum):
    ALL = 'ALL'
    INSIDE_ONLY = 'INSIDE_ONLY'

class RechargeRouteStrategy(str, Enum):
    AUTO = 'AUTO'
    MANUAL = 'MANUAL'