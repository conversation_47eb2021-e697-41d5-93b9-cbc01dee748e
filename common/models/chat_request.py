from pydantic import BaseModel

from common.common_constant import ChatApiVersion, ChatModeType, Language, ModelEventType
from common.role_model import UserRoleBrief

class PresetConfig(BaseModel):
    presets: dict
    model: str
    nsfw: str
    scenario: str


# message为空代表没有消息
# 消息重试：retry_message_id 重试消息ID
# 网络错误重试：isRetry=True，message不为空
class ChatRequest(BaseModel):
    mode_type: str = ChatModeType.SINGLE.value
    group_id: int = 0
    role_id: int = 0  # 说话者ID（角色ID，0为用户）
    message: str = ""
    conversation_id: str = ""
    isRetry: bool = False
    auto_retry: bool = False  # 是否自动重试
    chat_continue: bool = False  # 是否继续对话
    retry_message_id: str = ""  # 重试消息ID
    last_message_id: str = ""  # 上一条消息ID
    last_message_version: str = ""  # 上一条消息版本
    language: str = Language.ZH.value
    api_version: str = "v1"  # API版本
    platform:str = "TMA"

    def verify_param(self):
        if self.mode_type == ChatModeType.SINGLE.value and self.role_id == 0:
            return False
        if self.mode_type == ChatModeType.GROUP.value and self.group_id == 0:
            return False
        if self.mode_type not in [ChatModeType.SINGLE.value, ChatModeType.GROUP.value]:
            return False
        return True

    def get_mode_target_id(self):
        if self.mode_type == ChatModeType.SINGLE.value:
            return self.role_id
        return self.group_id

    def new_user_message(self):
        if self.mode_type == ChatModeType.SINGLE.value:
            if not self.isRetry:
                return True
        if self.mode_type == ChatModeType.GROUP.value:
            if self.role_id == 0:
                return True
        return False


class ImpersonateRequest(BaseModel):
    mode_type: str = ChatModeType.SINGLE.value
    group_id: int = 0
    role_id: int = 0  # 说话者ID（角色ID，0为用户）
    conversation_id: str = ""
    language: str = Language.ZH.value
    api_version: str = ChatApiVersion.V1.value  # API版本

class ModelEventItem(BaseModel):
    event_type: str
    from_model: str
    from_model_name: str
    to_model: str
    to_model_name: str
    from_chat_channel: str = ""
    to_chat_channel: str = ""

class ChatHistoryItem(BaseModel):
    content: str
    type: str
    timestamp: int
    version: int
    voice_url: str = ""
    message_id: str = ""

    # v2新增
    role_id: int = 0
    photo_url: str = ""
    photo_id: str = ""
    can_continue_replay: bool = False

    retry_photos: list[dict] = [] # {photo_id, photo_url}重试的图片列表

    @staticmethod
    def of_dict(d: dict):
        voice_url = "" if d.get("voice_url") is None else d["voice_url"]
        message_id = "" if d.get("message_id") is None else d["message_id"]
        return ChatHistoryItem(
            content=d["content"],
            type=d["type"],
            timestamp=d["timestamp"],
            voice_url=voice_url,
            message_id=message_id,
            version=d["version"],
        )
class BuildHistoryRet(BaseModel):
    history_list : list[ChatHistoryItem] = []
    latest_status_block:str = ""

class ModelSwitch(BaseModel):
    target_model: str = ""
    need_recharge_first: bool = False


class ChatHistoryResponse(BaseModel):
    conversation_id: str = ""
    user_id: int = 0

    role_id: int = 0
    role_name: str = ""
    card_name: str = ""
    role_avatar: str = ""
    introduction: str = ""
    scenario: str = ""
    chat_type: str = ""
    chat_list: list[ChatHistoryItem] = []
    private_card: bool = False
    support_photo: bool = False
    support_product_ids: list[str] = []
    switch_model: ModelSwitch | None = None

    # new
    group_name: str = ""
    roles: list[UserRoleBrief] = []
    deleted_roles: list[UserRoleBrief] = []

    model_event_map: dict[str, list[ModelEventItem]] = {}

    photos_likes_map: dict[str, int] = {} # {photo_id: like_status} 图片的点赞状态

    # audit
    audit_status: str = ""


class ChatConversation(BaseModel):
    title: str = ""
    conversation_id: str = ""
    first_chat_at: str = ""
    latest_chat_at: str = ""
    has_permission: bool = True
    desc: str = ""


class DelMessageRequest(BaseModel):
    role_id: int = 0
    conversation_id: str = ""
    message_ids: list[str] = []

class UpdateModelRequest(BaseModel):
    mode_type: ChatModeType
    mode_target_id: int
    conversation_id: str 
    model: str
    event_type: ModelEventType
    chat_channel: str = ""
    from_chat_channel: str = ""
    to_chat_channel: str = ""

class UserInputResponse(BaseModel):
    human_message_id: str = ""
    version:str = ""
