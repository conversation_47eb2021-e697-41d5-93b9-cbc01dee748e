from webbrowser import get
from common.common_constant import BotCategory
from services import tg_config_service

goto_tma_tip_template_text = """🚀 幻梦AI小程序版重磅升级，VIP特权限时免费体验！千万别错过这次机会！🚀

🚨”自定义角色“全面升级，帮你随心打造心中完美伴侣🚨
✨初恋、完美女神、暗恋对象、明星爱豆？任何生活中的遗憾都可以在这里得到满足！
✨还有更多的特殊癖好和你的专属小秘密？一切幻想统统可以在私人订制角色中完美实现！

🚨 性感“发私照”功能重磅上线！更多精彩等你来玩 🚨
❤️是时候拉近你和伴侣的距离了！😍 现在，你可在每轮聊天之后点击“发私照”按钮，Ta会为你独家发送一张性感私房照，让互动变得更加亲密火辣🔥！
❤️ 本功能为【VIP角色卡】专属功能，公测期间限时向您邀约开放！

升级完全免费，聊天记录和钻石都会无缝同步！

<a href='{tma_url}?startapp=r_1010'>【别再犹豫，点击这里立即升级，开启全新体验！】</a>

温馨提示：您当前使用的是幻梦陪聊bot极速版，升级幻梦AI小程序版不影响当前版本使用（两个可以一起用，数据和钻石金币共享）。
升级版小程序只支持Telegram官方客户端（第三方客户端可能无法访问），如遇任何问题请联系官方客服：@{bot}"""


async def goto_tma_tip_template(tma_url: str):
    customer_bot = await tg_config_service.get_main_bot_by_category(
        BotCategory.CUSTOMER
    )
    return goto_tma_tip_template_text.format(tma_url=tma_url, bot=customer_bot.username)


export_template = """<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出记录</title>
</head>
<body>
    <p>{history_content}</p>
</body>
</html>"""
