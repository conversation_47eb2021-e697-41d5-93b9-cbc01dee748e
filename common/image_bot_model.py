from typing import Optional
from pydantic import BaseModel
import json

from persistence.models.models_bot_image import BotImgBasicProfile



class GenImageBaseProfileBO(BaseModel):
    style: str 
    resolution: str = "low"
    privacy: Optional[str] = "public"
    
    @staticmethod
    def from_model(basic_profile: BotImgBasicProfile):
        
        profile_json = basic_profile.img_gen_profile
        
        return GenImageBaseProfileBO.model_validate_json(json.dumps(profile_json))
    

    def to_json(self):
        return self.model_dump_json(exclude=None)

    