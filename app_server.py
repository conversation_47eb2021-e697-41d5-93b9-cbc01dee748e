import argparse
from asyncio import sleep
import asyncio
import logging
import os, json
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi.concurrency import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from tortoise import Tortoise, run_async
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv
import sentry_sdk

from common.common_constant import ErrorKey
from utils.response_util import CommonResponse

load_dotenv()

from controllers import (
    chat_api,
    diamond_history_api,
    dislike_api,
    favorite_api,
    like_api,
    share_api,
    user_alt_persona_api,
    user_alt_profile_api,
    user_diamond_season_activitiy_api,
)
from controllers.stripe import stripe_router
from controllers.chat import chat_router
from controllers.user_login import login_router
from controllers.role_api import role_router
from controllers.recharge import recharge_router
from controllers.recharge_v2 import recharge_router_v2
from controllers.extensions import extension_router
from controllers.gift_api import gift_router
from controllers.alchemy_webhook import alchemy_router
from controllers.role_card_api import char_book_router
from controllers.history import history_router
from controllers.telegram_webhook import tg_router, on_tg_startup
from controllers.pcakge_voucher import pkg_voucher_router
from controllers.user_task import task_router
from controllers.cc_recharge import cc_recharge_router
from controllers.andada_recharge import andada_recharge_router
from controllers.bot_hooks.role_bot import role_bot_router
from controllers.global_config_api import global_config_router
from controllers.chat_photos import chat_photo_router
from controllers.p91pay_recharge import p91pay_recharge_router
from controllers.tmpay_recharge import tmpay_recharge_router
from controllers.tg_login import tg_login_router
from controllers.config import config_router
from controllers.ffpay_recharge import ffpay_recharge_router
from controllers.sdfkw_recharge import sdfkw_recharge_router
from controllers.qszf_recharge import qszf_recharge_router
from controllers.sjzf_recharge import sjzf_recharge_router
from controllers.jlbzf_recharge import jlbzf_recharge_router
from controllers.xjtzf_recharge import xjtzf_recharge_router
from controllers.user_role_config import user_role_config_router

import uvicorn

from services import (
    product_service,
    translate_service,
    user_active_service,
    user_service,
)
from utils import json_util
from utils.exception_util import ParamException, VerifyException
from utils.translate_util import _t, _tl

cors_origins = os.getenv("CORS_ORIGINS", "").split(",")
sentry_dsn = os.getenv("SENTRY_DSN")
if sentry_dsn:
    sentry_sdk.init(
        dsn=sentry_dsn,
        traces_sample_rate=0.05,
        profiles_sample_rate=1.0,
    )

if os.environ.get("ENV") == "production":
    app = FastAPI(redoc_url=None, docs_url=None, openapi_url=None)
else:
    app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Conversation-Id",
        "Message-Id",
        "Message-Version",
        "Human-Message-Id",
        "Current-Language",
    ],
)

app.include_router(login_router)
app.include_router(stripe_router)
app.include_router(chat_router)
app.include_router(chat_api.chat_api_router)
app.include_router(role_router)
app.include_router(recharge_router)
app.include_router(recharge_router_v2)
app.include_router(extension_router)
app.include_router(gift_router)
app.include_router(alchemy_router)
app.include_router(char_book_router)
app.include_router(history_router)
app.include_router(tg_router)
app.include_router(pkg_voucher_router)
app.include_router(task_router)
app.include_router(cc_recharge_router)
app.include_router(andada_recharge_router)
app.include_router(role_bot_router)
app.include_router(global_config_router)
app.include_router(chat_photo_router)
app.include_router(p91pay_recharge_router)
app.include_router(tmpay_recharge_router)
app.include_router(favorite_api.favorite_api_router)
app.include_router(tg_login_router)
app.include_router(config_router)
app.include_router(like_api.like_api_router)
app.include_router(dislike_api.dislike_api_router)
app.include_router(diamond_history_api.diamond_hisotry_router)
app.include_router(share_api.share_api_router)
app.include_router(user_diamond_season_activitiy_api.diamond_season_activity_router)
app.include_router(user_alt_profile_api.user_alt_profile_api_router)
app.include_router(user_alt_persona_api.user_alt_persona_api_router)
app.include_router(ffpay_recharge_router)
app.include_router(sdfkw_recharge_router)
app.include_router(qszf_recharge_router)
app.include_router(sjzf_recharge_router)
app.include_router(jlbzf_recharge_router)
app.include_router(xjtzf_recharge_router)
app.include_router(user_role_config_router)

log_level = os.getenv("LOGGER_LEVEL","INFO").upper()
logging.basicConfig(
    level=logging.DEBUG if log_level == "DEBUG" else logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] [%(thread)d] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

log.info("Starting server...")


Tortoise.init_models(["persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models"]},
    generate_schemas=True,
)
scheduler = AsyncIOScheduler()


# 拦截所有请求，进行权限验证
@app.middleware("http")
async def record_user_active(request: Request, call_next):
    response = await call_next(request)
    tg_init_data = request.headers.get("tg-init-data", "")
    tg_bot_id = request.headers.get("tg-bot-id", "")
    white_list = ["/user/me", "/chat/replay"]
    try:
        path = request.url.path.split("?")[0]
        if path not in white_list:
            return response
        await user_active_service.refresh_by_api(tg_init_data, tg_bot_id)
    except Exception as e:
        log.error(f"Error: {e}")
    return response


scheduler = AsyncIOScheduler()


@app.on_event("startup")
async def startup_event():
    await translate_service.refresh_all_cache()
    scheduler.add_job(translate_service.refresh_cache, "interval", seconds=60 * 5)
    scheduler.start()


@app.exception_handler(HTTPException)
async def global_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(content=exc.detail, status_code=exc.status_code)


@app.exception_handler(ParamException)
async def param_exception_handler(request: Request, exc: ParamException):
    log.error(f"ParamException: {exc.detail}")
    return JSONResponse(content=exc.detail, status_code=exc.status_code)


@app.exception_handler(VerifyException)
async def verify_exception_handler(request: Request, exc: VerifyException):
    log.error(f"VerifyException: {exc.detail}")
    current_language = request.headers.get("Current-Language", "zh")
    detail = exc.detail
    if not detail:
        return JSONResponse(
            content={"error_code": -1, "message": "verify error"},
            status_code=exc.status_code,
        )
    common_response = CommonResponse(**json.loads(detail))
    if common_response.error_key:
        error_key = ErrorKey(common_response.error_key)
        common_response.message = _tl(error_key.message(), current_language)
    elif common_response.message:
        common_response.message = _tl(common_response.message, current_language)
    return JSONResponse(
        content=common_response.model_dump(),
        status_code=exc.status_code,
    )


# Api Url：http://127.0.0.1:8800/docs
# 解析命令行参数
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8800)
