{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 63, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "title": "全体用户汇总数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 0, "y": 1}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\n count(1)\nFROM\n  tavern.users_pw\n\nwhere id >= 1000000", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "注册总用户", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 2, "y": 1}, "id": 10, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(1)\n\nFROM tavern.invitation", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "邀请获客总数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 5, "y": 1}, "id": 49, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "-- SELECT sum(pay_amount_sum)/100000\n\n-- FROM tavern.user_summary_stats\n\nSELECT SUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000\n\n  FROM  tavern.recharge_order WHERE pay_fee > 0 and status='SUCCEED'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "充值总收入(CNY)", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 75, "panels": [], "title": "活跃用户-汇总数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日完成支付充值金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 5}, "id": 76, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \n  SUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  ) / 100000\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' and  (pay_currency = 'CNY' OR pay_currency = 'USD')", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日充值金额汇总(CNY)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "非当日注册，当日完成支付充值金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 3, "y": 5}, "id": 59, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(pay_fee)/100000\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' and uss.register_at_day <> $search_date and pay_currency='CNY'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "非当日注册用户-当日充值金额(CNY)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日所有首次充值用户的首充金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 7, "y": 5}, "id": 77, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "\nSELECT SUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000 FROM tavern.recharge_order ro\n\nleft join ( select user_id,min(finished_at) as min_fat from tavern.recharge_order \n  where pay_fee > 0 and status='SUCCEED' \n  GROUP BY user_id\n ) min_ro on min_ro.user_id = ro.user_id\n\n where ro.pay_fee > 0 and ro.status='SUCCEED'  and ro.finished_at = min_ro.min_fat and DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d')=$search_date \n\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "全部用户当日首充金额", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日所有首次充值用户的首充金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 10, "y": 5}, "id": 78, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "\nSELECT SUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000 FROM tavern.recharge_order ro\n\nleft join ( select user_id,min(finished_at) as min_fat from tavern.recharge_order \n  where pay_fee > 0 and status='SUCCEED' \n  GROUP BY user_id\n ) min_ro on min_ro.user_id = ro.user_id\n\n where ro.pay_fee > 0 and ro.status='SUCCEED' and ro.finished_at <> min_ro.min_fat and DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d')=$search_date \n\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "全部用户当日复购金额", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 13, "y": 5}, "id": 47, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(1)\nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date and joined_group_count >0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "加群人数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 16, "y": 5}, "id": 73, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(total_fee)\nFROM tavern.pay_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and product_id='1e5c8e11-2891-4d11-bc35-6397b5fedf8d'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "图像调用钻石", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 19, "y": 5}, "id": 72, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(*)\nFROM tavern.pay_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and product_id='1e5c8e11-2891-4d11-bc35-6397b5fedf8d'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "图像调用次数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 8}, "id": 43, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(pay_count)\nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date and pay_count>0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册的充值订单数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 8}, "id": 54, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(1)\nFROM tavern.recharge_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and pay_fee > 0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "订单数-发起订单数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 8}, "id": 55, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(distinct(user_id))\nFROM tavern.recharge_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and pay_fee > 0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "订单人数-发起订单独立人", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "订单充值完成率（所有成功充值订单总数/所有在各个渠道发起充值订单总数）可能存在成功订单不是在当日完成", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 8}, "id": 56, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(CASE WHEN status='SUCCEED' THEN 1 ELSE 0 END)/count(1)*100\nFROM tavern.recharge_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and pay_fee > 0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "订单充值完成率%", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "订单充值完成率（所有成功充值订单总数/所有在各个渠道发起充值订单总数）可能存在成功订单不是在当日完成", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 8}, "id": 88, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(CASE WHEN status='SUCCEED' THEN 1 ELSE 0 END)/count(1)*100\nFROM tavern.recharge_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and pay_fee > 0 and recharge_channel<>'PACKAGE_VOUCHER' and recharge_channel<>'USDT'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "不含兑换码-订单充值完成率%", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日注册并且当日完成支付金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 8}, "id": 53, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nSUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000\nFROM tavern.recharge_order ro\nleft join user_summary_stats uss on uss.user_id = ro.user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' AND uss.register_at_day = $search_date", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册用户当日充值(CNY)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日注册之后完成的充值", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 8}, "id": 74, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nSUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000\nFROM tavern.recharge_order ro\nleft join user_summary_stats uss on uss.user_id = ro.user_id\n\nWHERE ro.pay_fee>0 and ro.status='SUCCEED' AND uss.register_at_day = $search_date", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册用户-累计充值金额(CNY)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 8}, "id": 44, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(1)\nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date and pay_count>0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "充值人数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 11}, "id": 91, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(amount)/1000 from recharge_order where status='SUCCEED' and pay_fee>0\nAND  DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date ", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日充值钻石(单位：k)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 11}, "id": 90, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(amount)/1000 from recharge_order \n\nwhere status='SUCCEED' and pay_fee=0 \nAND  DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date ", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日赠送钻石(单位：k)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 11}, "id": 92, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(total_fee)/1000 from pay_order where status='SUCCESS'\nAND  DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date ", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日消耗钻石总量(单位：k)", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 9, "y": 11}, "id": 41, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(cov_count)/count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date and turn_count>0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "平均聊天回合数（只包括聊天人群）", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 13, "y": 11}, "id": 40, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(turn_count)/count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date and turn_count>0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "平均聊天轮数（只包括聊天人群）", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 17, "y": 11}, "id": 42, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(role_count)/count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date and turn_count>0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "平均聊天角色数（只包括聊天人群）", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 14}, "id": 36, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date ", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册用户数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 14}, "id": 38, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date  and turn_count > 0", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "有聊天用户数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 14}, "id": 39, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \nsum(case when turn_count>0 then 1 else 0 end )/count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date ", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "首聊率", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 96, "panels": [], "title": "活跃用户-汇总数据（饼图）", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日完成支付充值金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["充值金额CNY"], "prefix": "All except:", "readOnly": true}}, "properties": []}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "id": 81, "options": {"legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 50, "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT \nCAST(uss.from_user_id AS CHAR) as category,\nSUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000 as '充值金额CNY'\n\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' \ngroup by from_user_id ORDER BY  sum(pay_fee) desc\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日充值金额汇总(CNY)（展示最高50个渠道）", "type": "piechart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日完成支付充值金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 18}, "id": 85, "options": {"legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 50, "values": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT \nCAST(uss.from_user_id AS CHAR) as category,\ncount(1) as '注册人数'\n\nFROM tavern.users_pw up\nleft join user_summary_stats uss on uss.user_id=up.id\n\n\nWHERE DATE_FORMAT(CONVERT_TZ(up.created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date\ngroup by from_user_id ORDER BY  sum(1) desc\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册人数汇总（展示最高50个渠道）", "type": "piechart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "当日完成支付充值金额汇总", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["充值金额CNY"], "prefix": "All except:", "readOnly": true}}, "properties": []}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 27}, "id": 89, "options": {"legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 50, "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT \nCAST(uss.from_user_id AS CHAR) as category,\nSUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  )/100000 as '充值金额CNY'\n\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' \n\nAND DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = uss.register_at_day\n\ngroup by from_user_id ORDER BY  sum(pay_fee/100000) desc\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册并且充值金额汇总(CNY)（展示最高50个渠道）", "type": "piechart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["充值金额CNY"], "prefix": "All except:", "readOnly": true}}, "properties": []}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 27}, "id": 93, "options": {"legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 50, "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT \nrp.title as category,\nSUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n) / 100000 AS \"充值金额汇总（CNY）\"\nFROM tavern.recharge_order ro \njoin recharge_product rp on ro.recharge_product_id=rp.recharge_product_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' \n\ngroup by rp.recharge_product_id\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日充值套餐汇总(CNY)", "type": "piechart"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}, "id": 37, "panels": [], "title": "新用户【当日注册】-汇总数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["充值金额CNY"], "prefix": "All except:", "readOnly": true}}, "properties": []}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 37}, "id": 94, "options": {"legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 50, "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT rp.title as '产品名称',count(1) as '订单数' from recharge_order ro\nLEFT JOIN recharge_product rp on rp.recharge_product_id = ro.recharge_product_id \nWHERE\nro.user_id in (SELECT id as user_id from users_pw \nwhere DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = '$search_date') and ro.status='SUCCEED' and ro.pay_fee > 0\nand DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = '$search_date'\nGROUP BY rp.title", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册当日充值订单数量", "type": "piechart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["充值金额CNY"], "prefix": "All except:", "readOnly": true}}, "properties": []}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 37}, "id": 95, "options": {"legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 50, "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT rp.title as '产品名称', \nSUM(\n    CASE\n      WHEN pay_currency = 'CNY' THEN pay_fee\n      WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n      ELSE 0\n    END\n  ) / 100000 AS \"充值金额汇总（CNY）\"\nfrom recharge_order ro\nLEFT JOIN recharge_product rp on rp.recharge_product_id = ro.recharge_product_id \nWHERE\nro.user_id in (SELECT id as user_id from users_pw \nwhere DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = '$search_date') and ro.status='SUCCEED' and ro.pay_fee > 0\nand DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = '$search_date'\nGROUP BY rp.title", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "当日注册当日充值金额-CNY", "type": "piechart"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 46}, "id": 50, "panels": [], "title": "活跃用户-汇总数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 47}, "id": 51, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(distinct(user_id))\nFROM tavern.recharge_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and pay_fee>0 and status='SUCCEED'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "充值人数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 47}, "id": 52, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(1)\nFROM tavern.recharge_order \n\nWHERE DATE_FORMAT(CONVERT_TZ(finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and pay_fee>0 and status='SUCCEED'", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "充值订单数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 47}, "id": 58, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(1)\nFROM tavern.chat_join_task \n\nWHERE chat_id = -1002223050046 and DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "加群人数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 47}, "id": 57, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \ncount(1)\nFROM tavern.chat_join_task \n\nWHERE chat_id = -1002201418897 and DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "加渠道人数", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 61, "panels": [], "title": "今日钻石数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 51}, "id": 62, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(amount) from recharge_order where status='SUCCEED' and pay_fee>0 and DATE(CONVERT_TZ(finished_at, '+00:00', '+08:00'))=DATE(UTC_TIMESTAMP() + INTERVAL 8 HOUR);", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "用户充值钻石数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 51}, "id": 63, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(amount) from recharge_order where status='SUCCEED' and pay_fee=0 and DATE(CONVERT_TZ(finished_at, '+00:00', '+08:00'))=DATE(UTC_TIMESTAMP() + INTERVAL 8 HOUR);", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "平台赠送钻石数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 51}, "id": 64, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(total_fee) from pay_order where status='SUCCESS' and DATE(CONVERT_TZ(finished_at, '+00:00', '+08:00'))=DATE(UTC_TIMESTAMP() + INTERVAL 8 HOUR);", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "用户消耗钻石数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 51}, "id": 65, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(po.total_fee) from pay_order po left join user_summary_stats us on po.user_id=us.user_id where po.status='SUCCESS' and us.pay_count>0 and DATE(CONVERT_TZ(finished_at, '+00:00', '+08:00'))=DATE(UTC_TIMESTAMP() + INTERVAL 8 HOUR);", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "付费用户消耗钻石数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 51}, "id": 66, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(po.total_fee) from pay_order po left join user_summary_stats us on po.user_id=us.user_id where po.status='SUCCESS' and us.pay_count=0 and DATE(CONVERT_TZ(finished_at, '+00:00', '+08:00'))=DATE(UTC_TIMESTAMP() + INTERVAL 8 HOUR);", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "非付费用户消耗钻石数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 51}, "id": 67, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "select sum(balance) from expirable_award where expires_at<now() and balance>0;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "用户过期钻石数", "type": "stat"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 54}, "id": 79, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "\nSELECT DATE(CONVERT_TZ(cjt.created_at, '+00:00', '+08:00')) as days,\ncount(distinct(cjt.user_id)) as '入群用户数（新老独立用户）'\nFROM tavern.chat_join_task cjt\nleft join bot_config bc on cjt.chat_id = bc.bot_id\nwhere bc.type = 'GROUP' and $__timeFilter(cjt.created_at) \ngroup by DATE(CONVERT_TZ(cjt.created_at, '+00:00', '+08:00')) ORDER BY days asc", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "\nSELECT DATE(CONVERT_TZ(ct.created_at, '+00:00', '+08:00')) as days,\ncount(distinct(ct.user_id)) as '每日群签到用户数（独立用户）'\nFROM tavern.check_in_task ct \n\nWHERE $__timeFilter(ct.created_at) \ngroup by DATE(CONVERT_TZ(ct.created_at, '+00:00', '+08:00')) ORDER BY days asc\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT DATE(CONVERT_TZ(cjt.created_at, '+00:00', '+08:00')) as days,\ncount(distinct(cjt.user_id)) as '订阅频道用户数（独立用户）'\nFROM tavern.chat_join_task cjt \nleft join bot_config bc on cjt.chat_id = bc.bot_id\nwhere bc.type = 'CHANNEL' and $__timeFilter(cjt.created_at) \ngroup by DATE(CONVERT_TZ(cjt.created_at, '+00:00', '+08:00')) ORDER BY days asc\n", "refId": "C", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "群管理数据", "type": "barchart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 63}, "id": 86, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xField": "chs_cat", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "-- 生成 00:00 到 23:00 的小时列表\nWITH hours AS (\n    SELECT '00:00' AS hour\n    UNION ALL SELECT '01:00'\n    UNION ALL SELECT '02:00'\n    UNION ALL SELECT '03:00'\n    UNION ALL SELECT '04:00'\n    UNION ALL SELECT '05:00'\n    UNION ALL SELECT '06:00'\n    UNION ALL SELECT '07:00'\n    UNION ALL SELECT '08:00'\n    UNION ALL SELECT '09:00'\n    UNION ALL SELECT '10:00'\n    UNION ALL SELECT '11:00'\n    UNION ALL SELECT '12:00'\n    UNION ALL SELECT '13:00'\n    UNION ALL SELECT '14:00'\n    UNION ALL SELECT '15:00'\n    UNION ALL SELECT '16:00'\n    UNION ALL SELECT '17:00'\n    UNION ALL SELECT '18:00'\n    UNION ALL SELECT '19:00'\n    UNION ALL SELECT '20:00'\n    UNION ALL SELECT '21:00'\n    UNION ALL SELECT '22:00'\n    UNION ALL SELECT '23:00'\n)\n\n-- 实际查询\nSELECT \n    CAST(h.hour as CHAR) AS chs_cat,\n    IFNULL(u.reg_count, 0) AS '注册用户数'\nFROM hours h\nLEFT JOIN (\n    -- 实际注册用户数据按小时分组\n    SELECT \n        DATE_FORMAT(CONVERT_TZ(min(created_at), '+00:00', '+08:00'), '%H:00') AS hour,\n        count(1) AS reg_count\n    FROM tavern.users_pw \n    WHERE DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'), '%Y%m%d') = $search_date  \n    GROUP BY DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'), '%Y-%m-%d %H')\n) u ON h.hour = u.hour\nORDER BY h.hour;\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "每个小时注册用户数", "type": "barchart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 72}, "id": 87, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xField": "chs_cat", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "-- 生成 00:00 到 23:00 的小时列表\nWITH hours AS (\n    SELECT '00:00' AS hour\n    UNION ALL SELECT '01:00'\n    UNION ALL SELECT '02:00'\n    UNION ALL SELECT '03:00'\n    UNION ALL SELECT '04:00'\n    UNION ALL SELECT '05:00'\n    UNION ALL SELECT '06:00'\n    UNION ALL SELECT '07:00'\n    UNION ALL SELECT '08:00'\n    UNION ALL SELECT '09:00'\n    UNION ALL SELECT '10:00'\n    UNION ALL SELECT '11:00'\n    UNION ALL SELECT '12:00'\n    UNION ALL SELECT '13:00'\n    UNION ALL SELECT '14:00'\n    UNION ALL SELECT '15:00'\n    UNION ALL SELECT '16:00'\n    UNION ALL SELECT '17:00'\n    UNION ALL SELECT '18:00'\n    UNION ALL SELECT '19:00'\n    UNION ALL SELECT '20:00'\n    UNION ALL SELECT '21:00'\n    UNION ALL SELECT '22:00'\n    UNION ALL SELECT '23:00'\n)\n\n-- 实际查询\nSELECT \n    h.hour AS chs_cat,  -- 将小时字符串转换为 TIME 类型\n    IFNULL(u.reg_count, 0) AS '注册用户数'\nFROM hours h\nLEFT JOIN (\n    -- 实际注册用户数据按小时分组\n    SELECT \n        DATE_FORMAT(CONVERT_TZ(MIN(created_at), '+00:00', '+08:00'), '%H:00') AS hour,\n        COUNT(1) AS reg_count\n    FROM tavern.users_pw \n    WHERE DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'), '%Y%m%d') = \n          DATE_FORMAT(DATE_SUB(STR_TO_DATE('$search_date', '%Y%m%d'), INTERVAL 1 DAY), '%Y%m%d')  \n    GROUP BY DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'), '%Y-%m-%d %H')\n) u ON h.hour = u.hour\nORDER BY h.hour;\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "每个小时注册用户数(T-1)", "type": "barchart"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 81}, "id": 15, "panels": [], "title": "拉新获客", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 82}, "id": 29, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "邀请数量"}]}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT inv.inviter_user_id as '邀请人',\nup.nickname,\nup.email,\ncount(inv.id) as '邀请数量',\nsum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END) as '聊天用户数',\nsum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END)*100/count(inv.id) as '首聊率%',\nsum(chat_count)/sum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END) as '邀请用户平均聊天轮次',\ncount(po.user_id) as '充值人数',\nsum(po.sumamount)/100000 as '充值总金额(CNY)',\nsum(po.charge_times) as '充值总次数'\n\nfrom tavern.invitation inv\n\n\nleft join users_pw up on up.id = inv.inviter_user_id\n\nleft join (select user_id,count(1) as chat_count from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= inv.invitee_user_id\n\nleft join ( SELECT  \nuser_id,\nsum(pay_fee) as sumamount,\nsum(CASE WHEN pay_currency = 'CNY' THEN pay_fee ELSE 0 END) as total_amount,\ncount(*) as charge_times FROM tavern.recharge_order where pay_fee > 0 and status = 'SUCCEED' GROUP BY user_id )  po on inv.invitee_user_id = po.user_id \n\nGROUP BY inviter_user_id", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT cc.user_cat,count(1) as '首日未聊' FROM  (\n  SELECT up.id,DATE_FORMAT(up.created_at, '%Y%m%d') AS user_cat ,chs.chs_cat FROM tavern.users_pw up\n  left join (select user_id,DATE_FORMAT(min(created_at), '%Y%m%d') AS chs_cat from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= up.id\n\n) cc\nwhere cc.chs_cat IS NULL\n\ngroup by cc.user_cat\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "邀请排行榜", "type": "table"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 91}, "id": 35, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "邀请数量"}]}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT inv.inviter_user_id as '邀请人',\nup.nickname,\nup.email,\ncount(inv.id) as '邀请数量',\nsum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END) as '聊天用户数',\nsum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END)*100/count(inv.id) as '首聊率%',\nsum(chat_count)/sum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END) as '邀请用户平均聊天轮次',\nsum(role_count)/sum(CASE WHEN chs.user_id is not null THEN 1 ELSE 0 END) as '邀请用户平均聊天角色数',\ncount(po.user_id) as '充值人数',\nsum(po.sumamount)/100000 as '充值总金额(CNY)',\nsum(po.charge_times) as '充值总次数'\n\n\nfrom tavern.invitation inv\n\n\nleft join users_pw up on up.id = inv.inviter_user_id\n\nleft join (select user_id,count(1) as chat_count,count(distinct(role_id)) as role_count from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= inv.invitee_user_id\n\nleft join ( SELECT  \nuser_id,\nsum(pay_fee) as sumamount,\nsum(CASE WHEN pay_currency = 'CNY' THEN pay_fee ELSE 0 END) as total_amount,\ncount(*) as charge_times\n\nFROM tavern.recharge_order where pay_fee > 0 and status = 'SUCCEED' GROUP BY user_id )  po on inv.invitee_user_id = po.user_id \n\nWHERE DATE_FORMAT(CONVERT_TZ(inv.created_at, '+00:00', '+08:00'), '%Y%m%d') = '$search_date' \nGROUP BY inviter_user_id", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT cc.user_cat,count(1) as '首日未聊' FROM  (\n  SELECT up.id,DATE_FORMAT(up.created_at, '%Y%m%d') AS user_cat ,chs.chs_cat FROM tavern.users_pw up\n  left join (select user_id,DATE_FORMAT(min(created_at), '%Y%m%d') AS chs_cat from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= up.id\n\n) cc\nwhere cc.chs_cat IS NULL\n\ngroup by cc.user_cat\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "【日期选择】每日排行榜（UTC+8）", "type": "table"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 100}, "id": 48, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "邀请数量"}]}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT inv.inviter_user_id as '邀请人',\nust_from.nickname,\nust_from.email,\nust_from.tg_id,\ncount(inv.id) as '邀请数量',\ncount(inv.id)/(SELECT count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date )*100 as '当日注册占比%',\nsum(CASE WHEN ust.cov_count >0 THEN 1 ELSE 0 END) as '聊天用户数',\nsum(CASE WHEN ust.cov_count >0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '首聊率%',\nsum(ust.turn_count)/sum(CASE WHEN ust.cov_count >0 THEN 1 ELSE 0 END) as '平均聊天轮次（有聊天用户）',\nsum(ust.role_count)/sum(CASE WHEN ust.role_count >0 THEN 1 ELSE 0 END) as '聊天平局角色（有聊天用户）',\nsum(CASE WHEN ust.pay_count >0 THEN 1 ELSE 0 END) as '充值人数',\nsum(CASE WHEN ust.pay_count >0 THEN 1 ELSE 0 END)/(SELECT count(1) \nFROM tavern.user_summary_stats \nWHERE register_at_day = $search_date ) *100 as '充值转化',\nsum(ust.pay_amount_sum)/100000 as '充值总金额(CNY)',\nsum(ust.pay_count) as '充值总次数'\n\n\n\nfrom tavern.invitation inv\n\nleft join user_summary_stats ust on ust.user_id=inv.invitee_user_id\nleft join user_summary_stats ust_from on ust_from.user_id=inv.inviter_user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(inv.created_at, '+00:00', '+08:00'), '%Y%m%d') = '$search_date' \nGROUP BY inviter_user_id", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT cc.user_cat,count(1) as '首日未聊' FROM  (\n  SELECT up.id,DATE_FORMAT(up.created_at, '%Y%m%d') AS user_cat ,chs.chs_cat FROM tavern.users_pw up\n  left join (select user_id,DATE_FORMAT(min(created_at), '%Y%m%d') AS chs_cat from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= up.id\n\n) cc\nwhere cc.chs_cat IS NULL\n\ngroup by cc.user_cat\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "【日期选择】每日排行榜（UTC+8）-付费（新版本）", "type": "table"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 109}, "id": 60, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "邀请数量"}]}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT inv.inviter_user_id as '邀请人',\nust_from.nickname,\nust_from.email,\nust_from.tg_id,\ncount(inv.id) as '邀请数量',\ncount(inv.id)/(SELECT count(1) \nFROM tavern.user_summary_stats \n\nWHERE register_at_day = $search_date )*100 as '当日注册占比%',\nsum(CASE WHEN ust.chat_days&1>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '首日聊天%',\nsum(CASE WHEN ust.chat_days&2>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '1日留存%',\nsum(CASE WHEN ust.chat_days&4>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '2日留存%',\nsum(CASE WHEN ust.chat_days&8>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '3日留存%',\nsum(CASE WHEN ust.chat_days&16>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '4日留存%',\nsum(CASE WHEN ust.chat_days&32>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '5日留存%',\nsum(CASE WHEN ust.chat_days&64>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '6日留存%',\nsum(CASE WHEN ust.chat_days&128>0 THEN 1 ELSE 0 END)/count(inv.id)*100 as '7日留存%'\n\n\n\nfrom tavern.invitation inv\n\nleft join user_summary_stats ust on ust.user_id=inv.invitee_user_id\nleft join user_summary_stats ust_from on ust_from.user_id=inv.inviter_user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(inv.created_at, '+00:00', '+08:00'), '%Y%m%d') = '$search_date' \nGROUP BY inviter_user_id", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT cc.user_cat,count(1) as '首日未聊' FROM  (\n  SELECT up.id,DATE_FORMAT(up.created_at, '%Y%m%d') AS user_cat ,chs.chs_cat FROM tavern.users_pw up\n  left join (select user_id,DATE_FORMAT(min(created_at), '%Y%m%d') AS chs_cat from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= up.id\n\n) cc\nwhere cc.chs_cat IS NULL\n\ngroup by cc.user_cat\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "【日期选择】每日排行榜（UTC+8）-留存（新版本）", "type": "table"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 118}, "id": 25, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "邀请数量"}]}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT \ninv.id as 'id',\ninv.created_at as '时间',\ninv.invitee_user_id as '受邀者ID',\ninv.inviter_user_id as '邀请者ID',\nup.nickname as '昵称',\nup.email as 'Email（包括TG）',\npo.sumamount/100000 as '累计支付',\npo.order_count as '支付订单数量'\nFROM tavern.invitation inv\n\nleft join users_pw up on up.id = inv.invitee_user_id\nleft join ( SELECT  \nuser_id,\nsum(pay_fee) as sumamount,\ncount(1) as order_count\n\nFROM tavern.recharge_order where pay_fee > 0 GROUP BY user_id )  po on po.user_id = inv.invitee_user_id\n\n ORDER BY inv.created_at desc", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT cc.user_cat,count(1) as '首日未聊' FROM  (\n  SELECT up.id,DATE_FORMAT(up.created_at, '%Y%m%d') AS user_cat ,chs.chs_cat FROM tavern.users_pw up\n  left join (select user_id,DATE_FORMAT(min(created_at), '%Y%m%d') AS chs_cat from tavern.chat_history_statistic group by user_id)  chs on chs.user_id= up.id\n\n) cc\nwhere cc.chs_cat IS NULL\n\ngroup by cc.user_cat\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "邀请明细榜", "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 129}, "id": 68, "panels": [], "title": "聊天数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"log": 2, "type": "log"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 130}, "id": 69, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT STR_TO_DATE(uss.register_at_day, '%Y%m%d') as '注册日期',count(uss.user_id) as '数量' from (\nSELECT distinct(user_id) as user_id\nFROM chat_history_statistic chs \nwhere DATE_FORMAT(CONVERT_TZ(chs.created_at, '+00:00', '+08:00'), '%Y%m%d') = '$search_date' \n)  mid \n\nleft join user_summary_stats uss on uss.user_id = mid.user_id\n\ngroup by uss.register_at_day ORDER BY uss.register_at_day desc", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "每日活跃用户（注册时间分布）", "type": "barchart"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 139}, "id": 33, "panels": [], "title": "签到数据", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 140}, "id": 70, "panels": [], "title": "付费数据", "type": "row"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 0, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 141}, "id": 32, "options": {"barRadius": 0, "barWidth": 0.97, "colorByField": "hours", "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "WITH hourly_data AS (\n    SELECT \n        DATE_FORMAT(CONVERT_TZ(ro.finished_at, '+00:00', '+08:00'), '%H:00') as hours,\n        SUM(\n            CASE\n            WHEN pay_currency = 'CNY' THEN pay_fee\n            WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n            ELSE 0\n            END\n        ) / 100000 AS \"金额CNY\"\n    FROM tavern.recharge_order ro \n    LEFT JOIN user_summary_stats uss ON uss.user_id = ro.user_id\n    WHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date \n        AND ro.pay_fee > 0 \n        AND ro.status = 'SUCCEED' \n    GROUP BY hours\n)\nSELECT all_hours.hours, COALESCE(hourly_data.金额CNY, 0) AS 金额CNY\nFROM (\n    SELECT DATE_FORMAT(SEC_TO_TIME(3600 * hour), '%H:00') AS hours\n    FROM (SELECT 0 AS hour UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23) AS hours_table\n) AS all_hours\nLEFT JOIN hourly_data ON all_hours.hours = hourly_data.hours\nORDER BY all_hours.hours ASC;", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT \nDATE_FORMAT(CONVERT_TZ(ro.finished_at, '+00:00', '+08:00'), '%H:00') as hours,sum(pay_fee)/100000 as '金额CNY（昨日）'\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\nWHERE DATE_FORMAT(DATE_SUB(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), INTERVAL 1 DAY), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' and pay_currency='CNY'\n\nGROUP BY hours order by hours asc\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "小时纬度（当日）", "type": "barchart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 150}, "id": 80, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "WITH hourly_data AS (\n    SELECT \n        DATE_FORMAT(CONVERT_TZ(ro.finished_at, '+00:00', '+08:00'), '%H:00') as hours,\n        SUM(\n            CASE\n            WHEN pay_currency = 'CNY' THEN pay_fee\n            WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n            ELSE 0\n            END\n        ) / 100000 AS \"金额CNY_昨日\"\n    FROM tavern.recharge_order ro \n    LEFT JOIN user_summary_stats uss ON uss.user_id = ro.user_id\n    WHERE DATE_FORMAT(DATE_SUB(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), INTERVAL -1 DAY), '%Y%m%d') = $search_date \n        AND ro.pay_fee > 0 \n        AND ro.status = 'SUCCEED' \n    GROUP BY hours\n)\nSELECT all_hours.hours, COALESCE(hourly_data.金额CNY_昨日, 0) AS 金额CNY_昨日\nFROM (\n    SELECT DATE_FORMAT(SEC_TO_TIME(3600 * hour), '%H:00') AS hours\n    FROM (SELECT 0 AS hour UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23) AS hours_table\n) AS all_hours\nLEFT JOIN hourly_data ON all_hours.hours = hourly_data.hours\nORDER BY all_hours.hours ASC;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT \nDATE_FORMAT(CONVERT_TZ(ro.finished_at, '+00:00', '+08:00'), '%H:00') as hours,sum(pay_fee)/100000 as '金额CNY'\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' and pay_currency='CNY'\n\nGROUP BY hours order by hours asc\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "小时纬度（T-1）", "type": "barchart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 159}, "id": 82, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "WITH hourly_data AS (\n    SELECT \n        DATE_FORMAT(CONVERT_TZ(ro.finished_at, '+00:00', '+08:00'), '%H:00') as hours,\n        SUM(\n            CASE\n            WHEN pay_currency = 'CNY' THEN pay_fee\n            WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n            ELSE 0\n            END\n        ) / 100000 AS \"金额CNY_7天前\"\n    FROM tavern.recharge_order ro \n    LEFT JOIN user_summary_stats uss ON uss.user_id = ro.user_id\n    WHERE DATE_FORMAT(DATE_SUB(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), INTERVAL -7 DAY), '%Y%m%d') = $search_date \n        AND ro.pay_fee > 0 \n        AND ro.status = 'SUCCEED' \n        AND pay_currency = 'CNY'\n    GROUP BY hours\n)\nSELECT all_hours.hours, COALESCE(hourly_data.金额CNY_7天前, 0) AS 金额CNY_7天前\nFROM (\n    SELECT DATE_FORMAT(SEC_TO_TIME(3600 * hour), '%H:00') AS hours\n    FROM (SELECT 0 AS hour UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23) AS hours_table\n) AS all_hours\nLEFT JOIN hourly_data ON all_hours.hours = hourly_data.hours\nORDER BY all_hours.hours ASC;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "hide": true, "rawQuery": true, "rawSql": "SELECT \nDATE_FORMAT(CONVERT_TZ(ro.finished_at, '+00:00', '+08:00'), '%H:00') as hours,sum(pay_fee)/100000 as '金额CNY'\nFROM tavern.recharge_order ro \nleft join user_summary_stats uss on uss.user_id=ro.user_id\n\nWHERE DATE_FORMAT(CONVERT_TZ(ro.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date and ro.pay_fee>0 and ro.status='SUCCEED' and pay_currency='CNY'\n\nGROUP BY hours order by hours asc\n", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "小时纬度（T-7）", "type": "barchart"}, {"datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "ura"}, "properties": [{"id": "custom.width", "value": 193}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "AI回复次数(Sonnet3.5)"}, "properties": [{"id": "custom.width", "value": 174}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "AI回复次数(Sonnet3)"}, "properties": [{"id": "custom.width", "value": 170}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 169}, "id": 71, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "注册时间"}]}, "pluginVersion": "12.1.0-89438", "targets": [{"dataset": "tavern", "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "-- SET time_zone = '+00:00';\nSELECT \npo.user_id,\nuss.nickname,\nCONVERT_TZ(FROM_UNIXTIME(uss.register_at),'+08:00', '+00:00') as '注册时间',\n-- uss.register_at_day,\nmin(po.finished_at) as '首笔支付完成时间',\n        SUM(\n            CASE\n            WHEN pay_currency = 'CNY' THEN pay_fee\n            WHEN pay_currency = 'USD' THEN pay_fee * 7.5\n            ELSE 0\n            END\n        ) / 100000 AS '支付总金额',\nuss.from_user_id as '邀请ID',\ncount(1) as '支付次数',\nchs.ai_count as 'AI回复总次数',\nchs.ai_haiku_count as 'AI回复次数(Haiku)',\nchs.ai_sonnet3_count as 'AI回复次数(Sonnet3)',\nchs.ai_sonnet35_count as 'AI回复次数(Sonnet3.5)',\nchs.ai_opus_count as 'AI回复次数(opus)'\nFROM tavern.recharge_order po  \n\nleft join user_summary_stats uss on uss.user_id = po.user_id\nleft join (\n  select user_id,\n  count(1) as ai_count,\n  SUM(CASE WHEN model = 'claude-3-haiku' THEN 1 ELSE 0 END) as ai_haiku_count,\n  SUM(CASE WHEN model = 'claude-3-sonnet' THEN 1 ELSE 0 END) as ai_sonnet3_count,\n  SUM(CASE WHEN model = 'claude-3.5-sonnet' THEN 1 ELSE 0 END) as ai_sonnet35_count,\n  SUM(CASE WHEN model = 'claude-3-opus' THEN 1 ELSE 0 END) as ai_opus_count\n\n  FROM tavern.chat_history_statistic\n\n  where \n  DATE_FORMAT(CONVERT_TZ(created_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date group by user_id\n) chs on chs.user_id = po.user_id\n\n\nwhere po.pay_fee > 0 and po.status = 'SUCCEED' and DATE_FORMAT(CONVERT_TZ(po.finished_at, 'UTC', '+08:00'), '%Y%m%d') = $search_date  group by po.user_id\n\n\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "用户每日充值汇总(当日数据）", "type": "table"}], "preload": false, "refresh": "30s", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "20250616", "value": "20250616"}, "datasource": {"type": "mysql", "uid": "bdpimouwwkw74f"}, "definition": "SELECT DISTINCT(DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'), '%Y%m%d')) as a\nFROM tavern.users_pw\norder by a desc", "description": "", "label": "日期选择", "name": "search_date", "options": [], "query": "SELECT DISTINCT(DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '+08:00'), '%Y%m%d')) as a\nFROM tavern.users_pw\norder by a desc", "refresh": 1, "regex": "", "sort": 2, "type": "query"}, {"allowCustomValue": false, "current": {"text": "zh", "value": "zh"}, "name": "lang", "options": [{"selected": true, "text": "zh", "value": "zh"}, {"selected": false, "text": "en", "value": "en"}], "query": "zh,en", "type": "custom"}]}, "time": {"from": "now-9d", "to": "now"}, "timepicker": {"refresh_intervals": ["1h", "2h", "1d"]}, "timezone": "browser", "title": "运营报表-每日增长报表（分语言）", "uid": "9d0471e5-3dd1-484c-bea6-b38515fde941", "version": 2}