{
    admin :2019
    servers {
        metrics
    }
    log {
        output file /data/caddy_logs/caddy.log {
            roll_local_time
        }
    }
    order jwtauth before reverse_proxy
    order tgauth before reverse_proxy
}

https://tavern-api.fancyme.xyz {
    tls internal
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers "Conversation-Id, Message-Id, Message-Version, Human-Message-Id, Current-Language"
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type, Tg-init-data, Tg-bot-id, Current-Language"
    @skip_auth {
        path /roles/filter_list_v1
        path /recharge/list
        path /tg_code_login
        path /tg_auth_login
        path /user/login
        path /login/google
        path /auth/google
        path /auth/token
        path /config
        path /roles/filter_list_v2
    }

    @need_auth {
        not path /roles/filter_list_v1
        not path /recharge/list
        not path /tg_code_login
        not path /tg_auth_login
        not path /user/login
        not path /login/google
        not path /auth/google
        not path /auth/token
        not path /config
        not path /roles/filter_list_v2
    }


    tgauth @need_auth {
        redis_addr **********:6379
        redis_password "5A7A6HD1d7gZH0f9WqFbqtodVUT!"
        redis_db 1
        bot_token_key_prefix tg_bot_tokens
        jwt_secret "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
    }

    reverse_proxy {
        to ***********:8800 **********:8800 ***********:8800
        header_down -Access-Control-Allow-Origin
        header_down -Access-Control-Expose-Headers
        header_down -Access-Control-Allow-Credentials
        header_down -Access-Control-Allow-Headers
        header_down -X-User-Id
        lb_policy round_robin
        lb_retries 1
        lb_try_duration 5s
        handle_response {
            vars http.auth.user.id {rp.header.X-User-Id}
            copy_response
        }
    }
    log {
        output file tavern-api.log {
            roll_local_time
        }
    }
}

https://preview-tavern-api.fancyme.xyz {
    tls internal
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers "Conversation-Id, Message-Id, Message-Version, Human-Message-Id, Current-Language"
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type, Tg-init-data, Tg-bot-id, Current-Language"
    reverse_proxy {
        to **********:18800
        header_down -Access-Control-Allow-Origin
        header_down -Access-Control-Expose-Headers
        header_down -Access-Control-Allow-Credentials
        header_down -Access-Control-Allow-Headers
    }
    log {
        output file preview-tavern-api.log {
            roll_local_time
        }
    }
}

https://tavern-admin-api.fancyme.xyz {
    tls internal
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers Conversation-Id,Message-Id,Message-Version
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type,authorization"
    reverse_proxy **********:8900 {
        header_down -Access-Control-Allow-Origin
        header_down -Access-Control-Expose-Headers
        header_down -Access-Control-Allow-Credentials
        header_down -Access-Control-Allow-Headers
    }
    log {
        output file tavern-admin-api.log {
            roll_local_time
        }
    }
}

https://tavern-admin.fancyme.xyz {
    tls internal
    reverse_proxy {
        to **********:3001
    }
    log {
        output file tavern-admin.log {
            roll_local_time
        }
    }
}
https://tavern.fancyme.xyz {
    tls internal
    reverse_proxy {
        to **********:3002
    }
    log {
        output file tavern-fe.log {
            roll_local_time
        }
    }
}
https://silly-stage.fancyme.xyz {
    tls internal
    reverse_proxy {
        to **********:8880
    }
    log {
        output file silly-stage.log {
            roll_local_time
        }
    }
}

https://api.fancyou.ai {
    tls internal
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers "Conversation-Id, Message-Id, Message-Version, Human-Message-Id, Current-Language"
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type, Tg-init-data, Tg-bot-id, Current-Language"
    reverse_proxy {
        to **********:8800 **********:8800
        header_down -Access-Control-Allow-Origin
        header_down -Access-Control-Expose-Headers
        header_down -Access-Control-Allow-Credentials
        header_down -Access-Control-Allow-Headers
    }
    log {
        output file api-fancyou.log {
            roll_local_time
        }
    }
}

https://fancyou.ai {
    tls internal
    reverse_proxy {
        to **********:3003
    }
    log {
        output file fancyou-fe.log {
            roll_local_time
        }
    }
}
https://www.fancyou.ai {
    tls internal
    reverse_proxy {
        to **********:3003
    }
    log {
        output file fancyou-fe.log {
            roll_local_time
        }
    }
}

https://gh-hook.fancyme.xyz {
    tls internal
    reverse_proxy {
        to to **********3:8903 172.22.0.35:8903
        lb_policy round_robin
        lb_retries 1
        lb_try_duration 5s
    }
    log {
        output file gh-hook-api.log {
            roll_local_time
            time_format wall_milli
        }
    }
}

https://tavern-api.huanmeng.ai {
    tls internal
    @responseOptions method OPTIONS
    respond @responseOptions 'OK' 200
    header Access-Control-Allow-Origin {http.request.header.Origin}
    header Access-Control-Expose-Headers "Conversation-Id, Message-Id, Message-Version, Human-Message-Id, Current-Language"
    header Access-Control-Allow-Credentials true
    header Access-Control-Allow-Headers "Content-Type, Tg-init-data, Tg-bot-id, Current-Language"
    @skip_auth {
        path /roles/filter_list_v1
        path /recharge/list
        path /tg_code_login
        path /tg_auth_login
        path /user/login
        path /login/google
        path /auth/google
        path /auth/token
        path /config
        path /roles/filter_list_v2
    }

    @need_auth {
        not path /roles/filter_list_v1
        not path /recharge/list
        not path /tg_code_login
        not path /tg_auth_login
        not path /user/login
        not path /login/google
        not path /auth/google
        not path /auth/token
        not path /config
        not path /roles/filter_list_v2
    }

    jwtauth @need_auth {
        redis_addr **********:6379
        redis_password "5A7A6HD1d7gZH0f9WqFbqtodVUT!"
        redis_db 1
        jwt_secret "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
    }

    reverse_proxy {
        to ***********:8800 ***********:8800 **********:8800 **********:8800

        header_down -Access-Control-Allow-Origin
        header_down -Access-Control-Expose-Headers
        header_down -Access-Control-Allow-Credentials
        header_down -Access-Control-Allow-Headers
        header_down -X-User-Id
        lb_policy round_robin
        lb_retries 1
        lb_try_duration 5s
        handle_response {
            vars http.auth.user.id {rp.header.X-User-Id}
            copy_response
        }
    }

    log {
        output file /data/caddy_logs/tavern-api-huanmeng.log {
            roll_local_time
        }
    }
}