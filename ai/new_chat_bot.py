from datetime import datetime
import hashlib
import json
import os
import logging
import re
from langchain_core.pydantic_v1 import SecretStr
from dotenv import load_dotenv
from openai import OpenAI

from common.chat_bot_model import DfyRequest, PresetSwitchQuery
from common.common_constant import LlmModel
from common.models.chat_model import ChatNextInput
from utils import (
    env_util,
    request_util,
    tg_util,
    token_util,
    preset_util,
)
from langchain_core.messages.base import BaseMessage
from langchain_openai import ChatOpenAI
from utils import message_utils

load_dotenv()

log = logging.getLogger(__name__)
DEFAULT_MODEL = os.getenv("ai_model", "claude-3-haiku")
DEFAULT_BASE_URL = os.getenv("ai_base_url", "")
DEFAULT_LLM_KEY = os.getenv("litellm_key", "")


def load_preset_switch(input: ChatNextInput) -> PresetSwitchQuery:
    example_switch = bool(input.format_example) and bool(
        bool(input.format_example.messages)
    )
    scenario_switch = bool(input.scenario)
    personality_switch = bool(input.user_personality)
    first_msg_switch = bool(input.first_ai_message)
    # world_info_switch = bool(input.world_info_before) or bool(input.world_info_after)
    # world_info_switch = bool(input.constant_book_entries) or bool(input.keywords_book_entries)
    return PresetSwitchQuery(
        status_enable=input.status_block_enable,
        role_chat_type=input.role_chat_type,
        example_switch=example_switch,
        scenario_switch=scenario_switch,
        personality_switch=personality_switch,
        first_msg_switch=first_msg_switch,
        world_info_switch=bool(input.constant_book_entries),
        kw_world_info_switch=bool(input.keywords_book_entries),
        card_public_switch=bool(input.privacy),
    )


llm = ChatOpenAI(
    model=DEFAULT_MODEL,
    base_url=DEFAULT_BASE_URL,
    api_key=SecretStr(DEFAULT_LLM_KEY),  # type: ignore
    temperature=0.9,
    max_tokens=4096,
    model_kwargs={"top_p": 1},
    timeout=60,  # 首次请求超时时间
)


def format_preset_prompts(presets, chat_next_input: ChatNextInput):
    preset_switch = load_preset_switch(chat_next_input)
    preset_prompts = preset_util.format_prompts_by_status(presets, preset_switch)

    if preset_switch.first_msg_switch and preset_util.need_replace_first_msg(
        preset_prompts
    ):
        first_msg = chat_next_input.first_ai_message
        chat_next_input.history = chat_next_input.history[1:]
        preset_prompts = preset_util.replace_first_msg(preset_prompts, first_msg)
    if preset_switch.kw_world_info_switch:
        preset_prompts = preset_util.replace_kw_world_info(
            preset_prompts, chat_next_input
        )
    preset_prompts = preset_util.replace_prompts(preset_prompts, chat_next_input)
    return preset_prompts


def cat_tokens(
    preset_prompts, reduce_sum_token: int, chat_next_input: ChatNextInput
) -> ChatNextInput:
    # cat example
    if preset_util.exist_example(preset_prompts):
        example_ret, reduce_token = preset_util.cat_examples(
            chat_next_input.format_example, reduce_sum_token
        )
        chat_next_input.format_example = example_ret
        if not example_ret.messages:
            reduce_token += preset_util.token_for_example(preset_prompts)
        reduce_sum_token -= reduce_token
        # log.info("cat example,reduce_sum_token:%d", reduce_sum_token)

    # cat first_ai_message
    if reduce_sum_token > 0 and chat_next_input.first_ai_message:
        reduce_token = preset_util.token_for_first_message(preset_prompts)
        reduce_sum_token -= reduce_token
        chat_next_input.first_ai_message = ""
        # log.info("cat first_ai_message,reduce_sum_token:%d", reduce_sum_token)

    # cat scenario
    if reduce_sum_token > 0 and chat_next_input.scenario:
        reduce_sum_token -= preset_util.token_for_scenario(
            preset_prompts, chat_next_input.scenario
        )
        chat_next_input.scenario = ""
        # log.info("cat scenario,reduce_sum_token:%d", reduce_sum_token)

    # cat message
    chat_next_input, reduce_token = preset_util.cat_message_and_book_new(
        chat_next_input, reduce_sum_token
    )
    reduce_sum_token -= reduce_token

    return chat_next_input


def assemble_prompts_v3(presets, chat_next_input: ChatNextInput) -> list[BaseMessage]:
    preset_prompts = format_preset_prompts(presets, chat_next_input)
    open_ai_max_context = preset_util.load_actual_max_context(chat_next_input, presets)
    final_messages, original_token_sum = preset_util.assembling_messages_v2(
        preset_prompts, chat_next_input
    )
    if original_token_sum < open_ai_max_context:
        return final_messages
    # 超出上下文长度限制，需要进行截断
    reduce_sum_token = original_token_sum - open_ai_max_context
    chat_next_input = cat_tokens(preset_prompts, reduce_sum_token, chat_next_input)

    preset_prompts = format_preset_prompts(presets, chat_next_input)
    final_messages, ret_token_sum = preset_util.assembling_messages_v2(
        preset_prompts, chat_next_input
    )
    # logging.info(
    #     "cat message success origin_token_sum:%d,ret_token_sum:%d",
    #     original_token_sum,
    #     ret_token_sum,
    # )
    return final_messages


def record_log(input: ChatNextInput, message: list[str], input_token_sum: int):
    def print_log():
        log.info(
            "BeginCallLlm,model:%s,sessionId:%s,user_id:%s,role_id:%s,role_name:%s,token_sum:%s,product_name:%s,balance:%s,freeBenefit:%s,chat_channel:%s",
            input.preset_model,
            input.conversation_id,
            input.user_id,
            input.role_id,
            input.role_name,
            input_token_sum,
            input.product_display_name,
            input.balance,
            input.chat_free_benefit,
            input.chat_channel,
        )

    try:
        print_log()
    except Exception as e:
        log.error(f"record log error:{e}")


# def role_next(input: ChatNextInput) -> ChatResponse:
#     input_messages: list[BaseMessage] = []
#     try:
#         input_messages = assemble_prompts_v3(input.preset_dict, input)
#         input_messages = message_utils.process_merged(input_messages)

#     except Exception as e:
#         logging.error("assemble_prompts_v3 error", e)
#         raise e
#     stop = preset_util.find_stop_sequence(
#         input.preset_dict, input.role_name, input.request_user_name
#     )
#     top_k = preset_util.find_top_k(input.preset_dict, 0)
#     token_message = [str(x.content) for x in input_messages]
#     input_token = token_util.num_token_from_list(token_message)
#     record_log(input, token_message, input_token)

#     start_request_time = datetime.now().timestamp()
#     response = llm.astream(
#         model=input.request_model,
#         input=input_messages,
#         max_tokens=input.preset_dict["openai_max_tokens"],
#         # max_tokens=400,
#         temperature=float(input.preset_dict["temperature"]),
#         top_p=input.preset_dict["top_p"],
#         frequency_penalty=input.preset_dict["frequency_penalty"],
#         presence_penalty=input.preset_dict["presence_penalty"],
#         stop=stop,
#         extra_body={
#             "top_k": top_k,
#             "metadata": {
#                 "trace_user_id": input.user_id,
#                 "generation_name": "llm-call",
#                 "session_id": input.conversation_id,
#                 "trace_metadata": {"role_id": input.role_id},
#             },
#         },
#     )

#     return ChatResponse(
#         response=response,
#         token_sum=input_token,
#         start_request_time=int(start_request_time),
#     )


moderation_client = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)


def moderation_input_check(input: str):
    try:
        response = moderation_client.moderations.create(
            model="omni-moderation-latest",
            input=input,
        )
        results = response.results
        if not results:
            return None, None
        mid = results[0]
        categories = mid.categories.model_dump()
        scores_map = mid.category_scores.model_dump()
        return categories, scores_map
    except Exception as e:
        tg_util.send_message(
            {
                "title": "Moderation Input Check Error",
                "content": f"Error:{e}\nInput:{input}",
            }
        )
        log.error(f"moderation_input_check error:{e}")
        return None, None


# async def run_task(
#     llm_model: str, system_message: str, user_message: str, max_tokens: int = 200
# ) -> str:
#     input_messages: list[BaseMessage] = []
#     input_messages.append(SystemMessage(content=system_message))
#     input_messages.append(HumanMessage(content=user_message))
#     response = await llm.ainvoke(
#         model=llm_model,
#         input=input_messages,
#         max_tokens=max_tokens,
#         extra_body={
#             "top_k": 0,
#             "metadata": {
#                 "generation_name": "llm-task",
#             },
#         },
#     )
#     log.info(f"run_task system=====:\n{system_message}\n")
#     log.info(f"run_task user=====:\n{user_message}\n")
#     log.info(f"run_task response=====:\n{response.content}\n=====")
#     return str(response.content)


async def dfy_run_task(query: str, api_key: str, inputs: dict = {}) -> str:

    req = DfyRequest(query=query, inputs=inputs)
    # ret = requests.post(
    #     "http://dify.198432.xyz/v1/chat-messages",
    #     json=req.model_dump(),
    #     headers={"Authorization": f"Bearer {api_key}"},
    # )
    ret_json = await request_util.post_json(
        url="http://dify.198432.xyz/v1/chat-messages",
        post_data=req.model_dump(),
        headers={"Authorization": f"Bearer {api_key}"},
        timeout=60,
    )
    if not ret_json:
        return ""
    log.info(f"dfy_run_task,request:{req.model_dump()},response:{ret_json}")
    answer = ret_json.get("answer", "")
    return answer
